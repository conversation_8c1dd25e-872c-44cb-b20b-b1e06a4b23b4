{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\components\\\\SafeErrorBoundary.tsx\",\n  _s = $RefreshSig$();\n/**\n * 安全的错误边界组件 - 带有自动回退机制\n */\n\nimport React, { Component } from 'react';\nimport { Alert, Button, Space } from 'antd';\nimport { ReloadOutlined, BugOutlined } from '@ant-design/icons';\nimport ErrorBoundary from './ErrorBoundary';\nimport { useFeatureFlag } from '../utils/featureFlags';\nimport { errorMonitoring } from '../utils/errorMonitoring';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// 简单的回退错误边界组件\nclass FallbackErrorBoundary extends Component {\n  constructor(props) {\n    super(props);\n    this.handleRetry = () => {\n      this.setState({\n        hasError: false,\n        error: null,\n        errorInfo: null\n      });\n    };\n    this.handleReload = () => {\n      window.location.reload();\n    };\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    this.setState({\n      error,\n      errorInfo\n    });\n\n    // 报告错误\n    errorMonitoring.reportError({\n      component: 'FallbackErrorBoundary',\n      feature: 'useErrorBoundary',\n      error: {\n        message: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack\n      }\n    });\n\n    // 调用外部错误处理器\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n    console.error('FallbackErrorBoundary caught error:', error);\n  }\n  render() {\n    if (this.state.hasError) {\n      const {\n        fallback,\n        level = 'component',\n        showDetails = false\n      } = this.props;\n      const {\n        error\n      } = this.state;\n\n      // 如果提供了自定义fallback，使用它\n      if (fallback) {\n        return fallback;\n      }\n\n      // 页面级错误\n      if (level === 'page') {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            minHeight: '100vh',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '24px',\n            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxWidth: '500px',\n              width: '100%',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '16px'\n              },\n              children: \"\\uD83D\\uDEAB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              style: {\n                marginBottom: '16px'\n              },\n              children: \"\\u9875\\u9762\\u51FA\\u73B0\\u9519\\u8BEF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginBottom: '24px',\n                color: '#666',\n                fontSize: '16px'\n              },\n              children: \"\\u62B1\\u6B49\\uFF0C\\u9875\\u9762\\u9047\\u5230\\u4E86\\u4E00\\u4E9B\\u95EE\\u9898\\u3002\\u8BF7\\u5C1D\\u8BD5\\u5237\\u65B0\\u9875\\u9762\\u6216\\u8054\\u7CFB\\u6280\\u672F\\u652F\\u6301\\u3002\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 25\n                }, this),\n                onClick: this.handleReload,\n                children: \"\\u5237\\u65B0\\u9875\\u9762\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                icon: /*#__PURE__*/_jsxDEV(BugOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 25\n                }, this),\n                onClick: this.handleRetry,\n                children: \"\\u91CD\\u8BD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), showDetails && error && /*#__PURE__*/_jsxDEV(Alert, {\n              message: \"\\u9519\\u8BEF\\u8BE6\\u60C5\",\n              description: error.message,\n              type: \"error\",\n              showIcon: true,\n              style: {\n                marginTop: '24px',\n                textAlign: 'left'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this);\n      }\n\n      // 组件级错误\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          margin: '16px 0'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u7EC4\\u4EF6\\u52A0\\u8F7D\\u5931\\u8D25\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u8BE5\\u7EC4\\u4EF6\\u9047\\u5230\\u4E86\\u9519\\u8BEF\\uFF0C\\u8BF7\\u5C1D\\u8BD5\\u91CD\\u65B0\\u52A0\\u8F7D\\u3002\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this), showDetails && error && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                fontSize: '12px',\n                color: '#666',\n                marginTop: '8px',\n                fontFamily: 'monospace'\n              },\n              children: [\"\\u9519\\u8BEF\\u4FE1\\u606F: \", error.message]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this),\n          type: \"error\",\n          showIcon: true,\n          action: /*#__PURE__*/_jsxDEV(Button, {\n            size: \"small\",\n            onClick: this.handleRetry,\n            children: \"\\u91CD\\u8BD5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\n\n// 错误边界组件适配器\nconst ErrorBoundaryAdapter = props => {\n  try {\n    return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 12\n    }, this);\n  } catch (error) {\n    console.warn('ErrorBoundary failed, falling back to simple error boundary:', error);\n    return /*#__PURE__*/_jsxDEV(FallbackErrorBoundary, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 12\n    }, this);\n  }\n};\n\n// 使用安全包装器包装错误边界（注意：这里不能使用withSafeWrapper，因为会造成循环依赖）\n// 所以我们直接使用功能开关\n_c = ErrorBoundaryAdapter;\nconst SafeErrorBoundary = props => {\n  // 注意：这里不能使用useFeatureFlag hook，因为这是在类组件中\n  // 我们需要直接检查功能标志\n  const isErrorBoundaryEnabled = typeof window !== 'undefined' && localStorage.getItem('ux-feature-flags') && JSON.parse(localStorage.getItem('ux-feature-flags') || '{}').useErrorBoundary;\n  if (!isErrorBoundaryEnabled) {\n    return /*#__PURE__*/_jsxDEV(FallbackErrorBoundary, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(ErrorBoundaryAdapter, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 213,\n    columnNumber: 10\n  }, this);\n};\n\n// 函数式组件版本的安全错误边界\n_c2 = SafeErrorBoundary;\nexport const SafeErrorBoundaryWrapper = props => {\n  _s();\n  const isErrorBoundaryEnabled = useFeatureFlag('useErrorBoundary');\n  if (!isErrorBoundaryEnabled) {\n    return /*#__PURE__*/_jsxDEV(FallbackErrorBoundary, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(ErrorBoundaryAdapter, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 224,\n    columnNumber: 10\n  }, this);\n};\n\n// 添加CSS样式\n_s(SafeErrorBoundaryWrapper, \"iioT5HthjF1zAr2ncBQWK0AZOCI=\", false, function () {\n  return [useFeatureFlag];\n});\n_c3 = SafeErrorBoundaryWrapper;\nconst errorBoundaryStyles = `\n/* 错误边界样式 */\n.error-boundary-fallback {\n  padding: 16px;\n  border: 1px solid #ffccc7;\n  border-radius: 6px;\n  background: #fff2f0;\n  margin: 16px 0;\n}\n\n.error-boundary-page {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n}\n\n.error-boundary-content {\n  max-width: 500px;\n  text-align: center;\n  padding: 24px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n/* 响应式调整 */\n@media (max-width: 768px) {\n  .error-boundary-content {\n    margin: 16px;\n    padding: 16px;\n  }\n}\n`;\n\n// 动态注入样式\nif (typeof document !== 'undefined') {\n  const styleId = 'safe-error-boundary-styles';\n  if (!document.getElementById(styleId)) {\n    const style = document.createElement('style');\n    style.id = styleId;\n    style.textContent = errorBoundaryStyles;\n    document.head.appendChild(style);\n  }\n}\nexport default SafeErrorBoundary;\n\n// 导出类型\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ErrorBoundaryAdapter\");\n$RefreshReg$(_c2, \"SafeErrorBoundary\");\n$RefreshReg$(_c3, \"SafeErrorBoundaryWrapper\");", "map": {"version": 3, "names": ["React", "Component", "<PERSON><PERSON>", "<PERSON><PERSON>", "Space", "ReloadOutlined", "BugOutlined", "Error<PERSON>ou<PERSON><PERSON>", "useFeatureFlag", "errorMonitoring", "jsxDEV", "_jsxDEV", "FallbackErrorBoundary", "constructor", "props", "handleRetry", "setState", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "handleReload", "window", "location", "reload", "state", "getDerivedStateFromError", "componentDidCatch", "reportError", "component", "feature", "message", "stack", "componentStack", "onError", "console", "render", "fallback", "level", "showDetails", "style", "minHeight", "display", "alignItems", "justifyContent", "padding", "background", "children", "max<PERSON><PERSON><PERSON>", "width", "textAlign", "fontSize", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "type", "icon", "onClick", "description", "showIcon", "marginTop", "margin", "fontFamily", "action", "size", "ErrorBoundaryAdapter", "warn", "_c", "SafeErrorBoundary", "isErrorBoundaryEnabled", "localStorage", "getItem", "JSON", "parse", "useErrorBoundary", "_c2", "SafeErrorBoundaryWrapper", "_s", "_c3", "errorBoundaryStyles", "document", "styleId", "getElementById", "createElement", "id", "textContent", "head", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/components/SafeErrorBoundary.tsx"], "sourcesContent": ["/**\n * 安全的错误边界组件 - 带有自动回退机制\n */\n\nimport React, { Component, ReactNode, ErrorInfo } from 'react';\nimport { <PERSON>ert, Button, Space } from 'antd';\nimport { ReloadOutlined, BugOutlined } from '@ant-design/icons';\nimport { withSafeWrapper } from './SafeComponentWrapper';\nimport ErrorBoundary from './ErrorBoundary';\nimport { useFeatureFlag } from '../utils/featureFlags';\nimport { errorMonitoring } from '../utils/errorMonitoring';\n\ninterface SafeErrorBoundaryProps {\n  children: ReactNode;\n  fallback?: ReactNode;\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\n  showDetails?: boolean;\n  level?: 'page' | 'component';\n}\n\ninterface FallbackErrorBoundaryState {\n  hasError: boolean;\n  error: Error | null;\n  errorInfo: ErrorInfo | null;\n}\n\n// 简单的回退错误边界组件\nclass FallbackErrorBoundary extends Component<SafeErrorBoundaryProps, FallbackErrorBoundaryState> {\n  constructor(props: SafeErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null\n    };\n  }\n\n  static getDerivedStateFromError(error: Error): Partial<FallbackErrorBoundaryState> {\n    return {\n      hasError: true,\n      error\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    this.setState({\n      error,\n      errorInfo\n    });\n\n    // 报告错误\n    errorMonitoring.reportError({\n      component: 'FallbackErrorBoundary',\n      feature: 'useErrorBoundary',\n      error: {\n        message: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n      },\n    });\n\n    // 调用外部错误处理器\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n\n    console.error('FallbackErrorBoundary caught error:', error);\n  }\n\n  handleRetry = () => {\n    this.setState({\n      hasError: false,\n      error: null,\n      errorInfo: null\n    });\n  };\n\n  handleReload = () => {\n    window.location.reload();\n  };\n\n  render() {\n    if (this.state.hasError) {\n      const { fallback, level = 'component', showDetails = false } = this.props;\n      const { error } = this.state;\n\n      // 如果提供了自定义fallback，使用它\n      if (fallback) {\n        return fallback;\n      }\n\n      // 页面级错误\n      if (level === 'page') {\n        return (\n          <div style={{\n            minHeight: '100vh',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '24px',\n            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'\n          }}>\n            <div style={{\n              maxWidth: '500px',\n              width: '100%',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                fontSize: '48px',\n                marginBottom: '16px'\n              }}>\n                🚫\n              </div>\n              <h1 style={{ marginBottom: '16px' }}>页面出现错误</h1>\n              <p style={{ \n                marginBottom: '24px',\n                color: '#666',\n                fontSize: '16px'\n              }}>\n                抱歉，页面遇到了一些问题。请尝试刷新页面或联系技术支持。\n              </p>\n              <Space>\n                <Button \n                  type=\"primary\" \n                  icon={<ReloadOutlined />} \n                  onClick={this.handleReload}\n                >\n                  刷新页面\n                </Button>\n                <Button \n                  icon={<BugOutlined />} \n                  onClick={this.handleRetry}\n                >\n                  重试\n                </Button>\n              </Space>\n              {showDetails && error && (\n                <Alert\n                  message=\"错误详情\"\n                  description={error.message}\n                  type=\"error\"\n                  showIcon\n                  style={{ \n                    marginTop: '24px',\n                    textAlign: 'left'\n                  }}\n                />\n              )}\n            </div>\n          </div>\n        );\n      }\n\n      // 组件级错误\n      return (\n        <div style={{ margin: '16px 0' }}>\n          <Alert\n            message=\"组件加载失败\"\n            description={\n              <div>\n                <p>该组件遇到了错误，请尝试重新加载。</p>\n                {showDetails && error && (\n                  <p style={{ \n                    fontSize: '12px', \n                    color: '#666',\n                    marginTop: '8px',\n                    fontFamily: 'monospace'\n                  }}>\n                    错误信息: {error.message}\n                  </p>\n                )}\n              </div>\n            }\n            type=\"error\"\n            showIcon\n            action={\n              <Button size=\"small\" onClick={this.handleRetry}>\n                重试\n              </Button>\n            }\n          />\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n// 错误边界组件适配器\nconst ErrorBoundaryAdapter: React.FC<SafeErrorBoundaryProps> = (props) => {\n  try {\n    return <ErrorBoundary {...props} />;\n  } catch (error) {\n    console.warn('ErrorBoundary failed, falling back to simple error boundary:', error);\n    return <FallbackErrorBoundary {...props} />;\n  }\n};\n\n// 使用安全包装器包装错误边界（注意：这里不能使用withSafeWrapper，因为会造成循环依赖）\n// 所以我们直接使用功能开关\nconst SafeErrorBoundary: React.FC<SafeErrorBoundaryProps> = (props) => {\n  // 注意：这里不能使用useFeatureFlag hook，因为这是在类组件中\n  // 我们需要直接检查功能标志\n  const isErrorBoundaryEnabled = typeof window !== 'undefined' && \n    localStorage.getItem('ux-feature-flags') &&\n    JSON.parse(localStorage.getItem('ux-feature-flags') || '{}').useErrorBoundary;\n\n  if (!isErrorBoundaryEnabled) {\n    return <FallbackErrorBoundary {...props} />;\n  }\n\n  return <ErrorBoundaryAdapter {...props} />;\n};\n\n// 函数式组件版本的安全错误边界\nexport const SafeErrorBoundaryWrapper: React.FC<SafeErrorBoundaryProps> = (props) => {\n  const isErrorBoundaryEnabled = useFeatureFlag('useErrorBoundary');\n  \n  if (!isErrorBoundaryEnabled) {\n    return <FallbackErrorBoundary {...props} />;\n  }\n\n  return <ErrorBoundaryAdapter {...props} />;\n};\n\n// 添加CSS样式\nconst errorBoundaryStyles = `\n/* 错误边界样式 */\n.error-boundary-fallback {\n  padding: 16px;\n  border: 1px solid #ffccc7;\n  border-radius: 6px;\n  background: #fff2f0;\n  margin: 16px 0;\n}\n\n.error-boundary-page {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n}\n\n.error-boundary-content {\n  max-width: 500px;\n  text-align: center;\n  padding: 24px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n/* 响应式调整 */\n@media (max-width: 768px) {\n  .error-boundary-content {\n    margin: 16px;\n    padding: 16px;\n  }\n}\n`;\n\n// 动态注入样式\nif (typeof document !== 'undefined') {\n  const styleId = 'safe-error-boundary-styles';\n  if (!document.getElementById(styleId)) {\n    const style = document.createElement('style');\n    style.id = styleId;\n    style.textContent = errorBoundaryStyles;\n    document.head.appendChild(style);\n  }\n}\n\nexport default SafeErrorBoundary;\n\n// 导出类型\nexport type { SafeErrorBoundaryProps };\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,SAAS,QAA8B,OAAO;AAC9D,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AAC3C,SAASC,cAAc,EAAEC,WAAW,QAAQ,mBAAmB;AAE/D,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAgB3D;AACA,MAAMC,qBAAqB,SAASX,SAAS,CAAqD;EAChGY,WAAWA,CAACC,KAA6B,EAAE;IACzC,KAAK,CAACA,KAAK,CAAC;IAAC,KAwCfC,WAAW,GAAG,MAAM;MAClB,IAAI,CAACC,QAAQ,CAAC;QACZC,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC;IAAA,KAEDC,YAAY,GAAG,MAAM;MACnBC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAjDC,IAAI,CAACC,KAAK,GAAG;MACXP,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE;IACb,CAAC;EACH;EAEA,OAAOM,wBAAwBA,CAACP,KAAY,EAAuC;IACjF,OAAO;MACLD,QAAQ,EAAE,IAAI;MACdC;IACF,CAAC;EACH;EAEAQ,iBAAiBA,CAACR,KAAY,EAAEC,SAAoB,EAAE;IACpD,IAAI,CAACH,QAAQ,CAAC;MACZE,KAAK;MACLC;IACF,CAAC,CAAC;;IAEF;IACAV,eAAe,CAACkB,WAAW,CAAC;MAC1BC,SAAS,EAAE,uBAAuB;MAClCC,OAAO,EAAE,kBAAkB;MAC3BX,KAAK,EAAE;QACLY,OAAO,EAAEZ,KAAK,CAACY,OAAO;QACtBC,KAAK,EAAEb,KAAK,CAACa,KAAK;QAClBC,cAAc,EAAEb,SAAS,CAACa;MAC5B;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,IAAI,CAAClB,KAAK,CAACmB,OAAO,EAAE;MACtB,IAAI,CAACnB,KAAK,CAACmB,OAAO,CAACf,KAAK,EAAEC,SAAS,CAAC;IACtC;IAEAe,OAAO,CAAChB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;EAC7D;EAcAiB,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACX,KAAK,CAACP,QAAQ,EAAE;MACvB,MAAM;QAAEmB,QAAQ;QAAEC,KAAK,GAAG,WAAW;QAAEC,WAAW,GAAG;MAAM,CAAC,GAAG,IAAI,CAACxB,KAAK;MACzE,MAAM;QAAEI;MAAM,CAAC,GAAG,IAAI,CAACM,KAAK;;MAE5B;MACA,IAAIY,QAAQ,EAAE;QACZ,OAAOA,QAAQ;MACjB;;MAEA;MACA,IAAIC,KAAK,KAAK,MAAM,EAAE;QACpB,oBACE1B,OAAA;UAAK4B,KAAK,EAAE;YACVC,SAAS,EAAE,OAAO;YAClBC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE;UACd,CAAE;UAAAC,QAAA,eACAnC,OAAA;YAAK4B,KAAK,EAAE;cACVQ,QAAQ,EAAE,OAAO;cACjBC,KAAK,EAAE,MAAM;cACbC,SAAS,EAAE;YACb,CAAE;YAAAH,QAAA,gBACAnC,OAAA;cAAK4B,KAAK,EAAE;gBACVW,QAAQ,EAAE,MAAM;gBAChBC,YAAY,EAAE;cAChB,CAAE;cAAAL,QAAA,EAAC;YAEH;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA;cAAI4B,KAAK,EAAE;gBAAEY,YAAY,EAAE;cAAO,CAAE;cAAAL,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChD5C,OAAA;cAAG4B,KAAK,EAAE;gBACRY,YAAY,EAAE,MAAM;gBACpBK,KAAK,EAAE,MAAM;gBACbN,QAAQ,EAAE;cACZ,CAAE;cAAAJ,QAAA,EAAC;YAEH;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ5C,OAAA,CAACP,KAAK;cAAA0C,QAAA,gBACJnC,OAAA,CAACR,MAAM;gBACLsD,IAAI,EAAC,SAAS;gBACdC,IAAI,eAAE/C,OAAA,CAACN,cAAc;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBI,OAAO,EAAE,IAAI,CAACvC,YAAa;gBAAA0B,QAAA,EAC5B;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5C,OAAA,CAACR,MAAM;gBACLuD,IAAI,eAAE/C,OAAA,CAACL,WAAW;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtBI,OAAO,EAAE,IAAI,CAAC5C,WAAY;gBAAA+B,QAAA,EAC3B;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACPjB,WAAW,IAAIpB,KAAK,iBACnBP,OAAA,CAACT,KAAK;cACJ4B,OAAO,EAAC,0BAAM;cACd8B,WAAW,EAAE1C,KAAK,CAACY,OAAQ;cAC3B2B,IAAI,EAAC,OAAO;cACZI,QAAQ;cACRtB,KAAK,EAAE;gBACLuB,SAAS,EAAE,MAAM;gBACjBb,SAAS,EAAE;cACb;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV;;MAEA;MACA,oBACE5C,OAAA;QAAK4B,KAAK,EAAE;UAAEwB,MAAM,EAAE;QAAS,CAAE;QAAAjB,QAAA,eAC/BnC,OAAA,CAACT,KAAK;UACJ4B,OAAO,EAAC,sCAAQ;UAChB8B,WAAW,eACTjD,OAAA;YAAAmC,QAAA,gBACEnC,OAAA;cAAAmC,QAAA,EAAG;YAAiB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACvBjB,WAAW,IAAIpB,KAAK,iBACnBP,OAAA;cAAG4B,KAAK,EAAE;gBACRW,QAAQ,EAAE,MAAM;gBAChBM,KAAK,EAAE,MAAM;gBACbM,SAAS,EAAE,KAAK;gBAChBE,UAAU,EAAE;cACd,CAAE;cAAAlB,QAAA,GAAC,4BACK,EAAC5B,KAAK,CAACY,OAAO;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;UACDE,IAAI,EAAC,OAAO;UACZI,QAAQ;UACRI,MAAM,eACJtD,OAAA,CAACR,MAAM;YAAC+D,IAAI,EAAC,OAAO;YAACP,OAAO,EAAE,IAAI,CAAC5C,WAAY;YAAA+B,QAAA,EAAC;UAEhD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAEV;IAEA,OAAO,IAAI,CAACzC,KAAK,CAACgC,QAAQ;EAC5B;AACF;;AAEA;AACA,MAAMqB,oBAAsD,GAAIrD,KAAK,IAAK;EACxE,IAAI;IACF,oBAAOH,OAAA,CAACJ,aAAa;MAAA,GAAKO;IAAK;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EACrC,CAAC,CAAC,OAAOrC,KAAK,EAAE;IACdgB,OAAO,CAACkC,IAAI,CAAC,8DAA8D,EAAElD,KAAK,CAAC;IACnF,oBAAOP,OAAA,CAACC,qBAAqB;MAAA,GAAKE;IAAK;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAC7C;AACF,CAAC;;AAED;AACA;AAAAc,EAAA,GAVMF,oBAAsD;AAW5D,MAAMG,iBAAmD,GAAIxD,KAAK,IAAK;EACrE;EACA;EACA,MAAMyD,sBAAsB,GAAG,OAAOlD,MAAM,KAAK,WAAW,IAC1DmD,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,IACxCC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,IAAI,IAAI,CAAC,CAACG,gBAAgB;EAE/E,IAAI,CAACL,sBAAsB,EAAE;IAC3B,oBAAO5D,OAAA,CAACC,qBAAqB;MAAA,GAAKE;IAAK;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAC7C;EAEA,oBAAO5C,OAAA,CAACwD,oBAAoB;IAAA,GAAKrD;EAAK;IAAAsC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;AAC5C,CAAC;;AAED;AAAAsB,GAAA,GAdMP,iBAAmD;AAezD,OAAO,MAAMQ,wBAA0D,GAAIhE,KAAK,IAAK;EAAAiE,EAAA;EACnF,MAAMR,sBAAsB,GAAG/D,cAAc,CAAC,kBAAkB,CAAC;EAEjE,IAAI,CAAC+D,sBAAsB,EAAE;IAC3B,oBAAO5D,OAAA,CAACC,qBAAqB;MAAA,GAAKE;IAAK;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAC7C;EAEA,oBAAO5C,OAAA,CAACwD,oBAAoB;IAAA,GAAKrD;EAAK;IAAAsC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;AAC5C,CAAC;;AAED;AAAAwB,EAAA,CAVaD,wBAA0D;EAAA,QACtCtE,cAAc;AAAA;AAAAwE,GAAA,GADlCF,wBAA0D;AAWvE,MAAMG,mBAAmB,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMC,OAAO,GAAG,4BAA4B;EAC5C,IAAI,CAACD,QAAQ,CAACE,cAAc,CAACD,OAAO,CAAC,EAAE;IACrC,MAAM5C,KAAK,GAAG2C,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;IAC7C9C,KAAK,CAAC+C,EAAE,GAAGH,OAAO;IAClB5C,KAAK,CAACgD,WAAW,GAAGN,mBAAmB;IACvCC,QAAQ,CAACM,IAAI,CAACC,WAAW,CAAClD,KAAK,CAAC;EAClC;AACF;AAEA,eAAe+B,iBAAiB;;AAEhC;AAAA,IAAAD,EAAA,EAAAQ,GAAA,EAAAG,GAAA;AAAAU,YAAA,CAAArB,EAAA;AAAAqB,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAV,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}