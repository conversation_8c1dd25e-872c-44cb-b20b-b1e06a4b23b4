{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\components\\\\FeatureFlagPanel.tsx\",\n  _s = $RefreshSig$();\n/**\n * 功能开关管理面板 - 开发和测试用\n */\n\nimport React, { useState } from 'react';\nimport { Drawer, Switch, Button, Space, Alert, Card, Statistic, Row, Col, Typography, Tooltip, Badge, Popconfirm } from 'antd';\nimport { SettingOutlined, ReloadOutlined, ExclamationCircleOutlined, CheckCircleOutlined, WarningOutlined } from '@ant-design/icons';\nimport { featureFlags, useFeatureFlags } from '../utils/featureFlags';\nimport { errorMonitoring } from '../utils/errorMonitoring';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst FEATURE_DESCRIPTIONS = {\n  useDesignSystem: '启用统一设计系统，包含颜色、字体、间距等规范',\n  useEnhancedButton: '使用增强按钮组件，支持波纹效果和多种样式',\n  useEnhancedInput: '使用增强输入框组件，支持验证和自适应',\n  useResponsiveLayout: '启用响应式布局组件，优化移动端体验',\n  useErrorBoundary: '启用错误边界组件，提供错误恢复机制',\n  useLoadingStates: '使用智能加载状态组件，提升加载体验',\n  useAccessibilityProvider: '启用无障碍访问功能，支持键盘导航等',\n  useOptimizedHeader: '使用优化的头部组件，包含渐变和动效',\n  useOptimizedSidebar: '使用优化的侧边栏组件，支持折叠和动画',\n  useSmartLoading: '启用智能加载，根据加载时间自动切换显示方式',\n  useLazyLoading: '启用懒加载功能，提升页面性能',\n  enableDebugMode: '启用调试模式，显示详细的调试信息',\n  enableErrorReporting: '启用错误报告，自动收集和上报错误',\n  enablePerformanceMonitoring: '启用性能监控，收集性能指标'\n};\nconst FEATURE_CATEGORIES = {\n  '核心组件': ['useDesignSystem', 'useEnhancedButton', 'useEnhancedInput', 'useResponsiveLayout'],\n  '错误处理': ['useErrorBoundary', 'enableErrorReporting'],\n  '性能优化': ['useLoadingStates', 'useSmartLoading', 'useLazyLoading', 'enablePerformanceMonitoring'],\n  '无障碍访问': ['useAccessibilityProvider'],\n  '界面优化': ['useOptimizedHeader', 'useOptimizedSidebar'],\n  '开发工具': ['enableDebugMode']\n};\nconst FeatureFlagPanel = ({\n  visible,\n  onClose\n}) => {\n  _s();\n  const flags = useFeatureFlags();\n  const [confirmReset, setConfirmReset] = useState(false);\n  const componentHealth = errorMonitoring.getAllComponentHealth();\n  const errorStats = errorMonitoring.getErrorStats();\n  const handleFlagChange = (flag, value) => {\n    featureFlags.setFlag(flag, value);\n  };\n  const handleResetAll = () => {\n    featureFlags.resetFlags();\n    errorMonitoring.resetAllComponentHealth();\n    setConfirmReset(false);\n  };\n  const handleEmergencyRollback = () => {\n    featureFlags.disableAllUXFeatures();\n    errorMonitoring.resetAllComponentHealth();\n  };\n  const handleEnableAll = () => {\n    featureFlags.enableAllUXFeatures();\n  };\n  const getFeatureStatus = feature => {\n    const health = componentHealth.find(h => h.feature === feature);\n    if (health !== null && health !== void 0 && health.autoDisabled) {\n      return {\n        status: 'error',\n        text: '自动禁用'\n      };\n    }\n    if (health && !health.isHealthy) {\n      return {\n        status: 'warning',\n        text: '不稳定'\n      };\n    }\n    if (flags[feature]) {\n      return {\n        status: 'success',\n        text: '已启用'\n      };\n    }\n    return {\n      status: 'default',\n      text: '已禁用'\n    };\n  };\n  const renderFeatureSwitch = feature => {\n    const status = getFeatureStatus(feature);\n    const health = componentHealth.find(h => h.feature === feature);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: feature\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Badge, {\n              status: status.status,\n              text: status.text,\n              style: {\n                fontSize: '12px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), (health === null || health === void 0 ? void 0 : health.errorCount) > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: `错误次数: ${health.errorCount}`,\n              children: /*#__PURE__*/_jsxDEV(WarningOutlined, {\n                style: {\n                  color: '#faad14'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            style: {\n              fontSize: '12px',\n              display: 'block',\n              marginTop: '2px'\n            },\n            children: FEATURE_DESCRIPTIONS[feature]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Switch, {\n          checked: flags[feature],\n          onChange: checked => handleFlagChange(feature, checked),\n          disabled: health === null || health === void 0 ? void 0 : health.autoDisabled,\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), (health === null || health === void 0 ? void 0 : health.autoDisabled) && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '4px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          type: \"link\",\n          onClick: () => errorMonitoring.recoverFeature(feature),\n          style: {\n            padding: 0,\n            height: 'auto',\n            fontSize: '12px'\n          },\n          children: \"\\u624B\\u52A8\\u6062\\u590D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 11\n      }, this)]\n    }, feature, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Drawer, {\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\u529F\\u80FD\\u5F00\\u5173\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(Badge, {\n        count: \"DEV\",\n        style: {\n          backgroundColor: '#52c41a'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 9\n    }, this),\n    placement: \"right\",\n    width: 480,\n    onClose: onClose,\n    open: visible,\n    extra: /*#__PURE__*/_jsxDEV(Space, {\n      children: /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7D27\\u6025\\u56DE\\u9000\\u6240\\u6709\\u529F\\u80FD\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          danger: true,\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 21\n          }, this),\n          onClick: handleEmergencyRollback,\n          children: \"\\u7D27\\u6025\\u56DE\\u9000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 9\n    }, this),\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginBottom: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u542F\\u7528\",\n            value: Object.values(flags).filter(Boolean).length,\n            suffix: `/ ${Object.keys(flags).length}`,\n            valueStyle: {\n              color: '#3f8600'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u9519\\u8BEF\",\n            value: errorStats.totalErrors,\n            valueStyle: {\n              color: errorStats.totalErrors > 0 ? '#cf1322' : '#3f8600'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FD1\\u671F\\u9519\\u8BEF\",\n            value: errorStats.recentErrors,\n            valueStyle: {\n              color: errorStats.recentErrors > 0 ? '#cf1322' : '#3f8600'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      title: \"\\u5FEB\\u901F\\u64CD\\u4F5C\",\n      style: {\n        marginBottom: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        wrap: true,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"small\",\n          onClick: handleEnableAll,\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 19\n          }, this),\n          children: \"\\u542F\\u7528\\u6240\\u6709UX\\u529F\\u80FD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          onClick: () => featureFlags.disableAllUXFeatures(),\n          icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 19\n          }, this),\n          children: \"\\u7981\\u7528\\u6240\\u6709UX\\u529F\\u80FD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n          title: \"\\u786E\\u5B9A\\u8981\\u91CD\\u7F6E\\u6240\\u6709\\u8BBE\\u7F6E\\u5417\\uFF1F\",\n          onConfirm: handleResetAll,\n          okText: \"\\u786E\\u5B9A\",\n          cancelText: \"\\u53D6\\u6D88\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 21\n            }, this),\n            children: \"\\u91CD\\u7F6E\\u6240\\u6709\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), Object.entries(FEATURE_CATEGORIES).map(([category, features]) => /*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      title: category,\n      style: {\n        marginBottom: '16px'\n      },\n      children: features.map(feature => renderFeatureSwitch(feature))\n    }, category, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 9\n    }, this)), componentHealth.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      title: \"\\u7EC4\\u4EF6\\u5065\\u5EB7\\u72B6\\u6001\",\n      style: {\n        marginBottom: '16px'\n      },\n      children: componentHealth.map(health => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '8px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            children: health.feature\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Badge, {\n              status: health.isHealthy ? 'success' : 'error',\n              text: health.isHealthy ? '健康' : '异常'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 19\n            }, this), health.errorCount > 0 && /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              style: {\n                fontSize: '12px'\n              },\n              children: [\"\\u9519\\u8BEF: \", health.errorCount]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 15\n        }, this)\n      }, health.feature, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u4F7F\\u7528\\u8BF4\\u660E\",\n      description: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Paragraph, {\n          style: {\n            fontSize: '12px',\n            margin: 0\n          },\n          children: [\"\\u2022 \\u529F\\u80FD\\u5F00\\u5173\\u5141\\u8BB8\\u5B89\\u5168\\u5730\\u542F\\u7528/\\u7981\\u7528\\u65B0\\u7684UX\\u4F18\\u5316\\u529F\\u80FD\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 39\n          }, this), \"\\u2022 \\u7CFB\\u7EDF\\u4F1A\\u81EA\\u52A8\\u76D1\\u63A7\\u7EC4\\u4EF6\\u9519\\u8BEF\\u5E76\\u5728\\u5FC5\\u8981\\u65F6\\u7981\\u7528\\u529F\\u80FD\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 37\n          }, this), \"\\u2022 \\u7D27\\u6025\\u56DE\\u9000\\u6309\\u94AE\\u53EF\\u4EE5\\u7ACB\\u5373\\u7981\\u7528\\u6240\\u6709\\u65B0\\u529F\\u80FD\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 34\n          }, this), \"\\u2022 \\u5F00\\u53D1\\u73AF\\u5883\\u4E0B\\u53EF\\u901A\\u8FC7 window.featureFlags \\u8BBF\\u95EEAPI\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this),\n      type: \"info\",\n      showIcon: true,\n      style: {\n        fontSize: '12px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 5\n  }, this);\n};\n_s(FeatureFlagPanel, \"M+M9dcKA2X10qmNrjpZK0Btel5k=\", false, function () {\n  return [useFeatureFlags];\n});\n_c = FeatureFlagPanel;\nexport default FeatureFlagPanel;\nvar _c;\n$RefreshReg$(_c, \"FeatureFlagPanel\");", "map": {"version": 3, "names": ["React", "useState", "Drawer", "Switch", "<PERSON><PERSON>", "Space", "<PERSON><PERSON>", "Card", "Statistic", "Row", "Col", "Typography", "<PERSON><PERSON><PERSON>", "Badge", "Popconfirm", "SettingOutlined", "ReloadOutlined", "ExclamationCircleOutlined", "CheckCircleOutlined", "WarningOutlined", "featureFlags", "useFeatureFlags", "errorMonitoring", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "FEATURE_DESCRIPTIONS", "useDesignSystem", "useEnhancedButton", "useEnhancedInput", "useResponsiveLayout", "useErrorBoundary", "useLoadingStates", "useAccessibilityProvider", "useOptimizedHeader", "useOptimizedSidebar", "useSmartLoading", "useLazyLoading", "enableDebugMode", "enableErrorReporting", "enablePerformanceMonitoring", "FEATURE_CATEGORIES", "FeatureFlagPanel", "visible", "onClose", "_s", "flags", "confirmReset", "setConfirmReset", "componentHealth", "getAllComponentHealth", "errorStats", "getErrorStats", "handleFlagChange", "flag", "value", "setFlag", "handleResetAll", "resetFlags", "resetAllComponentHealth", "handleEmergencyRollback", "disableAllUXFeatures", "handleEnableAll", "enableAllUXFeatures", "getFeatureStatus", "feature", "health", "find", "h", "autoDisabled", "status", "text", "is<PERSON><PERSON><PERSON>", "renderFeatureSwitch", "style", "marginBottom", "children", "display", "alignItems", "justifyContent", "flex", "gap", "strong", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "errorCount", "title", "color", "type", "marginTop", "checked", "onChange", "disabled", "size", "onClick", "recoverFeature", "padding", "height", "process", "env", "NODE_ENV", "count", "backgroundColor", "placement", "width", "open", "extra", "danger", "icon", "gutter", "span", "Object", "values", "filter", "Boolean", "length", "suffix", "keys", "valueStyle", "totalErrors", "recentErrors", "wrap", "onConfirm", "okText", "cancelText", "entries", "map", "category", "features", "message", "description", "margin", "showIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/components/FeatureFlagPanel.tsx"], "sourcesContent": ["/**\n * 功能开关管理面板 - 开发和测试用\n */\n\nimport React, { useState } from 'react';\nimport { \n  Drawer, \n  Switch, \n  Button, \n  Space, \n  Divider, \n  Alert, \n  Card, \n  Statistic, \n  Row, \n  Col,\n  Typography,\n  Tooltip,\n  Badge,\n  Popconfirm\n} from 'antd';\nimport { \n  SettingOutlined, \n  BugOutlined, \n  ReloadOutlined,\n  ExclamationCircleOutlined,\n  CheckCircleOutlined,\n  WarningOutlined\n} from '@ant-design/icons';\nimport { featureFlags, useFeatureFlags, FeatureFlags } from '../utils/featureFlags';\nimport { errorMonitoring } from '../utils/errorMonitoring';\n\nconst { Title, Text, Paragraph } = Typography;\n\ninterface FeatureFlagPanelProps {\n  visible: boolean;\n  onClose: () => void;\n}\n\nconst FEATURE_DESCRIPTIONS: Record<keyof FeatureFlags, string> = {\n  useDesignSystem: '启用统一设计系统，包含颜色、字体、间距等规范',\n  useEnhancedButton: '使用增强按钮组件，支持波纹效果和多种样式',\n  useEnhancedInput: '使用增强输入框组件，支持验证和自适应',\n  useResponsiveLayout: '启用响应式布局组件，优化移动端体验',\n  useErrorBoundary: '启用错误边界组件，提供错误恢复机制',\n  useLoadingStates: '使用智能加载状态组件，提升加载体验',\n  useAccessibilityProvider: '启用无障碍访问功能，支持键盘导航等',\n  useOptimizedHeader: '使用优化的头部组件，包含渐变和动效',\n  useOptimizedSidebar: '使用优化的侧边栏组件，支持折叠和动画',\n  useSmartLoading: '启用智能加载，根据加载时间自动切换显示方式',\n  useLazyLoading: '启用懒加载功能，提升页面性能',\n  enableDebugMode: '启用调试模式，显示详细的调试信息',\n  enableErrorReporting: '启用错误报告，自动收集和上报错误',\n  enablePerformanceMonitoring: '启用性能监控，收集性能指标',\n};\n\nconst FEATURE_CATEGORIES = {\n  '核心组件': ['useDesignSystem', 'useEnhancedButton', 'useEnhancedInput', 'useResponsiveLayout'],\n  '错误处理': ['useErrorBoundary', 'enableErrorReporting'],\n  '性能优化': ['useLoadingStates', 'useSmartLoading', 'useLazyLoading', 'enablePerformanceMonitoring'],\n  '无障碍访问': ['useAccessibilityProvider'],\n  '界面优化': ['useOptimizedHeader', 'useOptimizedSidebar'],\n  '开发工具': ['enableDebugMode'],\n};\n\nconst FeatureFlagPanel: React.FC<FeatureFlagPanelProps> = ({ visible, onClose }) => {\n  const flags = useFeatureFlags();\n  const [confirmReset, setConfirmReset] = useState(false);\n  const componentHealth = errorMonitoring.getAllComponentHealth();\n  const errorStats = errorMonitoring.getErrorStats();\n\n  const handleFlagChange = (flag: keyof FeatureFlags, value: boolean) => {\n    featureFlags.setFlag(flag, value);\n  };\n\n  const handleResetAll = () => {\n    featureFlags.resetFlags();\n    errorMonitoring.resetAllComponentHealth();\n    setConfirmReset(false);\n  };\n\n  const handleEmergencyRollback = () => {\n    featureFlags.disableAllUXFeatures();\n    errorMonitoring.resetAllComponentHealth();\n  };\n\n  const handleEnableAll = () => {\n    featureFlags.enableAllUXFeatures();\n  };\n\n  const getFeatureStatus = (feature: keyof FeatureFlags) => {\n    const health = componentHealth.find(h => h.feature === feature);\n    if (health?.autoDisabled) {\n      return { status: 'error', text: '自动禁用' };\n    }\n    if (health && !health.isHealthy) {\n      return { status: 'warning', text: '不稳定' };\n    }\n    if (flags[feature]) {\n      return { status: 'success', text: '已启用' };\n    }\n    return { status: 'default', text: '已禁用' };\n  };\n\n  const renderFeatureSwitch = (feature: keyof FeatureFlags) => {\n    const status = getFeatureStatus(feature);\n    const health = componentHealth.find(h => h.feature === feature);\n    \n    return (\n      <div key={feature} style={{ marginBottom: '12px' }}>\n        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <div style={{ flex: 1 }}>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n              <Text strong>{feature}</Text>\n              <Badge \n                status={status.status as any} \n                text={status.text}\n                style={{ fontSize: '12px' }}\n              />\n              {health?.errorCount > 0 && (\n                <Tooltip title={`错误次数: ${health.errorCount}`}>\n                  <WarningOutlined style={{ color: '#faad14' }} />\n                </Tooltip>\n              )}\n            </div>\n            <Text type=\"secondary\" style={{ fontSize: '12px', display: 'block', marginTop: '2px' }}>\n              {FEATURE_DESCRIPTIONS[feature]}\n            </Text>\n          </div>\n          <Switch\n            checked={flags[feature]}\n            onChange={(checked) => handleFlagChange(feature, checked)}\n            disabled={health?.autoDisabled}\n            size=\"small\"\n          />\n        </div>\n        {health?.autoDisabled && (\n          <div style={{ marginTop: '4px' }}>\n            <Button\n              size=\"small\"\n              type=\"link\"\n              onClick={() => errorMonitoring.recoverFeature(feature)}\n              style={{ padding: 0, height: 'auto', fontSize: '12px' }}\n            >\n              手动恢复\n            </Button>\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  return (\n    <Drawer\n      title={\n        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n          <SettingOutlined />\n          <span>功能开关管理</span>\n          {process.env.NODE_ENV === 'development' && (\n            <Badge count=\"DEV\" style={{ backgroundColor: '#52c41a' }} />\n          )}\n        </div>\n      }\n      placement=\"right\"\n      width={480}\n      onClose={onClose}\n      open={visible}\n      extra={\n        <Space>\n          <Tooltip title=\"紧急回退所有功能\">\n            <Button \n              danger \n              size=\"small\" \n              icon={<ExclamationCircleOutlined />}\n              onClick={handleEmergencyRollback}\n            >\n              紧急回退\n            </Button>\n          </Tooltip>\n        </Space>\n      }\n    >\n      {/* 统计信息 */}\n      <Card size=\"small\" style={{ marginBottom: '16px' }}>\n        <Row gutter={16}>\n          <Col span={8}>\n            <Statistic\n              title=\"已启用\"\n              value={Object.values(flags).filter(Boolean).length}\n              suffix={`/ ${Object.keys(flags).length}`}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Col>\n          <Col span={8}>\n            <Statistic\n              title=\"总错误\"\n              value={errorStats.totalErrors}\n              valueStyle={{ color: errorStats.totalErrors > 0 ? '#cf1322' : '#3f8600' }}\n            />\n          </Col>\n          <Col span={8}>\n            <Statistic\n              title=\"近期错误\"\n              value={errorStats.recentErrors}\n              valueStyle={{ color: errorStats.recentErrors > 0 ? '#cf1322' : '#3f8600' }}\n            />\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 快速操作 */}\n      <Card size=\"small\" title=\"快速操作\" style={{ marginBottom: '16px' }}>\n        <Space wrap>\n          <Button \n            type=\"primary\" \n            size=\"small\"\n            onClick={handleEnableAll}\n            icon={<CheckCircleOutlined />}\n          >\n            启用所有UX功能\n          </Button>\n          <Button \n            size=\"small\"\n            onClick={() => featureFlags.disableAllUXFeatures()}\n            icon={<ExclamationCircleOutlined />}\n          >\n            禁用所有UX功能\n          </Button>\n          <Popconfirm\n            title=\"确定要重置所有设置吗？\"\n            onConfirm={handleResetAll}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button \n              size=\"small\"\n              icon={<ReloadOutlined />}\n            >\n              重置所有\n            </Button>\n          </Popconfirm>\n        </Space>\n      </Card>\n\n      {/* 功能开关列表 */}\n      {Object.entries(FEATURE_CATEGORIES).map(([category, features]) => (\n        <Card key={category} size=\"small\" title={category} style={{ marginBottom: '16px' }}>\n          {features.map(feature => renderFeatureSwitch(feature as keyof FeatureFlags))}\n        </Card>\n      ))}\n\n      {/* 错误信息 */}\n      {componentHealth.length > 0 && (\n        <Card size=\"small\" title=\"组件健康状态\" style={{ marginBottom: '16px' }}>\n          {componentHealth.map(health => (\n            <div key={health.feature} style={{ marginBottom: '8px' }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                <Text>{health.feature}</Text>\n                <Space>\n                  <Badge \n                    status={health.isHealthy ? 'success' : 'error'} \n                    text={health.isHealthy ? '健康' : '异常'}\n                  />\n                  {health.errorCount > 0 && (\n                    <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                      错误: {health.errorCount}\n                    </Text>\n                  )}\n                </Space>\n              </div>\n            </div>\n          ))}\n        </Card>\n      )}\n\n      {/* 使用说明 */}\n      <Alert\n        message=\"使用说明\"\n        description={\n          <div>\n            <Paragraph style={{ fontSize: '12px', margin: 0 }}>\n              • 功能开关允许安全地启用/禁用新的UX优化功能<br/>\n              • 系统会自动监控组件错误并在必要时禁用功能<br/>\n              • 紧急回退按钮可以立即禁用所有新功能<br/>\n              • 开发环境下可通过 window.featureFlags 访问API\n            </Paragraph>\n          </div>\n        }\n        type=\"info\"\n        showIcon\n        style={{ fontSize: '12px' }}\n      />\n    </Drawer>\n  );\n};\n\nexport default FeatureFlagPanel;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,KAAK,EAELC,KAAK,EACLC,IAAI,EACJC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,UAAU,QACL,MAAM;AACb,SACEC,eAAe,EAEfC,cAAc,EACdC,yBAAyB,EACzBC,mBAAmB,EACnBC,eAAe,QACV,mBAAmB;AAC1B,SAASC,YAAY,EAAEC,eAAe,QAAsB,uBAAuB;AACnF,SAASC,eAAe,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGhB,UAAU;AAO7C,MAAMiB,oBAAwD,GAAG;EAC/DC,eAAe,EAAE,wBAAwB;EACzCC,iBAAiB,EAAE,sBAAsB;EACzCC,gBAAgB,EAAE,oBAAoB;EACtCC,mBAAmB,EAAE,mBAAmB;EACxCC,gBAAgB,EAAE,mBAAmB;EACrCC,gBAAgB,EAAE,mBAAmB;EACrCC,wBAAwB,EAAE,mBAAmB;EAC7CC,kBAAkB,EAAE,mBAAmB;EACvCC,mBAAmB,EAAE,oBAAoB;EACzCC,eAAe,EAAE,uBAAuB;EACxCC,cAAc,EAAE,gBAAgB;EAChCC,eAAe,EAAE,kBAAkB;EACnCC,oBAAoB,EAAE,kBAAkB;EACxCC,2BAA2B,EAAE;AAC/B,CAAC;AAED,MAAMC,kBAAkB,GAAG;EACzB,MAAM,EAAE,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,qBAAqB,CAAC;EAC3F,MAAM,EAAE,CAAC,kBAAkB,EAAE,sBAAsB,CAAC;EACpD,MAAM,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,6BAA6B,CAAC;EAChG,OAAO,EAAE,CAAC,0BAA0B,CAAC;EACrC,MAAM,EAAE,CAAC,oBAAoB,EAAE,qBAAqB,CAAC;EACrD,MAAM,EAAE,CAAC,iBAAiB;AAC5B,CAAC;AAED,MAAMC,gBAAiD,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAClF,MAAMC,KAAK,GAAG3B,eAAe,CAAC,CAAC;EAC/B,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMkD,eAAe,GAAG7B,eAAe,CAAC8B,qBAAqB,CAAC,CAAC;EAC/D,MAAMC,UAAU,GAAG/B,eAAe,CAACgC,aAAa,CAAC,CAAC;EAElD,MAAMC,gBAAgB,GAAGA,CAACC,IAAwB,EAAEC,KAAc,KAAK;IACrErC,YAAY,CAACsC,OAAO,CAACF,IAAI,EAAEC,KAAK,CAAC;EACnC,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3BvC,YAAY,CAACwC,UAAU,CAAC,CAAC;IACzBtC,eAAe,CAACuC,uBAAuB,CAAC,CAAC;IACzCX,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMY,uBAAuB,GAAGA,CAAA,KAAM;IACpC1C,YAAY,CAAC2C,oBAAoB,CAAC,CAAC;IACnCzC,eAAe,CAACuC,uBAAuB,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B5C,YAAY,CAAC6C,mBAAmB,CAAC,CAAC;EACpC,CAAC;EAED,MAAMC,gBAAgB,GAAIC,OAA2B,IAAK;IACxD,MAAMC,MAAM,GAAGjB,eAAe,CAACkB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,OAAO,KAAKA,OAAO,CAAC;IAC/D,IAAIC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEG,YAAY,EAAE;MACxB,OAAO;QAAEC,MAAM,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAO,CAAC;IAC1C;IACA,IAAIL,MAAM,IAAI,CAACA,MAAM,CAACM,SAAS,EAAE;MAC/B,OAAO;QAAEF,MAAM,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAM,CAAC;IAC3C;IACA,IAAIzB,KAAK,CAACmB,OAAO,CAAC,EAAE;MAClB,OAAO;QAAEK,MAAM,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAM,CAAC;IAC3C;IACA,OAAO;MAAED,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAM,CAAC;EAC3C,CAAC;EAED,MAAME,mBAAmB,GAAIR,OAA2B,IAAK;IAC3D,MAAMK,MAAM,GAAGN,gBAAgB,CAACC,OAAO,CAAC;IACxC,MAAMC,MAAM,GAAGjB,eAAe,CAACkB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,OAAO,KAAKA,OAAO,CAAC;IAE/D,oBACE3C,OAAA;MAAmBoD,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAC,QAAA,gBACjDtD,OAAA;QAAKoD,KAAK,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAH,QAAA,gBACrFtD,OAAA;UAAKoD,KAAK,EAAE;YAAEM,IAAI,EAAE;UAAE,CAAE;UAAAJ,QAAA,gBACtBtD,OAAA;YAAKoD,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEG,GAAG,EAAE;YAAM,CAAE;YAAAL,QAAA,gBAChEtD,OAAA,CAACE,IAAI;cAAC0D,MAAM;cAAAN,QAAA,EAAEX;YAAO;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7BhE,OAAA,CAACX,KAAK;cACJ2D,MAAM,EAAEA,MAAM,CAACA,MAAc;cAC7BC,IAAI,EAAED,MAAM,CAACC,IAAK;cAClBG,KAAK,EAAE;gBAAEa,QAAQ,EAAE;cAAO;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,EACD,CAAApB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEsB,UAAU,IAAG,CAAC,iBACrBlE,OAAA,CAACZ,OAAO;cAAC+E,KAAK,EAAE,SAASvB,MAAM,CAACsB,UAAU,EAAG;cAAAZ,QAAA,eAC3CtD,OAAA,CAACL,eAAe;gBAACyD,KAAK,EAAE;kBAAEgB,KAAK,EAAE;gBAAU;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CACV;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNhE,OAAA,CAACE,IAAI;YAACmE,IAAI,EAAC,WAAW;YAACjB,KAAK,EAAE;cAAEa,QAAQ,EAAE,MAAM;cAAEV,OAAO,EAAE,OAAO;cAAEe,SAAS,EAAE;YAAM,CAAE;YAAAhB,QAAA,EACpFlD,oBAAoB,CAACuC,OAAO;UAAC;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNhE,OAAA,CAACrB,MAAM;UACL4F,OAAO,EAAE/C,KAAK,CAACmB,OAAO,CAAE;UACxB6B,QAAQ,EAAGD,OAAO,IAAKxC,gBAAgB,CAACY,OAAO,EAAE4B,OAAO,CAAE;UAC1DE,QAAQ,EAAE7B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,YAAa;UAC/B2B,IAAI,EAAC;QAAO;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EACL,CAAApB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,YAAY,kBACnB/C,OAAA;QAAKoD,KAAK,EAAE;UAAEkB,SAAS,EAAE;QAAM,CAAE;QAAAhB,QAAA,eAC/BtD,OAAA,CAACpB,MAAM;UACL8F,IAAI,EAAC,OAAO;UACZL,IAAI,EAAC,MAAM;UACXM,OAAO,EAAEA,CAAA,KAAM7E,eAAe,CAAC8E,cAAc,CAACjC,OAAO,CAAE;UACvDS,KAAK,EAAE;YAAEyB,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE,MAAM;YAAEb,QAAQ,EAAE;UAAO,CAAE;UAAAX,QAAA,EACzD;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA,GAtCOrB,OAAO;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAuCZ,CAAC;EAEV,CAAC;EAED,oBACEhE,OAAA,CAACtB,MAAM;IACLyF,KAAK,eACHnE,OAAA;MAAKoD,KAAK,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEG,GAAG,EAAE;MAAM,CAAE;MAAAL,QAAA,gBAChEtD,OAAA,CAACT,eAAe;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnBhE,OAAA;QAAAsD,QAAA,EAAM;MAAM;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAClBe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCjF,OAAA,CAACX,KAAK;QAAC6F,KAAK,EAAC,KAAK;QAAC9B,KAAK,EAAE;UAAE+B,eAAe,EAAE;QAAU;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC5D;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;IACDoB,SAAS,EAAC,OAAO;IACjBC,KAAK,EAAE,GAAI;IACX/D,OAAO,EAAEA,OAAQ;IACjBgE,IAAI,EAAEjE,OAAQ;IACdkE,KAAK,eACHvF,OAAA,CAACnB,KAAK;MAAAyE,QAAA,eACJtD,OAAA,CAACZ,OAAO;QAAC+E,KAAK,EAAC,kDAAU;QAAAb,QAAA,eACvBtD,OAAA,CAACpB,MAAM;UACL4G,MAAM;UACNd,IAAI,EAAC,OAAO;UACZe,IAAI,eAAEzF,OAAA,CAACP,yBAAyB;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpCW,OAAO,EAAErC,uBAAwB;UAAAgB,QAAA,EAClC;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;IAAAV,QAAA,gBAGDtD,OAAA,CAACjB,IAAI;MAAC2F,IAAI,EAAC,OAAO;MAACtB,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAC,QAAA,eACjDtD,OAAA,CAACf,GAAG;QAACyG,MAAM,EAAE,EAAG;QAAApC,QAAA,gBACdtD,OAAA,CAACd,GAAG;UAACyG,IAAI,EAAE,CAAE;UAAArC,QAAA,eACXtD,OAAA,CAAChB,SAAS;YACRmF,KAAK,EAAC,oBAAK;YACXlC,KAAK,EAAE2D,MAAM,CAACC,MAAM,CAACrE,KAAK,CAAC,CAACsE,MAAM,CAACC,OAAO,CAAC,CAACC,MAAO;YACnDC,MAAM,EAAE,KAAKL,MAAM,CAACM,IAAI,CAAC1E,KAAK,CAAC,CAACwE,MAAM,EAAG;YACzCG,UAAU,EAAE;cAAE/B,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhE,OAAA,CAACd,GAAG;UAACyG,IAAI,EAAE,CAAE;UAAArC,QAAA,eACXtD,OAAA,CAAChB,SAAS;YACRmF,KAAK,EAAC,oBAAK;YACXlC,KAAK,EAAEJ,UAAU,CAACuE,WAAY;YAC9BD,UAAU,EAAE;cAAE/B,KAAK,EAAEvC,UAAU,CAACuE,WAAW,GAAG,CAAC,GAAG,SAAS,GAAG;YAAU;UAAE;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhE,OAAA,CAACd,GAAG;UAACyG,IAAI,EAAE,CAAE;UAAArC,QAAA,eACXtD,OAAA,CAAChB,SAAS;YACRmF,KAAK,EAAC,0BAAM;YACZlC,KAAK,EAAEJ,UAAU,CAACwE,YAAa;YAC/BF,UAAU,EAAE;cAAE/B,KAAK,EAAEvC,UAAU,CAACwE,YAAY,GAAG,CAAC,GAAG,SAAS,GAAG;YAAU;UAAE;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPhE,OAAA,CAACjB,IAAI;MAAC2F,IAAI,EAAC,OAAO;MAACP,KAAK,EAAC,0BAAM;MAACf,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAC,QAAA,eAC9DtD,OAAA,CAACnB,KAAK;QAACyH,IAAI;QAAAhD,QAAA,gBACTtD,OAAA,CAACpB,MAAM;UACLyF,IAAI,EAAC,SAAS;UACdK,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEnC,eAAgB;UACzBiD,IAAI,eAAEzF,OAAA,CAACN,mBAAmB;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAV,QAAA,EAC/B;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThE,OAAA,CAACpB,MAAM;UACL8F,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEA,CAAA,KAAM/E,YAAY,CAAC2C,oBAAoB,CAAC,CAAE;UACnDkD,IAAI,eAAEzF,OAAA,CAACP,yBAAyB;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAV,QAAA,EACrC;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThE,OAAA,CAACV,UAAU;UACT6E,KAAK,EAAC,oEAAa;UACnBoC,SAAS,EAAEpE,cAAe;UAC1BqE,MAAM,EAAC,cAAI;UACXC,UAAU,EAAC,cAAI;UAAAnD,QAAA,eAEftD,OAAA,CAACpB,MAAM;YACL8F,IAAI,EAAC,OAAO;YACZe,IAAI,eAAEzF,OAAA,CAACR,cAAc;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAV,QAAA,EAC1B;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGN4B,MAAM,CAACc,OAAO,CAACvF,kBAAkB,CAAC,CAACwF,GAAG,CAAC,CAAC,CAACC,QAAQ,EAAEC,QAAQ,CAAC,kBAC3D7G,OAAA,CAACjB,IAAI;MAAgB2F,IAAI,EAAC,OAAO;MAACP,KAAK,EAAEyC,QAAS;MAACxD,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAC,QAAA,EAChFuD,QAAQ,CAACF,GAAG,CAAChE,OAAO,IAAIQ,mBAAmB,CAACR,OAA6B,CAAC;IAAC,GADnEiE,QAAQ;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEb,CACP,CAAC,EAGDrC,eAAe,CAACqE,MAAM,GAAG,CAAC,iBACzBhG,OAAA,CAACjB,IAAI;MAAC2F,IAAI,EAAC,OAAO;MAACP,KAAK,EAAC,sCAAQ;MAACf,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAC,QAAA,EAC/D3B,eAAe,CAACgF,GAAG,CAAC/D,MAAM,iBACzB5C,OAAA;QAA0BoD,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAAC,QAAA,eACvDtD,OAAA;UAAKoD,KAAK,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE;UAAS,CAAE;UAAAF,QAAA,gBACrFtD,OAAA,CAACE,IAAI;YAAAoD,QAAA,EAAEV,MAAM,CAACD;UAAO;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7BhE,OAAA,CAACnB,KAAK;YAAAyE,QAAA,gBACJtD,OAAA,CAACX,KAAK;cACJ2D,MAAM,EAAEJ,MAAM,CAACM,SAAS,GAAG,SAAS,GAAG,OAAQ;cAC/CD,IAAI,EAAEL,MAAM,CAACM,SAAS,GAAG,IAAI,GAAG;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,EACDpB,MAAM,CAACsB,UAAU,GAAG,CAAC,iBACpBlE,OAAA,CAACE,IAAI;cAACmE,IAAI,EAAC,WAAW;cAACjB,KAAK,EAAE;gBAAEa,QAAQ,EAAE;cAAO,CAAE;cAAAX,QAAA,GAAC,gBAC9C,EAACV,MAAM,CAACsB,UAAU;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC,GAdEpB,MAAM,CAACD,OAAO;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAenB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAGDhE,OAAA,CAAClB,KAAK;MACJgI,OAAO,EAAC,0BAAM;MACdC,WAAW,eACT/G,OAAA;QAAAsD,QAAA,eACEtD,OAAA,CAACG,SAAS;UAACiD,KAAK,EAAE;YAAEa,QAAQ,EAAE,MAAM;YAAE+C,MAAM,EAAE;UAAE,CAAE;UAAA1D,QAAA,GAAC,8HACzB,eAAAtD,OAAA;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,mIACP,eAAAhE,OAAA;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,iHACR,eAAAhE,OAAA;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,+FAE1B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACN;MACDK,IAAI,EAAC,MAAM;MACX4C,QAAQ;MACR7D,KAAK,EAAE;QAAEa,QAAQ,EAAE;MAAO;IAAE;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEb,CAAC;AAACzC,EAAA,CArOIH,gBAAiD;EAAA,QACvCvB,eAAe;AAAA;AAAAqH,EAAA,GADzB9F,gBAAiD;AAuOvD,eAAeA,gBAAgB;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}