{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * 功能开关系统 - 用于安全地启用/禁用新的UX优化功能\n */\n\n// 默认配置 - 所有新功能默认关闭，确保安全\nconst DEFAULT_FLAGS = {\n  useDesignSystem: false,\n  useEnhancedButton: false,\n  useEnhancedInput: false,\n  useResponsiveLayout: false,\n  useErrorBoundary: false,\n  useLoadingStates: false,\n  useAccessibilityProvider: false,\n  useOptimizedHeader: false,\n  useOptimizedSidebar: false,\n  useSmartLoading: false,\n  useLazyLoading: false,\n  enableDebugMode: false,\n  enableErrorReporting: true,\n  // 错误报告默认开启\n  enablePerformanceMonitoring: false\n};\n\n// 开发环境配置 - 可以启用更多功能进行测试\nconst DEVELOPMENT_FLAGS = {\n  enableDebugMode: true,\n  enablePerformanceMonitoring: true\n  // 可以在这里启用需要测试的功能\n  // useDesignSystem: true,\n};\n\n// 生产环境配置 - 只启用经过充分测试的功能\nconst PRODUCTION_FLAGS = {\n  enableDebugMode: false,\n  enableErrorReporting: true\n  // 生产环境中谨慎启用新功能\n};\nclass FeatureFlagManager {\n  constructor() {\n    this.flags = void 0;\n    this.listeners = [];\n    this.flags = this.loadFlags();\n    this.setupStorageListener();\n  }\n  loadFlags() {\n    try {\n      // 从localStorage读取用户自定义配置\n      const savedFlags = localStorage.getItem('ux-feature-flags');\n      const userFlags = savedFlags ? JSON.parse(savedFlags) : {};\n\n      // 根据环境合并配置\n      const envFlags = process.env.NODE_ENV === 'development' ? DEVELOPMENT_FLAGS : PRODUCTION_FLAGS;\n      return {\n        ...DEFAULT_FLAGS,\n        ...envFlags,\n        ...userFlags\n      };\n    } catch (error) {\n      console.warn('Failed to load feature flags, using defaults:', error);\n      return {\n        ...DEFAULT_FLAGS\n      };\n    }\n  }\n  saveFlags() {\n    try {\n      localStorage.setItem('ux-feature-flags', JSON.stringify(this.flags));\n    } catch (error) {\n      console.warn('Failed to save feature flags:', error);\n    }\n  }\n  setupStorageListener() {\n    window.addEventListener('storage', event => {\n      if (event.key === 'ux-feature-flags') {\n        this.flags = this.loadFlags();\n        this.notifyListeners();\n      }\n    });\n  }\n  notifyListeners() {\n    this.listeners.forEach(listener => {\n      try {\n        listener(this.flags);\n      } catch (error) {\n        console.error('Error in feature flag listener:', error);\n      }\n    });\n  }\n\n  // 获取单个功能标志\n  isEnabled(flag) {\n    return this.flags[flag];\n  }\n\n  // 获取所有功能标志\n  getFlags() {\n    return {\n      ...this.flags\n    };\n  }\n\n  // 设置单个功能标志\n  setFlag(flag, value) {\n    this.flags[flag] = value;\n    this.saveFlags();\n    this.notifyListeners();\n    if (this.isEnabled('enableDebugMode')) {\n      console.log(`Feature flag ${flag} set to ${value}`);\n    }\n  }\n\n  // 批量设置功能标志\n  setFlags(flags) {\n    this.flags = {\n      ...this.flags,\n      ...flags\n    };\n    this.saveFlags();\n    this.notifyListeners();\n    if (this.isEnabled('enableDebugMode')) {\n      console.log('Feature flags updated:', flags);\n    }\n  }\n\n  // 重置所有功能标志\n  resetFlags() {\n    this.flags = {\n      ...DEFAULT_FLAGS\n    };\n    this.saveFlags();\n    this.notifyListeners();\n    console.log('Feature flags reset to defaults');\n  }\n\n  // 一键启用所有UX优化（谨慎使用）\n  enableAllUXFeatures() {\n    const uxFlags = {\n      useDesignSystem: true,\n      useEnhancedButton: true,\n      useEnhancedInput: true,\n      useResponsiveLayout: true,\n      useErrorBoundary: true,\n      useLoadingStates: true,\n      useOptimizedHeader: true,\n      useOptimizedSidebar: true,\n      useSmartLoading: true\n    };\n    this.setFlags(uxFlags);\n    console.log('All UX features enabled');\n  }\n\n  // 一键禁用所有UX优化（紧急回退）\n  disableAllUXFeatures() {\n    const uxFlags = {\n      useDesignSystem: false,\n      useEnhancedButton: false,\n      useEnhancedInput: false,\n      useResponsiveLayout: false,\n      useErrorBoundary: false,\n      useLoadingStates: false,\n      useAccessibilityProvider: false,\n      useOptimizedHeader: false,\n      useOptimizedSidebar: false,\n      useSmartLoading: false,\n      useLazyLoading: false\n    };\n    this.setFlags(uxFlags);\n    console.log('All UX features disabled (emergency rollback)');\n  }\n\n  // 监听功能标志变化\n  subscribe(listener) {\n    this.listeners.push(listener);\n\n    // 返回取消订阅函数\n    return () => {\n      const index = this.listeners.indexOf(listener);\n      if (index > -1) {\n        this.listeners.splice(index, 1);\n      }\n    };\n  }\n\n  // 获取功能使用统计\n  getUsageStats() {\n    return Object.entries(this.flags).filter(([key]) => key.startsWith('use')).reduce((stats, [key, value]) => {\n      stats[key] = value;\n      return stats;\n    }, {});\n  }\n}\n\n// 全局实例\nexport const featureFlags = new FeatureFlagManager();\n\n// React Hook\nimport { useState, useEffect } from 'react';\nexport function useFeatureFlag(flag) {\n  _s();\n  const [isEnabled, setIsEnabled] = useState(featureFlags.isEnabled(flag));\n  useEffect(() => {\n    const unsubscribe = featureFlags.subscribe(flags => {\n      setIsEnabled(flags[flag]);\n    });\n    return unsubscribe;\n  }, [flag]);\n  return isEnabled;\n}\n_s(useFeatureFlag, \"+v7Ft85pkK94ap0UZQIvORvtjKA=\");\nexport function useFeatureFlags() {\n  _s2();\n  const [flags, setFlags] = useState(featureFlags.getFlags());\n  useEffect(() => {\n    const unsubscribe = featureFlags.subscribe(setFlags);\n    return unsubscribe;\n  }, []);\n  return flags;\n}\n\n// 开发者工具 - 仅在开发环境中可用\n_s2(useFeatureFlags, \"by/+xwqXSeZLcyzDCku0JAVMXis=\");\nif (process.env.NODE_ENV === 'development') {\n  window.featureFlags = featureFlags;\n  console.log('Feature flags manager available at window.featureFlags');\n}", "map": {"version": 3, "names": ["DEFAULT_FLAGS", "useDesignSystem", "useEnhancedButton", "useEnhancedInput", "useResponsiveLayout", "useErrorBoundary", "useLoadingStates", "useAccessibilityProvider", "useOptimizedHeader", "useOptimizedSidebar", "useSmartLoading", "useLazyLoading", "enableDebugMode", "enableErrorReporting", "enablePerformanceMonitoring", "DEVELOPMENT_FLAGS", "PRODUCTION_FLAGS", "FeatureFlagManager", "constructor", "flags", "listeners", "loadFlags", "setupStorageListener", "savedFlags", "localStorage", "getItem", "userFlags", "JSON", "parse", "envFlags", "process", "env", "NODE_ENV", "error", "console", "warn", "saveFlags", "setItem", "stringify", "window", "addEventListener", "event", "key", "notifyListeners", "for<PERSON>ach", "listener", "isEnabled", "flag", "getFlags", "setFlag", "value", "log", "setFlags", "resetFlags", "enableAllUXFeatures", "uxFlags", "disableAllUXFeatures", "subscribe", "push", "index", "indexOf", "splice", "getUsageStats", "Object", "entries", "filter", "startsWith", "reduce", "stats", "featureFlags", "useState", "useEffect", "useFeatureFlag", "_s", "setIsEnabled", "unsubscribe", "useFeatureFlags", "_s2"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/utils/featureFlags.ts"], "sourcesContent": ["/**\n * 功能开关系统 - 用于安全地启用/禁用新的UX优化功能\n */\n\nexport interface FeatureFlags {\n  // 设计系统\n  useDesignSystem: boolean;\n  \n  // 组件优化\n  useEnhancedButton: boolean;\n  useEnhancedInput: boolean;\n  useResponsiveLayout: boolean;\n  useErrorBoundary: boolean;\n  useLoadingStates: boolean;\n  useAccessibilityProvider: boolean;\n  \n  // 样式优化\n  useOptimizedHeader: boolean;\n  useOptimizedSidebar: boolean;\n  \n  // 性能优化\n  useSmartLoading: boolean;\n  useLazyLoading: boolean;\n  \n  // 调试和监控\n  enableDebugMode: boolean;\n  enableErrorReporting: boolean;\n  enablePerformanceMonitoring: boolean;\n}\n\n// 默认配置 - 所有新功能默认关闭，确保安全\nconst DEFAULT_FLAGS: FeatureFlags = {\n  useDesignSystem: false,\n  useEnhancedButton: false,\n  useEnhancedInput: false,\n  useResponsiveLayout: false,\n  useErrorBoundary: false,\n  useLoadingStates: false,\n  useAccessibilityProvider: false,\n  useOptimizedHeader: false,\n  useOptimizedSidebar: false,\n  useSmartLoading: false,\n  useLazyLoading: false,\n  enableDebugMode: false,\n  enableErrorReporting: true, // 错误报告默认开启\n  enablePerformanceMonitoring: false,\n};\n\n// 开发环境配置 - 可以启用更多功能进行测试\nconst DEVELOPMENT_FLAGS: Partial<FeatureFlags> = {\n  enableDebugMode: true,\n  enablePerformanceMonitoring: true,\n  // 可以在这里启用需要测试的功能\n  // useDesignSystem: true,\n};\n\n// 生产环境配置 - 只启用经过充分测试的功能\nconst PRODUCTION_FLAGS: Partial<FeatureFlags> = {\n  enableDebugMode: false,\n  enableErrorReporting: true,\n  // 生产环境中谨慎启用新功能\n};\n\nclass FeatureFlagManager {\n  private flags: FeatureFlags;\n  private listeners: Array<(flags: FeatureFlags) => void> = [];\n\n  constructor() {\n    this.flags = this.loadFlags();\n    this.setupStorageListener();\n  }\n\n  private loadFlags(): FeatureFlags {\n    try {\n      // 从localStorage读取用户自定义配置\n      const savedFlags = localStorage.getItem('ux-feature-flags');\n      const userFlags = savedFlags ? JSON.parse(savedFlags) : {};\n\n      // 根据环境合并配置\n      const envFlags = process.env.NODE_ENV === 'development' \n        ? DEVELOPMENT_FLAGS \n        : PRODUCTION_FLAGS;\n\n      return {\n        ...DEFAULT_FLAGS,\n        ...envFlags,\n        ...userFlags,\n      };\n    } catch (error) {\n      console.warn('Failed to load feature flags, using defaults:', error);\n      return { ...DEFAULT_FLAGS };\n    }\n  }\n\n  private saveFlags(): void {\n    try {\n      localStorage.setItem('ux-feature-flags', JSON.stringify(this.flags));\n    } catch (error) {\n      console.warn('Failed to save feature flags:', error);\n    }\n  }\n\n  private setupStorageListener(): void {\n    window.addEventListener('storage', (event) => {\n      if (event.key === 'ux-feature-flags') {\n        this.flags = this.loadFlags();\n        this.notifyListeners();\n      }\n    });\n  }\n\n  private notifyListeners(): void {\n    this.listeners.forEach(listener => {\n      try {\n        listener(this.flags);\n      } catch (error) {\n        console.error('Error in feature flag listener:', error);\n      }\n    });\n  }\n\n  // 获取单个功能标志\n  isEnabled(flag: keyof FeatureFlags): boolean {\n    return this.flags[flag];\n  }\n\n  // 获取所有功能标志\n  getFlags(): FeatureFlags {\n    return { ...this.flags };\n  }\n\n  // 设置单个功能标志\n  setFlag(flag: keyof FeatureFlags, value: boolean): void {\n    this.flags[flag] = value;\n    this.saveFlags();\n    this.notifyListeners();\n    \n    if (this.isEnabled('enableDebugMode')) {\n      console.log(`Feature flag ${flag} set to ${value}`);\n    }\n  }\n\n  // 批量设置功能标志\n  setFlags(flags: Partial<FeatureFlags>): void {\n    this.flags = { ...this.flags, ...flags };\n    this.saveFlags();\n    this.notifyListeners();\n    \n    if (this.isEnabled('enableDebugMode')) {\n      console.log('Feature flags updated:', flags);\n    }\n  }\n\n  // 重置所有功能标志\n  resetFlags(): void {\n    this.flags = { ...DEFAULT_FLAGS };\n    this.saveFlags();\n    this.notifyListeners();\n    \n    console.log('Feature flags reset to defaults');\n  }\n\n  // 一键启用所有UX优化（谨慎使用）\n  enableAllUXFeatures(): void {\n    const uxFlags: Partial<FeatureFlags> = {\n      useDesignSystem: true,\n      useEnhancedButton: true,\n      useEnhancedInput: true,\n      useResponsiveLayout: true,\n      useErrorBoundary: true,\n      useLoadingStates: true,\n      useOptimizedHeader: true,\n      useOptimizedSidebar: true,\n      useSmartLoading: true,\n    };\n    \n    this.setFlags(uxFlags);\n    console.log('All UX features enabled');\n  }\n\n  // 一键禁用所有UX优化（紧急回退）\n  disableAllUXFeatures(): void {\n    const uxFlags: Partial<FeatureFlags> = {\n      useDesignSystem: false,\n      useEnhancedButton: false,\n      useEnhancedInput: false,\n      useResponsiveLayout: false,\n      useErrorBoundary: false,\n      useLoadingStates: false,\n      useAccessibilityProvider: false,\n      useOptimizedHeader: false,\n      useOptimizedSidebar: false,\n      useSmartLoading: false,\n      useLazyLoading: false,\n    };\n    \n    this.setFlags(uxFlags);\n    console.log('All UX features disabled (emergency rollback)');\n  }\n\n  // 监听功能标志变化\n  subscribe(listener: (flags: FeatureFlags) => void): () => void {\n    this.listeners.push(listener);\n    \n    // 返回取消订阅函数\n    return () => {\n      const index = this.listeners.indexOf(listener);\n      if (index > -1) {\n        this.listeners.splice(index, 1);\n      }\n    };\n  }\n\n  // 获取功能使用统计\n  getUsageStats(): Record<string, boolean> {\n    return Object.entries(this.flags)\n      .filter(([key]) => key.startsWith('use'))\n      .reduce((stats, [key, value]) => {\n        stats[key] = value;\n        return stats;\n      }, {} as Record<string, boolean>);\n  }\n}\n\n// 全局实例\nexport const featureFlags = new FeatureFlagManager();\n\n// React Hook\nimport { useState, useEffect } from 'react';\n\nexport function useFeatureFlag(flag: keyof FeatureFlags): boolean {\n  const [isEnabled, setIsEnabled] = useState(featureFlags.isEnabled(flag));\n\n  useEffect(() => {\n    const unsubscribe = featureFlags.subscribe((flags) => {\n      setIsEnabled(flags[flag]);\n    });\n\n    return unsubscribe;\n  }, [flag]);\n\n  return isEnabled;\n}\n\nexport function useFeatureFlags(): FeatureFlags {\n  const [flags, setFlags] = useState(featureFlags.getFlags());\n\n  useEffect(() => {\n    const unsubscribe = featureFlags.subscribe(setFlags);\n    return unsubscribe;\n  }, []);\n\n  return flags;\n}\n\n// 开发者工具 - 仅在开发环境中可用\nif (process.env.NODE_ENV === 'development') {\n  (window as any).featureFlags = featureFlags;\n  console.log('Feature flags manager available at window.featureFlags');\n}\n"], "mappings": ";;AAAA;AACA;AACA;;AA4BA;AACA,MAAMA,aAA2B,GAAG;EAClCC,eAAe,EAAE,KAAK;EACtBC,iBAAiB,EAAE,KAAK;EACxBC,gBAAgB,EAAE,KAAK;EACvBC,mBAAmB,EAAE,KAAK;EAC1BC,gBAAgB,EAAE,KAAK;EACvBC,gBAAgB,EAAE,KAAK;EACvBC,wBAAwB,EAAE,KAAK;EAC/BC,kBAAkB,EAAE,KAAK;EACzBC,mBAAmB,EAAE,KAAK;EAC1BC,eAAe,EAAE,KAAK;EACtBC,cAAc,EAAE,KAAK;EACrBC,eAAe,EAAE,KAAK;EACtBC,oBAAoB,EAAE,IAAI;EAAE;EAC5BC,2BAA2B,EAAE;AAC/B,CAAC;;AAED;AACA,MAAMC,iBAAwC,GAAG;EAC/CH,eAAe,EAAE,IAAI;EACrBE,2BAA2B,EAAE;EAC7B;EACA;AACF,CAAC;;AAED;AACA,MAAME,gBAAuC,GAAG;EAC9CJ,eAAe,EAAE,KAAK;EACtBC,oBAAoB,EAAE;EACtB;AACF,CAAC;AAED,MAAMI,kBAAkB,CAAC;EAIvBC,WAAWA,CAAA,EAAG;IAAA,KAHNC,KAAK;IAAA,KACLC,SAAS,GAAyC,EAAE;IAG1D,IAAI,CAACD,KAAK,GAAG,IAAI,CAACE,SAAS,CAAC,CAAC;IAC7B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC7B;EAEQD,SAASA,CAAA,EAAiB;IAChC,IAAI;MACF;MACA,MAAME,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;MAC3D,MAAMC,SAAS,GAAGH,UAAU,GAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,GAAG,CAAC,CAAC;;MAE1D;MACA,MAAMM,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,GACnDjB,iBAAiB,GACjBC,gBAAgB;MAEpB,OAAO;QACL,GAAGhB,aAAa;QAChB,GAAG6B,QAAQ;QACX,GAAGH;MACL,CAAC;IACH,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,+CAA+C,EAAEF,KAAK,CAAC;MACpE,OAAO;QAAE,GAAGjC;MAAc,CAAC;IAC7B;EACF;EAEQoC,SAASA,CAAA,EAAS;IACxB,IAAI;MACFZ,YAAY,CAACa,OAAO,CAAC,kBAAkB,EAAEV,IAAI,CAACW,SAAS,CAAC,IAAI,CAACnB,KAAK,CAAC,CAAC;IACtE,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,+BAA+B,EAAEF,KAAK,CAAC;IACtD;EACF;EAEQX,oBAAoBA,CAAA,EAAS;IACnCiB,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAK;MAC5C,IAAIA,KAAK,CAACC,GAAG,KAAK,kBAAkB,EAAE;QACpC,IAAI,CAACvB,KAAK,GAAG,IAAI,CAACE,SAAS,CAAC,CAAC;QAC7B,IAAI,CAACsB,eAAe,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;EACJ;EAEQA,eAAeA,CAAA,EAAS;IAC9B,IAAI,CAACvB,SAAS,CAACwB,OAAO,CAACC,QAAQ,IAAI;MACjC,IAAI;QACFA,QAAQ,CAAC,IAAI,CAAC1B,KAAK,CAAC;MACtB,CAAC,CAAC,OAAOc,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;IACF,CAAC,CAAC;EACJ;;EAEA;EACAa,SAASA,CAACC,IAAwB,EAAW;IAC3C,OAAO,IAAI,CAAC5B,KAAK,CAAC4B,IAAI,CAAC;EACzB;;EAEA;EACAC,QAAQA,CAAA,EAAiB;IACvB,OAAO;MAAE,GAAG,IAAI,CAAC7B;IAAM,CAAC;EAC1B;;EAEA;EACA8B,OAAOA,CAACF,IAAwB,EAAEG,KAAc,EAAQ;IACtD,IAAI,CAAC/B,KAAK,CAAC4B,IAAI,CAAC,GAAGG,KAAK;IACxB,IAAI,CAACd,SAAS,CAAC,CAAC;IAChB,IAAI,CAACO,eAAe,CAAC,CAAC;IAEtB,IAAI,IAAI,CAACG,SAAS,CAAC,iBAAiB,CAAC,EAAE;MACrCZ,OAAO,CAACiB,GAAG,CAAC,gBAAgBJ,IAAI,WAAWG,KAAK,EAAE,CAAC;IACrD;EACF;;EAEA;EACAE,QAAQA,CAACjC,KAA4B,EAAQ;IAC3C,IAAI,CAACA,KAAK,GAAG;MAAE,GAAG,IAAI,CAACA,KAAK;MAAE,GAAGA;IAAM,CAAC;IACxC,IAAI,CAACiB,SAAS,CAAC,CAAC;IAChB,IAAI,CAACO,eAAe,CAAC,CAAC;IAEtB,IAAI,IAAI,CAACG,SAAS,CAAC,iBAAiB,CAAC,EAAE;MACrCZ,OAAO,CAACiB,GAAG,CAAC,wBAAwB,EAAEhC,KAAK,CAAC;IAC9C;EACF;;EAEA;EACAkC,UAAUA,CAAA,EAAS;IACjB,IAAI,CAAClC,KAAK,GAAG;MAAE,GAAGnB;IAAc,CAAC;IACjC,IAAI,CAACoC,SAAS,CAAC,CAAC;IAChB,IAAI,CAACO,eAAe,CAAC,CAAC;IAEtBT,OAAO,CAACiB,GAAG,CAAC,iCAAiC,CAAC;EAChD;;EAEA;EACAG,mBAAmBA,CAAA,EAAS;IAC1B,MAAMC,OAA8B,GAAG;MACrCtD,eAAe,EAAE,IAAI;MACrBC,iBAAiB,EAAE,IAAI;MACvBC,gBAAgB,EAAE,IAAI;MACtBC,mBAAmB,EAAE,IAAI;MACzBC,gBAAgB,EAAE,IAAI;MACtBC,gBAAgB,EAAE,IAAI;MACtBE,kBAAkB,EAAE,IAAI;MACxBC,mBAAmB,EAAE,IAAI;MACzBC,eAAe,EAAE;IACnB,CAAC;IAED,IAAI,CAAC0C,QAAQ,CAACG,OAAO,CAAC;IACtBrB,OAAO,CAACiB,GAAG,CAAC,yBAAyB,CAAC;EACxC;;EAEA;EACAK,oBAAoBA,CAAA,EAAS;IAC3B,MAAMD,OAA8B,GAAG;MACrCtD,eAAe,EAAE,KAAK;MACtBC,iBAAiB,EAAE,KAAK;MACxBC,gBAAgB,EAAE,KAAK;MACvBC,mBAAmB,EAAE,KAAK;MAC1BC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,KAAK;MACvBC,wBAAwB,EAAE,KAAK;MAC/BC,kBAAkB,EAAE,KAAK;MACzBC,mBAAmB,EAAE,KAAK;MAC1BC,eAAe,EAAE,KAAK;MACtBC,cAAc,EAAE;IAClB,CAAC;IAED,IAAI,CAACyC,QAAQ,CAACG,OAAO,CAAC;IACtBrB,OAAO,CAACiB,GAAG,CAAC,+CAA+C,CAAC;EAC9D;;EAEA;EACAM,SAASA,CAACZ,QAAuC,EAAc;IAC7D,IAAI,CAACzB,SAAS,CAACsC,IAAI,CAACb,QAAQ,CAAC;;IAE7B;IACA,OAAO,MAAM;MACX,MAAMc,KAAK,GAAG,IAAI,CAACvC,SAAS,CAACwC,OAAO,CAACf,QAAQ,CAAC;MAC9C,IAAIc,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAACvC,SAAS,CAACyC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjC;IACF,CAAC;EACH;;EAEA;EACAG,aAAaA,CAAA,EAA4B;IACvC,OAAOC,MAAM,CAACC,OAAO,CAAC,IAAI,CAAC7C,KAAK,CAAC,CAC9B8C,MAAM,CAAC,CAAC,CAACvB,GAAG,CAAC,KAAKA,GAAG,CAACwB,UAAU,CAAC,KAAK,CAAC,CAAC,CACxCC,MAAM,CAAC,CAACC,KAAK,EAAE,CAAC1B,GAAG,EAAEQ,KAAK,CAAC,KAAK;MAC/BkB,KAAK,CAAC1B,GAAG,CAAC,GAAGQ,KAAK;MAClB,OAAOkB,KAAK;IACd,CAAC,EAAE,CAAC,CAA4B,CAAC;EACrC;AACF;;AAEA;AACA,OAAO,MAAMC,YAAY,GAAG,IAAIpD,kBAAkB,CAAC,CAAC;;AAEpD;AACA,SAASqD,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAE3C,OAAO,SAASC,cAAcA,CAACzB,IAAwB,EAAW;EAAA0B,EAAA;EAChE,MAAM,CAAC3B,SAAS,EAAE4B,YAAY,CAAC,GAAGJ,QAAQ,CAACD,YAAY,CAACvB,SAAS,CAACC,IAAI,CAAC,CAAC;EAExEwB,SAAS,CAAC,MAAM;IACd,MAAMI,WAAW,GAAGN,YAAY,CAACZ,SAAS,CAAEtC,KAAK,IAAK;MACpDuD,YAAY,CAACvD,KAAK,CAAC4B,IAAI,CAAC,CAAC;IAC3B,CAAC,CAAC;IAEF,OAAO4B,WAAW;EACpB,CAAC,EAAE,CAAC5B,IAAI,CAAC,CAAC;EAEV,OAAOD,SAAS;AAClB;AAAC2B,EAAA,CAZeD,cAAc;AAc9B,OAAO,SAASI,eAAeA,CAAA,EAAiB;EAAAC,GAAA;EAC9C,MAAM,CAAC1D,KAAK,EAAEiC,QAAQ,CAAC,GAAGkB,QAAQ,CAACD,YAAY,CAACrB,QAAQ,CAAC,CAAC,CAAC;EAE3DuB,SAAS,CAAC,MAAM;IACd,MAAMI,WAAW,GAAGN,YAAY,CAACZ,SAAS,CAACL,QAAQ,CAAC;IACpD,OAAOuB,WAAW;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOxD,KAAK;AACd;;AAEA;AAAA0D,GAAA,CAXgBD,eAAe;AAY/B,IAAI9C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;EACzCO,MAAM,CAAS8B,YAAY,GAAGA,YAAY;EAC3CnC,OAAO,CAACiB,GAAG,CAAC,wDAAwD,CAAC;AACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}