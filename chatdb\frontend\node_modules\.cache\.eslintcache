[{"C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\GraphVisualizationPage.tsx": "4", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\SchemaManagementPage.tsx": "5", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\IntelligentQueryPage.tsx": "6", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\ConnectionsPage.tsx": "7", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\ValueMappingsPage.tsx": "8", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\page.tsx": "9", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\MarkdownTest.tsx": "10", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\HybridQA\\index.tsx": "11", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\UserFeedback.tsx": "12", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\api.ts": "13", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\ErrorMessage.tsx": "14", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\ConnectionSelector.tsx": "15", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\utils.tsx": "16", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\HybridExamplesPanel.tsx": "17", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\ChatHistorySidebar.tsx": "18", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\services\\chatHistoryService.ts": "19", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\RegionPanel.tsx": "20", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\services\\api.ts": "21", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\StreamingMarkdown.tsx": "22", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\services\\hybridQA.ts": "23", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\components\\diagram\\TableNode.tsx": "24", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\components\\diagram\\CustomConnectionLine.tsx": "25", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\components\\diagram\\RelationshipEdge.tsx": "26", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\components\\diagram\\RelationshipModal.tsx": "27", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\components\\diagram\\Marker.tsx": "28", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\components\\diagram\\TableEditModal.tsx": "29", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\components\\QAFeedbackModal.tsx": "30", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\sse-api.ts": "31", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\services\\relationshipTipsService.ts": "32", "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\config.ts": "33"}, {"size": 628, "mtime": 1750343504000, "results": "34", "hashOfConfig": "35"}, {"size": 364, "mtime": 1750343504000, "results": "36", "hashOfConfig": "35"}, {"size": 3441, "mtime": 1754022639798, "results": "37", "hashOfConfig": "35"}, {"size": 8742, "mtime": 1750343504000, "results": "38", "hashOfConfig": "35"}, {"size": 63538, "mtime": 1750343504000, "results": "39", "hashOfConfig": "35"}, {"size": 11805, "mtime": 1753957706816, "results": "40", "hashOfConfig": "35"}, {"size": 10806, "mtime": 1750859297712, "results": "41", "hashOfConfig": "35"}, {"size": 12911, "mtime": 1753930735614, "results": "42", "hashOfConfig": "35"}, {"size": 75047, "mtime": 1754028015996, "results": "43", "hashOfConfig": "35"}, {"size": 3761, "mtime": 1750343504000, "results": "44", "hashOfConfig": "35"}, {"size": 26028, "mtime": 1754025710742, "results": "45", "hashOfConfig": "35"}, {"size": 4353, "mtime": 1753924461794, "results": "46", "hashOfConfig": "35"}, {"size": 22374, "mtime": 1750343504000, "results": "47", "hashOfConfig": "35"}, {"size": 1606, "mtime": 1750343504000, "results": "48", "hashOfConfig": "35"}, {"size": 2315, "mtime": 1754027316661, "results": "49", "hashOfConfig": "35"}, {"size": 19585, "mtime": 1753789808209, "results": "50", "hashOfConfig": "35"}, {"size": 9452, "mtime": 1750343504000, "results": "51", "hashOfConfig": "35"}, {"size": 5425, "mtime": 1750343504000, "results": "52", "hashOfConfig": "35"}, {"size": 3669, "mtime": 1750343504000, "results": "53", "hashOfConfig": "35"}, {"size": 24087, "mtime": 1750343504000, "results": "54", "hashOfConfig": "35"}, {"size": 2381, "mtime": 1750343504000, "results": "55", "hashOfConfig": "35"}, {"size": 9650, "mtime": 1750343504000, "results": "56", "hashOfConfig": "35"}, {"size": 4902, "mtime": 1754025851941, "results": "57", "hashOfConfig": "35"}, {"size": 13464, "mtime": 1750343504000, "results": "58", "hashOfConfig": "35"}, {"size": 885, "mtime": 1750343504000, "results": "59", "hashOfConfig": "35"}, {"size": 11548, "mtime": 1750343504000, "results": "60", "hashOfConfig": "35"}, {"size": 11951, "mtime": 1750343504000, "results": "61", "hashOfConfig": "35"}, {"size": 5215, "mtime": 1750343504000, "results": "62", "hashOfConfig": "35"}, {"size": 4467, "mtime": 1750343504000, "results": "63", "hashOfConfig": "35"}, {"size": 5034, "mtime": 1750343504000, "results": "64", "hashOfConfig": "35"}, {"size": 11606, "mtime": 1750343504000, "results": "65", "hashOfConfig": "35"}, {"size": 511, "mtime": 1750343504000, "results": "66", "hashOfConfig": "35"}, {"size": 55, "mtime": 1750343504000, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "5lpxyl", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 38, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\App.tsx", ["167", "168", "169"], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\GraphVisualizationPage.tsx", ["170", "171", "172", "173", "174"], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\SchemaManagementPage.tsx", ["175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185", "186", "187"], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\IntelligentQueryPage.tsx", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\ConnectionsPage.tsx", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\ValueMappingsPage.tsx", ["188", "189", "190"], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\page.tsx", ["191", "192", "193", "194", "195", "196", "197", "198", "199", "200", "201", "202", "203", "204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "220", "221", "222", "223", "224", "225", "226", "227", "228"], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\MarkdownTest.tsx", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\HybridQA\\index.tsx", ["229", "230"], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\UserFeedback.tsx", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\api.ts", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\ErrorMessage.tsx", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\ConnectionSelector.tsx", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\utils.tsx", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\HybridExamplesPanel.tsx", ["231", "232"], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\ChatHistorySidebar.tsx", ["233"], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\services\\chatHistoryService.ts", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\RegionPanel.tsx", ["234", "235"], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\components\\StreamingMarkdown.tsx", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\services\\hybridQA.ts", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\components\\diagram\\TableNode.tsx", ["236", "237", "238", "239"], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\components\\diagram\\CustomConnectionLine.tsx", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\components\\diagram\\RelationshipEdge.tsx", ["240"], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\components\\diagram\\RelationshipModal.tsx", ["241", "242", "243", "244", "245", "246"], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\components\\diagram\\Marker.tsx", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\components\\diagram\\TableEditModal.tsx", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\components\\QAFeedbackModal.tsx", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\pages\\text2sql\\sse-api.ts", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\services\\relationshipTipsService.ts", [], [], "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\src\\config.ts", [], [], {"ruleId": "247", "severity": 1, "message": "248", "line": 8, "column": 3, "nodeType": "249", "messageId": "250", "endLine": 8, "endColumn": 17}, {"ruleId": "247", "severity": 1, "message": "251", "line": 11, "column": 3, "nodeType": "249", "messageId": "250", "endLine": 11, "endColumn": 15}, {"ruleId": "247", "severity": 1, "message": "252", "line": 29, "column": 26, "nodeType": "249", "messageId": "250", "endLine": 29, "endColumn": 32}, {"ruleId": "247", "severity": 1, "message": "253", "line": 2, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 2, "endColumn": 14}, {"ruleId": "247", "severity": 1, "message": "254", "line": 11, "column": 3, "nodeType": "249", "messageId": "250", "endLine": 11, "endColumn": 21}, {"ruleId": "247", "severity": 1, "message": "255", "line": 18, "column": 11, "nodeType": "249", "messageId": "250", "endLine": 18, "endColumn": 19}, {"ruleId": "247", "severity": 1, "message": "256", "line": 32, "column": 9, "nodeType": "249", "messageId": "250", "endLine": 32, "endColumn": 15}, {"ruleId": "247", "severity": 1, "message": "257", "line": 42, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 42, "endColumn": 27}, {"ruleId": "247", "severity": 1, "message": "258", "line": 2, "column": 53, "nodeType": "249", "messageId": "250", "endLine": 2, "endColumn": 58}, {"ruleId": "247", "severity": 1, "message": "259", "line": 3, "column": 53, "nodeType": "249", "messageId": "250", "endLine": 3, "endColumn": 67}, {"ruleId": "247", "severity": 1, "message": "260", "line": 10, "column": 3, "nodeType": "249", "messageId": "250", "endLine": 10, "endColumn": 10}, {"ruleId": "247", "severity": 1, "message": "261", "line": 14, "column": 3, "nodeType": "249", "messageId": "250", "endLine": 14, "endColumn": 13}, {"ruleId": "247", "severity": 1, "message": "262", "line": 30, "column": 9, "nodeType": "249", "messageId": "250", "endLine": 30, "endColumn": 14}, {"ruleId": "247", "severity": 1, "message": "263", "line": 95, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 95, "endColumn": 30}, {"ruleId": "247", "severity": 1, "message": "264", "line": 95, "column": 32, "nodeType": "249", "messageId": "250", "endLine": 95, "endColumn": 55}, {"ruleId": "247", "severity": 1, "message": "265", "line": 304, "column": 9, "nodeType": "249", "messageId": "250", "endLine": 304, "endColumn": 39}, {"ruleId": "247", "severity": 1, "message": "266", "line": 885, "column": 15, "nodeType": "249", "messageId": "250", "endLine": 885, "endColumn": 20}, {"ruleId": "267", "severity": 1, "message": "268", "line": 961, "column": 6, "nodeType": "269", "endLine": 961, "endColumn": 13, "suggestions": "270"}, {"ruleId": "267", "severity": 1, "message": "271", "line": 1481, "column": 6, "nodeType": "269", "endLine": 1481, "endColumn": 8, "suggestions": "272"}, {"ruleId": "247", "severity": 1, "message": "273", "line": 1484, "column": 9, "nodeType": "249", "messageId": "250", "endLine": 1484, "endColumn": 28}, {"ruleId": "247", "severity": 1, "message": "274", "line": 1513, "column": 11, "nodeType": "249", "messageId": "250", "endLine": 1513, "endColumn": 15}, {"ruleId": "247", "severity": 1, "message": "275", "line": 54, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 54, "endColumn": 18}, {"ruleId": "247", "severity": 1, "message": "276", "line": 54, "column": 20, "nodeType": "249", "messageId": "250", "endLine": 54, "endColumn": 31}, {"ruleId": "267", "severity": 1, "message": "277", "line": 174, "column": 6, "nodeType": "269", "endLine": 174, "endColumn": 22, "suggestions": "278"}, {"ruleId": "247", "severity": 1, "message": "279", "line": 2, "column": 8, "nodeType": "249", "messageId": "250", "endLine": 2, "endColumn": 16}, {"ruleId": "247", "severity": 1, "message": "280", "line": 3, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 3, "endColumn": 14}, {"ruleId": "247", "severity": 1, "message": "281", "line": 23, "column": 8, "nodeType": "249", "messageId": "250", "endLine": 23, "endColumn": 16}, {"ruleId": "247", "severity": 1, "message": "282", "line": 24, "column": 8, "nodeType": "249", "messageId": "250", "endLine": 24, "endColumn": 12}, {"ruleId": "247", "severity": 1, "message": "283", "line": 25, "column": 8, "nodeType": "249", "messageId": "250", "endLine": 25, "endColumn": 19}, {"ruleId": "247", "severity": 1, "message": "284", "line": 26, "column": 8, "nodeType": "249", "messageId": "250", "endLine": 26, "endColumn": 14}, {"ruleId": "247", "severity": 1, "message": "285", "line": 27, "column": 8, "nodeType": "249", "messageId": "250", "endLine": 27, "endColumn": 24}, {"ruleId": "247", "severity": 1, "message": "286", "line": 28, "column": 8, "nodeType": "249", "messageId": "250", "endLine": 28, "endColumn": 20}, {"ruleId": "247", "severity": 1, "message": "287", "line": 34, "column": 8, "nodeType": "249", "messageId": "250", "endLine": 34, "endColumn": 20}, {"ruleId": "247", "severity": 1, "message": "288", "line": 37, "column": 59, "nodeType": "249", "messageId": "250", "endLine": 37, "endColumn": 74}, {"ruleId": "247", "severity": 1, "message": "289", "line": 40, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 40, "endColumn": 25}, {"ruleId": "247", "severity": 1, "message": "290", "line": 40, "column": 27, "nodeType": "249", "messageId": "250", "endLine": 40, "endColumn": 50}, {"ruleId": "247", "severity": 1, "message": "291", "line": 42, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 42, "endColumn": 16}, {"ruleId": "247", "severity": 1, "message": "292", "line": 54, "column": 7, "nodeType": "249", "messageId": "250", "endLine": 54, "endColumn": 12}, {"ruleId": "247", "severity": 1, "message": "293", "line": 72, "column": 7, "nodeType": "249", "messageId": "250", "endLine": 72, "endColumn": 15}, {"ruleId": "247", "severity": 1, "message": "256", "line": 91, "column": 7, "nodeType": "249", "messageId": "250", "endLine": 91, "endColumn": 13}, {"ruleId": "247", "severity": 1, "message": "294", "line": 109, "column": 7, "nodeType": "249", "messageId": "250", "endLine": 109, "endColumn": 15}, {"ruleId": "247", "severity": 1, "message": "295", "line": 128, "column": 7, "nodeType": "249", "messageId": "250", "endLine": 128, "endColumn": 15}, {"ruleId": "247", "severity": 1, "message": "296", "line": 149, "column": 7, "nodeType": "249", "messageId": "250", "endLine": 149, "endColumn": 11}, {"ruleId": "247", "severity": 1, "message": "297", "line": 167, "column": 7, "nodeType": "249", "messageId": "250", "endLine": 167, "endColumn": 15}, {"ruleId": "247", "severity": 1, "message": "298", "line": 187, "column": 7, "nodeType": "249", "messageId": "250", "endLine": 187, "endColumn": 18}, {"ruleId": "247", "severity": 1, "message": "299", "line": 207, "column": 7, "nodeType": "249", "messageId": "250", "endLine": 207, "endColumn": 15}, {"ruleId": "247", "severity": 1, "message": "300", "line": 228, "column": 7, "nodeType": "249", "messageId": "250", "endLine": 228, "endColumn": 31}, {"ruleId": "247", "severity": 1, "message": "301", "line": 329, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 329, "endColumn": 25}, {"ruleId": "247", "severity": 1, "message": "302", "line": 343, "column": 20, "nodeType": "249", "messageId": "250", "endLine": 343, "endColumn": 31}, {"ruleId": "247", "severity": 1, "message": "303", "line": 420, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 420, "endColumn": 25}, {"ruleId": "247", "severity": 1, "message": "304", "line": 420, "column": 27, "nodeType": "249", "messageId": "250", "endLine": 420, "endColumn": 45}, {"ruleId": "247", "severity": 1, "message": "305", "line": 422, "column": 25, "nodeType": "249", "messageId": "250", "endLine": 422, "endColumn": 41}, {"ruleId": "247", "severity": 1, "message": "306", "line": 493, "column": 9, "nodeType": "249", "messageId": "250", "endLine": 493, "endColumn": 30}, {"ruleId": "267", "severity": 1, "message": "307", "line": 806, "column": 6, "nodeType": "269", "endLine": 806, "endColumn": 8, "suggestions": "308"}, {"ruleId": "267", "severity": 1, "message": "309", "line": 813, "column": 6, "nodeType": "269", "endLine": 813, "endColumn": 28, "suggestions": "310"}, {"ruleId": "267", "severity": 1, "message": "311", "line": 846, "column": 6, "nodeType": "269", "endLine": 846, "endColumn": 91, "suggestions": "312"}, {"ruleId": "247", "severity": 1, "message": "313", "line": 849, "column": 9, "nodeType": "249", "messageId": "250", "endLine": 849, "endColumn": 23}, {"ruleId": "247", "severity": 1, "message": "314", "line": 958, "column": 9, "nodeType": "249", "messageId": "250", "endLine": 958, "endColumn": 28}, {"ruleId": "247", "severity": 1, "message": "315", "line": 977, "column": 11, "nodeType": "249", "messageId": "250", "endLine": 977, "endColumn": 30}, {"ruleId": "247", "severity": 1, "message": "316", "line": 1267, "column": 11, "nodeType": "249", "messageId": "250", "endLine": 1267, "endColumn": 24}, {"ruleId": "267", "severity": 1, "message": "317", "line": 1459, "column": 59, "nodeType": "249", "endLine": 1459, "endColumn": 66}, {"ruleId": "267", "severity": 1, "message": "318", "line": 1807, "column": 6, "nodeType": "269", "endLine": 1807, "endColumn": 134, "suggestions": "319"}, {"ruleId": "247", "severity": 1, "message": "320", "line": 31, "column": 3, "nodeType": "249", "messageId": "250", "endLine": 31, "endColumn": 15}, {"ruleId": "247", "severity": 1, "message": "321", "line": 52, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 52, "endColumn": 28}, {"ruleId": "247", "severity": 1, "message": "322", "line": 9, "column": 3, "nodeType": "249", "messageId": "250", "endLine": 9, "endColumn": 14}, {"ruleId": "267", "severity": 1, "message": "323", "line": 44, "column": 6, "nodeType": "269", "endLine": 44, "endColumn": 51, "suggestions": "324"}, {"ruleId": "247", "severity": 1, "message": "325", "line": 4, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 4, "endColumn": 21}, {"ruleId": "247", "severity": 1, "message": "326", "line": 5, "column": 3, "nodeType": "249", "messageId": "250", "endLine": 5, "endColumn": 19}, {"ruleId": "267", "severity": 1, "message": "317", "line": 94, "column": 59, "nodeType": "249", "endLine": 94, "endColumn": 66}, {"ruleId": "247", "severity": 1, "message": "327", "line": 3, "column": 31, "nodeType": "249", "messageId": "250", "endLine": 3, "endColumn": 36}, {"ruleId": "247", "severity": 1, "message": "328", "line": 4, "column": 52, "nodeType": "249", "messageId": "250", "endLine": 4, "endColumn": 66}, {"ruleId": "247", "severity": 1, "message": "329", "line": 11, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 11, "endColumn": 23}, {"ruleId": "247", "severity": 1, "message": "257", "line": 13, "column": 9, "nodeType": "249", "messageId": "250", "endLine": 13, "endColumn": 26}, {"ruleId": "247", "severity": 1, "message": "330", "line": 228, "column": 9, "nodeType": "249", "messageId": "250", "endLine": 228, "endColumn": 26}, {"ruleId": "247", "severity": 1, "message": "331", "line": 2, "column": 63, "nodeType": "249", "messageId": "250", "endLine": 2, "endColumn": 70}, {"ruleId": "247", "severity": 1, "message": "332", "line": 3, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 3, "endColumn": 22}, {"ruleId": "247", "severity": 1, "message": "333", "line": 3, "column": 24, "nodeType": "249", "messageId": "250", "endLine": 3, "endColumn": 42}, {"ruleId": "247", "severity": 1, "message": "334", "line": 6, "column": 9, "nodeType": "249", "messageId": "250", "endLine": 6, "endColumn": 15}, {"ruleId": "247", "severity": 1, "message": "262", "line": 7, "column": 15, "nodeType": "249", "messageId": "250", "endLine": 7, "endColumn": 20}, {"ruleId": "247", "severity": 1, "message": "335", "line": 63, "column": 10, "nodeType": "249", "messageId": "250", "endLine": 63, "endColumn": 21}, "@typescript-eslint/no-unused-vars", "'SearchOutlined' is defined but never used.", "Identifier", "unusedVar", "'BookOutlined' is defined but never used.", "'Footer' is assigned a value but never used.", "'Card' is defined but never used.", "'ConnectionLineType' is defined but never used.", "'NodeData' is defined but never used.", "'Search' is assigned a value but never used.", "'reactFlowInstance' is assigned a value but never used.", "'Space' is defined but never used.", "'ReloadOutlined' is defined but never used.", "'addEdge' is defined but never used.", "'MarkerType' is defined but never used.", "'Title' is assigned a value but never used.", "'creatingRelationship' is assigned a value but never used.", "'setCreatingRelationship' is assigned a value but never used.", "'analyzeAndDisplayRelationships' is assigned a value but never used.", "'table' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'deleteCurrentEdge' and 'handleDeleteRelationship'. Either include them or remove the dependency array.", "ArrayExpression", ["336"], "React Hook useEffect has missing dependencies: 'handleDeleteRelationship', 'nodes', and 'removeNodeAndRelatedEdges'. Either include them or remove the dependency array.", ["337"], "'handleRefreshSchema' is assigned a value but never used.", "'rows' is assigned a value but never used.", "'viewMode' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchValueMappings'. Either include it or remove the dependency array.", ["338"], "'ReactDOM' is defined but never used.", "'Link' is defined but never used.", "'TabPanel' is defined but never used.", "'Tabs' is defined but never used.", "'AnalysisTab' is defined but never used.", "'SQLTab' is defined but never used.", "'VisualizationTab' is defined but never used.", "'ControlPanel' is defined but never used.", "'TimelineChat' is defined but never used.", "'OutputFormatter' is defined but never used.", "'hybridQAService' is defined but never used.", "'enhancedText2SQLService' is defined but never used.", "'Switch' is defined but never used.", "'Brain' is assigned a value but never used.", "'Database' is assigned a value but never used.", "'Bar<PERSON>hart' is assigned a value but never used.", "'FileText' is assigned a value but never used.", "'Code' is assigned a value but never used.", "'CodeIcon' is assigned a value but never used.", "'AlertCircle' is assigned a value but never used.", "'WifiIcon' is assigned a value but never used.", "'WebSocketStatusIndicator' is assigned a value but never used.", "'processingSteps' is assigned a value but never used.", "'setPageSize' is assigned a value but never used.", "'similarExamples' is assigned a value but never used.", "'setSimilarExamples' is assigned a value but never used.", "'setSchemaContext' is assigned a value but never used.", "'updateTimelineMessage' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'createNewChatSession' and 'loadChatHistories'. Either include them or remove the dependency array.", ["339"], "React Hook useEffect has a missing dependency: 'loadChatHistories'. Either include it or remove the dependency array.", ["340"], "React Hook useEffect has missing dependencies: 'loadChatHistories' and 'saveChatHistory'. Either include them or remove the dependency array.", ["341"], "'toggleCollapse' is assigned a value but never used.", "'handleFinalAnalysis' is assigned a value but never used.", "'allRegionsCompleted' is assigned a value but never used.", "'userMessageId' is assigned a value but never used.", "The ref value 'chartRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'chartRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has a missing dependency: 'scrollAnalysisAreaToBottom'. Either include it or remove the dependency array.", ["342"], "'EditOutlined' is defined but never used.", "'searchModalVisible' is assigned a value but never used.", "'EyeOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'searchExamples'. Either include it or remove the dependency array.", ["343"], "'ChatHistory' is defined but never used.", "'DownloadOutlined' is defined but never used.", "'Badge' is defined but never used.", "'DeleteOutlined' is defined but never used.", "'activeHandles' is assigned a value but never used.", "'handleDeleteClick' is assigned a value but never used.", "'Divider' is defined but never used.", "'LinkOutlined' is defined but never used.", "'ArrowRightOutlined' is defined but never used.", "'Option' is assigned a value but never used.", "'tipsLoading' is assigned a value but never used.", {"desc": "344", "fix": "345"}, {"desc": "346", "fix": "347"}, {"desc": "348", "fix": "349"}, {"desc": "350", "fix": "351"}, {"desc": "352", "fix": "353"}, {"desc": "354", "fix": "355"}, {"desc": "356", "fix": "357"}, {"desc": "358", "fix": "359"}, "Update the dependencies array to be: [deleteCurrentEdge, handleDeleteRelationship, nodes]", {"range": "360", "text": "361"}, "Update the dependencies array to be: [handleDeleteRelationship, nodes, removeNodeAndRelatedEdges]", {"range": "362", "text": "363"}, "Update the dependencies array to be: [fetchValueMappings, selectedColumn]", {"range": "364", "text": "365"}, "Update the dependencies array to be: [createNewChatSession, loadChatHistories]", {"range": "366", "text": "367"}, "Update the dependencies array to be: [loadChatHistories, selectedConnectionId]", {"range": "368", "text": "369"}, "Update the dependencies array to be: [loading, currentSessionId, sqlResult, dataResult, timelineMessages, savedSessionIds, saveChatHistory, loadChatHistories]", {"range": "370", "text": "371"}, "Update the dependencies array to be: [regionOutputs.analysis.hasContent, regionOutputs.analysis.merged, regionOutputs.analysis.streaming, collapsedSections.analysis, scrollAnalysisAreaToBottom]", {"range": "372", "text": "373"}, "Update the dependencies array to be: [visible, query, connectionId, schemaContext, searchExamples]", {"range": "374", "text": "375"}, [29282, 29289], "[deleteCurrentEdge, handleDeleteRelationship, nodes]", [46980, 46982], "[handleDeleteRelationship, nodes, removeNodeAndRelatedEdges]", [4934, 4950], "[fetch<PERSON><PERSON>ue<PERSON>apping<PERSON>, selectedColumn]", [22652, 22654], "[createNewChatSession, loadChatHistories]", [22787, 22809], "[loadChatHistories, selectedConnectionId]", [23791, 23876], "[loading, currentSessionId, sqlResult, dataResult, timelineMessages, savedSessionIds, saveChatHistory, loadChatHistories]", [50226, 50354], "[regionOutputs.analysis.hasContent, regionOutputs.analysis.merged, regionOutputs.analysis.streaming, collapsedSections.analysis, scrollAnalysisAreaToBottom]", [1153, 1198], "[visible, query, connectionId, schemaContext, searchExamples]"]