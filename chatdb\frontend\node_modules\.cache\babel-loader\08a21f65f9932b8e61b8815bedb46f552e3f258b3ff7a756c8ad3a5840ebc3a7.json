{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * 错误监控和自动回退系统\n */\n\nimport { featureFlags } from './featureFlags';\nclass ErrorMonitoringService {\n  constructor() {\n    this.errorThreshold = 3;\n    // 错误阈值\n    this.timeWindow = 5 * 60 * 1000;\n    // 5分钟时间窗口\n    this.componentHealth = new Map();\n    this.sessionId = void 0;\n    this.errorReports = [];\n    this.sessionId = this.generateSessionId();\n    this.setupGlobalErrorHandlers();\n    this.loadHealthData();\n  }\n  generateSessionId() {\n    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n  setupGlobalErrorHandlers() {\n    // 捕获未处理的JavaScript错误\n    window.addEventListener('error', event => {\n      var _event$error;\n      this.reportError({\n        component: 'Global',\n        feature: 'useDesignSystem',\n        // 默认归类\n        error: {\n          message: event.message,\n          stack: (_event$error = event.error) === null || _event$error === void 0 ? void 0 : _event$error.stack\n        }\n      });\n    });\n\n    // 捕获未处理的Promise拒绝\n    window.addEventListener('unhandledrejection', event => {\n      var _event$reason, _event$reason2;\n      this.reportError({\n        component: 'Promise',\n        feature: 'useDesignSystem',\n        error: {\n          message: ((_event$reason = event.reason) === null || _event$reason === void 0 ? void 0 : _event$reason.message) || String(event.reason),\n          stack: (_event$reason2 = event.reason) === null || _event$reason2 === void 0 ? void 0 : _event$reason2.stack\n        }\n      });\n    });\n  }\n  loadHealthData() {\n    try {\n      const saved = localStorage.getItem('ux-component-health');\n      if (saved) {\n        const data = JSON.parse(saved);\n        Object.entries(data).forEach(([feature, health]) => {\n          this.componentHealth.set(feature, health);\n        });\n      }\n    } catch (error) {\n      console.warn('Failed to load component health data:', error);\n    }\n  }\n  saveHealthData() {\n    try {\n      const data = Object.fromEntries(this.componentHealth);\n      localStorage.setItem('ux-component-health', JSON.stringify(data));\n    } catch (error) {\n      console.warn('Failed to save component health data:', error);\n    }\n  }\n\n  // 报告组件错误\n  reportError(params) {\n    if (!featureFlags.isEnabled('enableErrorReporting')) {\n      return;\n    }\n    const errorReport = {\n      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      timestamp: Date.now(),\n      component: params.component,\n      feature: params.feature,\n      error: params.error,\n      userAgent: navigator.userAgent,\n      url: window.location.href,\n      userId: params.userId,\n      sessionId: this.sessionId\n    };\n    this.errorReports.push(errorReport);\n    this.updateComponentHealth(params.feature);\n    this.checkAutoDisable(params.feature);\n\n    // 发送错误报告到服务器（如果配置了）\n    this.sendErrorReport(errorReport);\n    if (featureFlags.isEnabled('enableDebugMode')) {\n      console.error('UX Component Error:', errorReport);\n    }\n  }\n  updateComponentHealth(feature) {\n    const health = this.componentHealth.get(feature) || {\n      feature,\n      errorCount: 0,\n      isHealthy: true,\n      autoDisabled: false\n    };\n    health.errorCount++;\n    health.lastError = Date.now();\n    health.isHealthy = health.errorCount < this.errorThreshold;\n    this.componentHealth.set(feature, health);\n    this.saveHealthData();\n  }\n  checkAutoDisable(feature) {\n    const health = this.componentHealth.get(feature);\n    if (!health) return;\n\n    // 检查是否在时间窗口内超过错误阈值\n    const recentErrors = this.errorReports.filter(report => report.feature === feature && Date.now() - report.timestamp < this.timeWindow);\n    if (recentErrors.length >= this.errorThreshold && !health.autoDisabled) {\n      console.warn(`Auto-disabling feature ${feature} due to excessive errors`);\n      health.autoDisabled = true;\n      health.isHealthy = false;\n      this.componentHealth.set(feature, health);\n\n      // 自动禁用功能\n      featureFlags.setFlag(feature, false);\n\n      // 显示用户通知\n      this.showUserNotification(feature);\n      this.saveHealthData();\n    }\n  }\n  showUserNotification(feature) {\n    // 创建一个简单的通知\n    const notification = document.createElement('div');\n    notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: #ff4d4f;\n      color: white;\n      padding: 12px 16px;\n      border-radius: 6px;\n      box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n      z-index: 10000;\n      font-size: 14px;\n      max-width: 300px;\n    `;\n    notification.innerHTML = `\n      <strong>功能已自动禁用</strong><br>\n      由于检测到多个错误，${feature} 功能已被暂时禁用。\n      <button onclick=\"this.parentElement.remove()\" style=\"\n        background: none;\n        border: none;\n        color: white;\n        float: right;\n        cursor: pointer;\n        font-size: 16px;\n        margin-top: -2px;\n      \">×</button>\n    `;\n    document.body.appendChild(notification);\n\n    // 5秒后自动移除\n    setTimeout(() => {\n      if (notification.parentElement) {\n        notification.remove();\n      }\n    }, 5000);\n  }\n  async sendErrorReport(report) {\n    try {\n      // 这里可以发送到你的错误收集服务\n      // 例如：Sentry, LogRocket, 或自定义API\n\n      if (process.env.NODE_ENV === 'development') {\n        console.log('Error report (dev mode):', report);\n        return;\n      }\n\n      // 示例：发送到自定义API\n      // await fetch('/api/errors', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify(report),\n      // });\n    } catch (error) {\n      console.warn('Failed to send error report:', error);\n    }\n  }\n\n  // 获取组件健康状态\n  getComponentHealth(feature) {\n    return this.componentHealth.get(feature) || null;\n  }\n\n  // 获取所有组件健康状态\n  getAllComponentHealth() {\n    return Array.from(this.componentHealth.values());\n  }\n\n  // 重置组件健康状态\n  resetComponentHealth(feature) {\n    const health = this.componentHealth.get(feature);\n    if (health) {\n      health.errorCount = 0;\n      health.isHealthy = true;\n      health.autoDisabled = false;\n      health.lastError = undefined;\n      this.componentHealth.set(feature, health);\n      this.saveHealthData();\n      console.log(`Component health reset for ${feature}`);\n    }\n  }\n\n  // 重置所有组件健康状态\n  resetAllComponentHealth() {\n    this.componentHealth.clear();\n    this.errorReports = [];\n    localStorage.removeItem('ux-component-health');\n    console.log('All component health data reset');\n  }\n\n  // 手动恢复功能\n  recoverFeature(feature) {\n    this.resetComponentHealth(feature);\n    featureFlags.setFlag(feature, true);\n    console.log(`Feature ${feature} manually recovered`);\n  }\n\n  // 获取错误统计\n  getErrorStats() {\n    const now = Date.now();\n    const recentErrors = this.errorReports.filter(report => now - report.timestamp < this.timeWindow);\n    const errorsByFeature = this.errorReports.reduce((acc, report) => {\n      acc[report.feature] = (acc[report.feature] || 0) + 1;\n      return acc;\n    }, {});\n    return {\n      totalErrors: this.errorReports.length,\n      errorsByFeature,\n      recentErrors: recentErrors.length\n    };\n  }\n\n  // 清理旧的错误报告\n  cleanupOldErrors() {\n    const cutoff = Date.now() - 24 * 60 * 60 * 1000; // 24小时前\n    this.errorReports = this.errorReports.filter(report => report.timestamp > cutoff);\n  }\n}\n\n// 全局实例\nexport const errorMonitoring = new ErrorMonitoringService();\n\n// React Hook for error reporting\nimport { useCallback } from 'react';\nexport function useErrorReporting() {\n  _s();\n  const reportError = useCallback((component, feature, error, userId) => {\n    errorMonitoring.reportError({\n      component,\n      feature,\n      error: {\n        message: error.message,\n        stack: error.stack\n      },\n      userId\n    });\n  }, []);\n  return {\n    reportError\n  };\n}\n\n// 开发者工具\n_s(useErrorReporting, \"DPt0zzRcNhTHMQiurvRHsg9EyMk=\");\nif (process.env.NODE_ENV === 'development') {\n  window.errorMonitoring = errorMonitoring;\n  console.log('Error monitoring service available at window.errorMonitoring');\n}", "map": {"version": 3, "names": ["featureFlags", "ErrorMonitoringService", "constructor", "errorT<PERSON>eshold", "timeWindow", "componentHealth", "Map", "sessionId", "errorReports", "generateSessionId", "setupGlobalErrorHandlers", "loadHealthData", "Date", "now", "Math", "random", "toString", "substr", "window", "addEventListener", "event", "_event$error", "reportError", "component", "feature", "error", "message", "stack", "_event$reason", "_event$reason2", "reason", "String", "saved", "localStorage", "getItem", "data", "JSON", "parse", "Object", "entries", "for<PERSON>ach", "health", "set", "console", "warn", "saveHealthData", "fromEntries", "setItem", "stringify", "params", "isEnabled", "errorReport", "id", "timestamp", "userAgent", "navigator", "url", "location", "href", "userId", "push", "updateComponentHealth", "checkAutoDisable", "sendErrorReport", "get", "errorCount", "is<PERSON><PERSON><PERSON>", "autoDisabled", "lastError", "recentErrors", "filter", "report", "length", "setFlag", "showUserNotification", "notification", "document", "createElement", "style", "cssText", "innerHTML", "body", "append<PERSON><PERSON><PERSON>", "setTimeout", "parentElement", "remove", "process", "env", "NODE_ENV", "log", "getComponentHealth", "getAllComponentHealth", "Array", "from", "values", "resetComponentHealth", "undefined", "resetAllComponentHealth", "clear", "removeItem", "recoverFeature", "getErrorStats", "errorsByFeature", "reduce", "acc", "totalErrors", "cleanupOldErrors", "cutoff", "errorMonitoring", "useCallback", "useErrorReporting", "_s"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/utils/errorMonitoring.ts"], "sourcesContent": ["/**\n * 错误监控和自动回退系统\n */\n\nimport { featureFlags, FeatureFlags } from './featureFlags';\n\ninterface ErrorReport {\n  id: string;\n  timestamp: number;\n  component: string;\n  feature: keyof FeatureFlags;\n  error: {\n    message: string;\n    stack?: string;\n    componentStack?: string;\n  };\n  userAgent: string;\n  url: string;\n  userId?: string;\n  sessionId: string;\n}\n\ninterface ComponentHealth {\n  feature: keyof FeatureFlags;\n  errorCount: number;\n  lastError?: number;\n  isHealthy: boolean;\n  autoDisabled: boolean;\n}\n\nclass ErrorMonitoringService {\n  private errorThreshold = 3; // 错误阈值\n  private timeWindow = 5 * 60 * 1000; // 5分钟时间窗口\n  private componentHealth: Map<keyof FeatureFlags, ComponentHealth> = new Map();\n  private sessionId: string;\n  private errorReports: ErrorReport[] = [];\n\n  constructor() {\n    this.sessionId = this.generateSessionId();\n    this.setupGlobalErrorHandlers();\n    this.loadHealthData();\n  }\n\n  private generateSessionId(): string {\n    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private setupGlobalErrorHandlers(): void {\n    // 捕获未处理的JavaScript错误\n    window.addEventListener('error', (event) => {\n      this.reportError({\n        component: 'Global',\n        feature: 'useDesignSystem', // 默认归类\n        error: {\n          message: event.message,\n          stack: event.error?.stack,\n        },\n      });\n    });\n\n    // 捕获未处理的Promise拒绝\n    window.addEventListener('unhandledrejection', (event) => {\n      this.reportError({\n        component: 'Promise',\n        feature: 'useDesignSystem',\n        error: {\n          message: event.reason?.message || String(event.reason),\n          stack: event.reason?.stack,\n        },\n      });\n    });\n  }\n\n  private loadHealthData(): void {\n    try {\n      const saved = localStorage.getItem('ux-component-health');\n      if (saved) {\n        const data = JSON.parse(saved);\n        Object.entries(data).forEach(([feature, health]) => {\n          this.componentHealth.set(feature as keyof FeatureFlags, health as ComponentHealth);\n        });\n      }\n    } catch (error) {\n      console.warn('Failed to load component health data:', error);\n    }\n  }\n\n  private saveHealthData(): void {\n    try {\n      const data = Object.fromEntries(this.componentHealth);\n      localStorage.setItem('ux-component-health', JSON.stringify(data));\n    } catch (error) {\n      console.warn('Failed to save component health data:', error);\n    }\n  }\n\n  // 报告组件错误\n  reportError(params: {\n    component: string;\n    feature: keyof FeatureFlags;\n    error: {\n      message: string;\n      stack?: string;\n      componentStack?: string;\n    };\n    userId?: string;\n  }): void {\n    if (!featureFlags.isEnabled('enableErrorReporting')) {\n      return;\n    }\n\n    const errorReport: ErrorReport = {\n      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      timestamp: Date.now(),\n      component: params.component,\n      feature: params.feature,\n      error: params.error,\n      userAgent: navigator.userAgent,\n      url: window.location.href,\n      userId: params.userId,\n      sessionId: this.sessionId,\n    };\n\n    this.errorReports.push(errorReport);\n    this.updateComponentHealth(params.feature);\n    this.checkAutoDisable(params.feature);\n\n    // 发送错误报告到服务器（如果配置了）\n    this.sendErrorReport(errorReport);\n\n    if (featureFlags.isEnabled('enableDebugMode')) {\n      console.error('UX Component Error:', errorReport);\n    }\n  }\n\n  private updateComponentHealth(feature: keyof FeatureFlags): void {\n    const health = this.componentHealth.get(feature) || {\n      feature,\n      errorCount: 0,\n      isHealthy: true,\n      autoDisabled: false,\n    };\n\n    health.errorCount++;\n    health.lastError = Date.now();\n    health.isHealthy = health.errorCount < this.errorThreshold;\n\n    this.componentHealth.set(feature, health);\n    this.saveHealthData();\n  }\n\n  private checkAutoDisable(feature: keyof FeatureFlags): void {\n    const health = this.componentHealth.get(feature);\n    if (!health) return;\n\n    // 检查是否在时间窗口内超过错误阈值\n    const recentErrors = this.errorReports.filter(\n      report => \n        report.feature === feature && \n        Date.now() - report.timestamp < this.timeWindow\n    );\n\n    if (recentErrors.length >= this.errorThreshold && !health.autoDisabled) {\n      console.warn(`Auto-disabling feature ${feature} due to excessive errors`);\n      \n      health.autoDisabled = true;\n      health.isHealthy = false;\n      this.componentHealth.set(feature, health);\n      \n      // 自动禁用功能\n      featureFlags.setFlag(feature, false);\n      \n      // 显示用户通知\n      this.showUserNotification(feature);\n      \n      this.saveHealthData();\n    }\n  }\n\n  private showUserNotification(feature: keyof FeatureFlags): void {\n    // 创建一个简单的通知\n    const notification = document.createElement('div');\n    notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: #ff4d4f;\n      color: white;\n      padding: 12px 16px;\n      border-radius: 6px;\n      box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n      z-index: 10000;\n      font-size: 14px;\n      max-width: 300px;\n    `;\n    notification.innerHTML = `\n      <strong>功能已自动禁用</strong><br>\n      由于检测到多个错误，${feature} 功能已被暂时禁用。\n      <button onclick=\"this.parentElement.remove()\" style=\"\n        background: none;\n        border: none;\n        color: white;\n        float: right;\n        cursor: pointer;\n        font-size: 16px;\n        margin-top: -2px;\n      \">×</button>\n    `;\n\n    document.body.appendChild(notification);\n\n    // 5秒后自动移除\n    setTimeout(() => {\n      if (notification.parentElement) {\n        notification.remove();\n      }\n    }, 5000);\n  }\n\n  private async sendErrorReport(report: ErrorReport): Promise<void> {\n    try {\n      // 这里可以发送到你的错误收集服务\n      // 例如：Sentry, LogRocket, 或自定义API\n      \n      if (process.env.NODE_ENV === 'development') {\n        console.log('Error report (dev mode):', report);\n        return;\n      }\n\n      // 示例：发送到自定义API\n      // await fetch('/api/errors', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify(report),\n      // });\n      \n    } catch (error) {\n      console.warn('Failed to send error report:', error);\n    }\n  }\n\n  // 获取组件健康状态\n  getComponentHealth(feature: keyof FeatureFlags): ComponentHealth | null {\n    return this.componentHealth.get(feature) || null;\n  }\n\n  // 获取所有组件健康状态\n  getAllComponentHealth(): ComponentHealth[] {\n    return Array.from(this.componentHealth.values());\n  }\n\n  // 重置组件健康状态\n  resetComponentHealth(feature: keyof FeatureFlags): void {\n    const health = this.componentHealth.get(feature);\n    if (health) {\n      health.errorCount = 0;\n      health.isHealthy = true;\n      health.autoDisabled = false;\n      health.lastError = undefined;\n      this.componentHealth.set(feature, health);\n      this.saveHealthData();\n      \n      console.log(`Component health reset for ${feature}`);\n    }\n  }\n\n  // 重置所有组件健康状态\n  resetAllComponentHealth(): void {\n    this.componentHealth.clear();\n    this.errorReports = [];\n    localStorage.removeItem('ux-component-health');\n    console.log('All component health data reset');\n  }\n\n  // 手动恢复功能\n  recoverFeature(feature: keyof FeatureFlags): void {\n    this.resetComponentHealth(feature);\n    featureFlags.setFlag(feature, true);\n    console.log(`Feature ${feature} manually recovered`);\n  }\n\n  // 获取错误统计\n  getErrorStats(): {\n    totalErrors: number;\n    errorsByFeature: Record<string, number>;\n    recentErrors: number;\n  } {\n    const now = Date.now();\n    const recentErrors = this.errorReports.filter(\n      report => now - report.timestamp < this.timeWindow\n    );\n\n    const errorsByFeature = this.errorReports.reduce((acc, report) => {\n      acc[report.feature] = (acc[report.feature] || 0) + 1;\n      return acc;\n    }, {} as Record<string, number>);\n\n    return {\n      totalErrors: this.errorReports.length,\n      errorsByFeature,\n      recentErrors: recentErrors.length,\n    };\n  }\n\n  // 清理旧的错误报告\n  cleanupOldErrors(): void {\n    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24小时前\n    this.errorReports = this.errorReports.filter(\n      report => report.timestamp > cutoff\n    );\n  }\n}\n\n// 全局实例\nexport const errorMonitoring = new ErrorMonitoringService();\n\n// React Hook for error reporting\nimport { useCallback } from 'react';\n\nexport function useErrorReporting() {\n  const reportError = useCallback((\n    component: string,\n    feature: keyof FeatureFlags,\n    error: Error,\n    userId?: string\n  ) => {\n    errorMonitoring.reportError({\n      component,\n      feature,\n      error: {\n        message: error.message,\n        stack: error.stack,\n      },\n      userId,\n    });\n  }, []);\n\n  return { reportError };\n}\n\n// 开发者工具\nif (process.env.NODE_ENV === 'development') {\n  (window as any).errorMonitoring = errorMonitoring;\n  console.log('Error monitoring service available at window.errorMonitoring');\n}\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,SAASA,YAAY,QAAsB,gBAAgB;AA0B3D,MAAMC,sBAAsB,CAAC;EAO3BC,WAAWA,CAAA,EAAG;IAAA,KANNC,cAAc,GAAG,CAAC;IAAE;IAAA,KACpBC,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;IAAE;IAAA,KAC5BC,eAAe,GAA6C,IAAIC,GAAG,CAAC,CAAC;IAAA,KACrEC,SAAS;IAAA,KACTC,YAAY,GAAkB,EAAE;IAGtC,IAAI,CAACD,SAAS,GAAG,IAAI,CAACE,iBAAiB,CAAC,CAAC;IACzC,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB;EAEQF,iBAAiBA,CAAA,EAAW;IAClC,OAAO,WAAWG,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EAC3E;EAEQP,wBAAwBA,CAAA,EAAS;IACvC;IACAQ,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;MAAA,IAAAC,YAAA;MAC1C,IAAI,CAACC,WAAW,CAAC;QACfC,SAAS,EAAE,QAAQ;QACnBC,OAAO,EAAE,iBAAiB;QAAE;QAC5BC,KAAK,EAAE;UACLC,OAAO,EAAEN,KAAK,CAACM,OAAO;UACtBC,KAAK,GAAAN,YAAA,GAAED,KAAK,CAACK,KAAK,cAAAJ,YAAA,uBAAXA,YAAA,CAAaM;QACtB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAT,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAGC,KAAK,IAAK;MAAA,IAAAQ,aAAA,EAAAC,cAAA;MACvD,IAAI,CAACP,WAAW,CAAC;QACfC,SAAS,EAAE,SAAS;QACpBC,OAAO,EAAE,iBAAiB;QAC1BC,KAAK,EAAE;UACLC,OAAO,EAAE,EAAAE,aAAA,GAAAR,KAAK,CAACU,MAAM,cAAAF,aAAA,uBAAZA,aAAA,CAAcF,OAAO,KAAIK,MAAM,CAACX,KAAK,CAACU,MAAM,CAAC;UACtDH,KAAK,GAAAE,cAAA,GAAET,KAAK,CAACU,MAAM,cAAAD,cAAA,uBAAZA,cAAA,CAAcF;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEQhB,cAAcA,CAAA,EAAS;IAC7B,IAAI;MACF,MAAMqB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC;MACzD,IAAIF,KAAK,EAAE;QACT,MAAMG,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,KAAK,CAAC;QAC9BM,MAAM,CAACC,OAAO,CAACJ,IAAI,CAAC,CAACK,OAAO,CAAC,CAAC,CAAChB,OAAO,EAAEiB,MAAM,CAAC,KAAK;UAClD,IAAI,CAACpC,eAAe,CAACqC,GAAG,CAAClB,OAAO,EAAwBiB,MAAyB,CAAC;QACpF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdkB,OAAO,CAACC,IAAI,CAAC,uCAAuC,EAAEnB,KAAK,CAAC;IAC9D;EACF;EAEQoB,cAAcA,CAAA,EAAS;IAC7B,IAAI;MACF,MAAMV,IAAI,GAAGG,MAAM,CAACQ,WAAW,CAAC,IAAI,CAACzC,eAAe,CAAC;MACrD4B,YAAY,CAACc,OAAO,CAAC,qBAAqB,EAAEX,IAAI,CAACY,SAAS,CAACb,IAAI,CAAC,CAAC;IACnE,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdkB,OAAO,CAACC,IAAI,CAAC,uCAAuC,EAAEnB,KAAK,CAAC;IAC9D;EACF;;EAEA;EACAH,WAAWA,CAAC2B,MASX,EAAQ;IACP,IAAI,CAACjD,YAAY,CAACkD,SAAS,CAAC,sBAAsB,CAAC,EAAE;MACnD;IACF;IAEA,MAAMC,WAAwB,GAAG;MAC/BC,EAAE,EAAE,SAASxC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACpEoC,SAAS,EAAEzC,IAAI,CAACC,GAAG,CAAC,CAAC;MACrBU,SAAS,EAAE0B,MAAM,CAAC1B,SAAS;MAC3BC,OAAO,EAAEyB,MAAM,CAACzB,OAAO;MACvBC,KAAK,EAAEwB,MAAM,CAACxB,KAAK;MACnB6B,SAAS,EAAEC,SAAS,CAACD,SAAS;MAC9BE,GAAG,EAAEtC,MAAM,CAACuC,QAAQ,CAACC,IAAI;MACzBC,MAAM,EAAEV,MAAM,CAACU,MAAM;MACrBpD,SAAS,EAAE,IAAI,CAACA;IAClB,CAAC;IAED,IAAI,CAACC,YAAY,CAACoD,IAAI,CAACT,WAAW,CAAC;IACnC,IAAI,CAACU,qBAAqB,CAACZ,MAAM,CAACzB,OAAO,CAAC;IAC1C,IAAI,CAACsC,gBAAgB,CAACb,MAAM,CAACzB,OAAO,CAAC;;IAErC;IACA,IAAI,CAACuC,eAAe,CAACZ,WAAW,CAAC;IAEjC,IAAInD,YAAY,CAACkD,SAAS,CAAC,iBAAiB,CAAC,EAAE;MAC7CP,OAAO,CAAClB,KAAK,CAAC,qBAAqB,EAAE0B,WAAW,CAAC;IACnD;EACF;EAEQU,qBAAqBA,CAACrC,OAA2B,EAAQ;IAC/D,MAAMiB,MAAM,GAAG,IAAI,CAACpC,eAAe,CAAC2D,GAAG,CAACxC,OAAO,CAAC,IAAI;MAClDA,OAAO;MACPyC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE;IAChB,CAAC;IAED1B,MAAM,CAACwB,UAAU,EAAE;IACnBxB,MAAM,CAAC2B,SAAS,GAAGxD,IAAI,CAACC,GAAG,CAAC,CAAC;IAC7B4B,MAAM,CAACyB,SAAS,GAAGzB,MAAM,CAACwB,UAAU,GAAG,IAAI,CAAC9D,cAAc;IAE1D,IAAI,CAACE,eAAe,CAACqC,GAAG,CAAClB,OAAO,EAAEiB,MAAM,CAAC;IACzC,IAAI,CAACI,cAAc,CAAC,CAAC;EACvB;EAEQiB,gBAAgBA,CAACtC,OAA2B,EAAQ;IAC1D,MAAMiB,MAAM,GAAG,IAAI,CAACpC,eAAe,CAAC2D,GAAG,CAACxC,OAAO,CAAC;IAChD,IAAI,CAACiB,MAAM,EAAE;;IAEb;IACA,MAAM4B,YAAY,GAAG,IAAI,CAAC7D,YAAY,CAAC8D,MAAM,CAC3CC,MAAM,IACJA,MAAM,CAAC/C,OAAO,KAAKA,OAAO,IAC1BZ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG0D,MAAM,CAAClB,SAAS,GAAG,IAAI,CAACjD,UACzC,CAAC;IAED,IAAIiE,YAAY,CAACG,MAAM,IAAI,IAAI,CAACrE,cAAc,IAAI,CAACsC,MAAM,CAAC0B,YAAY,EAAE;MACtExB,OAAO,CAACC,IAAI,CAAC,0BAA0BpB,OAAO,0BAA0B,CAAC;MAEzEiB,MAAM,CAAC0B,YAAY,GAAG,IAAI;MAC1B1B,MAAM,CAACyB,SAAS,GAAG,KAAK;MACxB,IAAI,CAAC7D,eAAe,CAACqC,GAAG,CAAClB,OAAO,EAAEiB,MAAM,CAAC;;MAEzC;MACAzC,YAAY,CAACyE,OAAO,CAACjD,OAAO,EAAE,KAAK,CAAC;;MAEpC;MACA,IAAI,CAACkD,oBAAoB,CAAClD,OAAO,CAAC;MAElC,IAAI,CAACqB,cAAc,CAAC,CAAC;IACvB;EACF;EAEQ6B,oBAAoBA,CAAClD,OAA2B,EAAQ;IAC9D;IACA,MAAMmD,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDF,YAAY,CAACG,KAAK,CAACC,OAAO,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IACDJ,YAAY,CAACK,SAAS,GAAG;AAC7B;AACA,kBAAkBxD,OAAO;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IAEDoD,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,YAAY,CAAC;;IAEvC;IACAQ,UAAU,CAAC,MAAM;MACf,IAAIR,YAAY,CAACS,aAAa,EAAE;QAC9BT,YAAY,CAACU,MAAM,CAAC,CAAC;MACvB;IACF,CAAC,EAAE,IAAI,CAAC;EACV;EAEA,MAActB,eAAeA,CAACQ,MAAmB,EAAiB;IAChE,IAAI;MACF;MACA;;MAEA,IAAIe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QAC1C7C,OAAO,CAAC8C,GAAG,CAAC,0BAA0B,EAAElB,MAAM,CAAC;QAC/C;MACF;;MAEA;MACA;MACA;MACA;MACA;MACA;IAEF,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACdkB,OAAO,CAACC,IAAI,CAAC,8BAA8B,EAAEnB,KAAK,CAAC;IACrD;EACF;;EAEA;EACAiE,kBAAkBA,CAAClE,OAA2B,EAA0B;IACtE,OAAO,IAAI,CAACnB,eAAe,CAAC2D,GAAG,CAACxC,OAAO,CAAC,IAAI,IAAI;EAClD;;EAEA;EACAmE,qBAAqBA,CAAA,EAAsB;IACzC,OAAOC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACxF,eAAe,CAACyF,MAAM,CAAC,CAAC,CAAC;EAClD;;EAEA;EACAC,oBAAoBA,CAACvE,OAA2B,EAAQ;IACtD,MAAMiB,MAAM,GAAG,IAAI,CAACpC,eAAe,CAAC2D,GAAG,CAACxC,OAAO,CAAC;IAChD,IAAIiB,MAAM,EAAE;MACVA,MAAM,CAACwB,UAAU,GAAG,CAAC;MACrBxB,MAAM,CAACyB,SAAS,GAAG,IAAI;MACvBzB,MAAM,CAAC0B,YAAY,GAAG,KAAK;MAC3B1B,MAAM,CAAC2B,SAAS,GAAG4B,SAAS;MAC5B,IAAI,CAAC3F,eAAe,CAACqC,GAAG,CAAClB,OAAO,EAAEiB,MAAM,CAAC;MACzC,IAAI,CAACI,cAAc,CAAC,CAAC;MAErBF,OAAO,CAAC8C,GAAG,CAAC,8BAA8BjE,OAAO,EAAE,CAAC;IACtD;EACF;;EAEA;EACAyE,uBAAuBA,CAAA,EAAS;IAC9B,IAAI,CAAC5F,eAAe,CAAC6F,KAAK,CAAC,CAAC;IAC5B,IAAI,CAAC1F,YAAY,GAAG,EAAE;IACtByB,YAAY,CAACkE,UAAU,CAAC,qBAAqB,CAAC;IAC9CxD,OAAO,CAAC8C,GAAG,CAAC,iCAAiC,CAAC;EAChD;;EAEA;EACAW,cAAcA,CAAC5E,OAA2B,EAAQ;IAChD,IAAI,CAACuE,oBAAoB,CAACvE,OAAO,CAAC;IAClCxB,YAAY,CAACyE,OAAO,CAACjD,OAAO,EAAE,IAAI,CAAC;IACnCmB,OAAO,CAAC8C,GAAG,CAAC,WAAWjE,OAAO,qBAAqB,CAAC;EACtD;;EAEA;EACA6E,aAAaA,CAAA,EAIX;IACA,MAAMxF,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;IACtB,MAAMwD,YAAY,GAAG,IAAI,CAAC7D,YAAY,CAAC8D,MAAM,CAC3CC,MAAM,IAAI1D,GAAG,GAAG0D,MAAM,CAAClB,SAAS,GAAG,IAAI,CAACjD,UAC1C,CAAC;IAED,MAAMkG,eAAe,GAAG,IAAI,CAAC9F,YAAY,CAAC+F,MAAM,CAAC,CAACC,GAAG,EAAEjC,MAAM,KAAK;MAChEiC,GAAG,CAACjC,MAAM,CAAC/C,OAAO,CAAC,GAAG,CAACgF,GAAG,CAACjC,MAAM,CAAC/C,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;MACpD,OAAOgF,GAAG;IACZ,CAAC,EAAE,CAAC,CAA2B,CAAC;IAEhC,OAAO;MACLC,WAAW,EAAE,IAAI,CAACjG,YAAY,CAACgE,MAAM;MACrC8B,eAAe;MACfjC,YAAY,EAAEA,YAAY,CAACG;IAC7B,CAAC;EACH;;EAEA;EACAkC,gBAAgBA,CAAA,EAAS;IACvB,MAAMC,MAAM,GAAG/F,IAAI,CAACC,GAAG,CAAC,CAAC,GAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAK,CAAC,CAAC;IACnD,IAAI,CAACL,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC8D,MAAM,CAC1CC,MAAM,IAAIA,MAAM,CAAClB,SAAS,GAAGsD,MAC/B,CAAC;EACH;AACF;;AAEA;AACA,OAAO,MAAMC,eAAe,GAAG,IAAI3G,sBAAsB,CAAC,CAAC;;AAE3D;AACA,SAAS4G,WAAW,QAAQ,OAAO;AAEnC,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAAAC,EAAA;EAClC,MAAMzF,WAAW,GAAGuF,WAAW,CAAC,CAC9BtF,SAAiB,EACjBC,OAA2B,EAC3BC,KAAY,EACZkC,MAAe,KACZ;IACHiD,eAAe,CAACtF,WAAW,CAAC;MAC1BC,SAAS;MACTC,OAAO;MACPC,KAAK,EAAE;QACLC,OAAO,EAAED,KAAK,CAACC,OAAO;QACtBC,KAAK,EAAEF,KAAK,CAACE;MACf,CAAC;MACDgC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IAAErC;EAAY,CAAC;AACxB;;AAEA;AAAAyF,EAAA,CArBgBD,iBAAiB;AAsBjC,IAAIxB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;EACzCtE,MAAM,CAAS0F,eAAe,GAAGA,eAAe;EACjDjE,OAAO,CAAC8C,GAAG,CAAC,8DAA8D,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}