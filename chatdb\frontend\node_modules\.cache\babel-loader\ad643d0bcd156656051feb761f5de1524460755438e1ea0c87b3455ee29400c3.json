{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\pages\\\\text2sql\\\\page.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport '../../styles/Text2SQL.css';\nimport '../../styles/Text2SQLTabs.css';\nimport '../../styles/ChatStyle.css';\nimport '../../styles/HybridExamples.css';\nimport '../../styles/EnterpriseChatStyle.css';\nimport '../../styles/TimelineChatStyle.css';\nimport { closeWebSocketConnection, getWebSocketInstance, WebSocketConnectionState, globalWebSocketState, globalWebSocketError, getConnections } from './api';\n\n// 导入组件\n\nimport UserFeedback from './components/UserFeedback';\nimport ErrorMessage from './components/ErrorMessage';\nimport ConnectionSelector from './components/ConnectionSelector';\nimport HybridExamplesPanel from './components/HybridExamplesPanel';\nimport ChatHistorySidebar from './components/ChatHistorySidebar';\nimport RegionPanel from './components/RegionPanel';\n// 导入工具函数\nimport { convertToCSV as csvConverter } from './utils';\n\n// 导入混合检索相关\n\nimport { Tooltip } from 'antd';\nimport { BulbOutlined, SendOutlined } from '@ant-design/icons';\n// 导入聊天历史服务\nimport { chatHistoryService } from '../../services/chatHistoryService';\n\n// 导入共享类型\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// 内联定义图标组件\nconst Brain = props => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: \"2\",\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\",\n  ...props,\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 55,\n  columnNumber: 3\n}, this);\n_c = Brain;\nconst Database = props => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: \"2\",\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\",\n  ...props,\n  children: [/*#__PURE__*/_jsxDEV(\"ellipse\", {\n    cx: \"12\",\n    cy: \"5\",\n    rx: \"9\",\n    ry: \"3\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M21 12c0 1.66-4 3-9 3s-9-1.34-9-3\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 73,\n  columnNumber: 3\n}, this);\n_c2 = Database;\nconst Search = props => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: \"2\",\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\",\n  ...props,\n  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"11\",\n    cy: \"11\",\n    r: \"8\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"m21 21-4.3-4.3\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 92,\n  columnNumber: 3\n}, this);\n_c3 = Search;\nconst BarChart = props => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: \"2\",\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\",\n  ...props,\n  children: [/*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"12\",\n    y1: \"20\",\n    x2: \"12\",\n    y2: \"10\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"18\",\n    y1: \"20\",\n    x2: \"18\",\n    y2: \"4\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"6\",\n    y1: \"20\",\n    x2: \"6\",\n    y2: \"16\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 110,\n  columnNumber: 3\n}, this);\n_c4 = BarChart;\nconst FileText = props => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: \"2\",\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\",\n  ...props,\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n    points: \"14 2 14 8 20 8\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"16\",\n    y1: \"13\",\n    x2: \"8\",\n    y2: \"13\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"16\",\n    y1: \"17\",\n    x2: \"8\",\n    y2: \"17\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"10\",\n    y1: \"9\",\n    x2: \"8\",\n    y2: \"9\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 129,\n  columnNumber: 3\n}, this);\n_c5 = FileText;\nconst Code = props => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: \"2\",\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\",\n  ...props,\n  children: [/*#__PURE__*/_jsxDEV(\"polyline\", {\n    points: \"16 18 22 12 16 6\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n    points: \"8 6 2 12 8 18\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 163,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 150,\n  columnNumber: 3\n}, this);\n_c6 = Code;\nconst CodeIcon = props => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: \"2\",\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\",\n  ...props,\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"m18 16 4-4-4-4\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"m6 8-4 4 4 4\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"m14.5 4-5 16\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 168,\n  columnNumber: 3\n}, this);\n\n// 导入错误图标\n_c7 = CodeIcon;\nconst AlertCircle = props => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: \"2\",\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\",\n  ...props,\n  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n    cx: \"12\",\n    cy: \"12\",\n    r: \"10\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 200,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"12\",\n    y1: \"8\",\n    x2: \"12\",\n    y2: \"12\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 201,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"12\",\n    y1: \"16\",\n    x2: \"12\",\n    y2: \"16\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 188,\n  columnNumber: 3\n}, this);\n\n// 添加连接状态图标\n_c8 = AlertCircle;\nconst WifiIcon = props => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: \"2\",\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\",\n  ...props,\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M5 12.55a11 11 0 0 1 14.08 0\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 220,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M1.42 9a16 16 0 0 1 21.16 0\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 221,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M8.53 16.11a6 6 0 0 1 6.95 0\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 222,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n    x1: \"12\",\n    y1: \"20\",\n    x2: \"12.01\",\n    y2: \"20\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 223,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 208,\n  columnNumber: 3\n}, this);\n\n// WebSocket连接状态指示器组件\n_c9 = WifiIcon;\nconst WebSocketStatusIndicator = () => {\n  _s();\n  const [status, setStatus] = useState(globalWebSocketState);\n  useEffect(() => {\n    // 创建一个定时器，定期检查WebSocket状态\n    const intervalId = setInterval(() => {\n      setStatus(globalWebSocketState);\n    }, 500); // 每500ms检查一次\n\n    return () => clearInterval(intervalId);\n  }, []);\n\n  // 根据状态返回不同的样式类和文本\n  const getStatusInfo = () => {\n    switch (status) {\n      case WebSocketConnectionState.CONNECTED:\n        return {\n          statusClass: 'websocket-status-connected',\n          text: '已连接'\n        };\n      case WebSocketConnectionState.CONNECTING:\n        return {\n          statusClass: 'websocket-status-connecting',\n          text: '连接中'\n        };\n      case WebSocketConnectionState.RECONNECTING:\n        return {\n          statusClass: 'websocket-status-reconnecting',\n          text: '重连中'\n        };\n      case WebSocketConnectionState.ERROR:\n        return {\n          statusClass: 'websocket-status-error',\n          text: '连接错误'\n        };\n      case WebSocketConnectionState.DISCONNECTED:\n        return {\n          statusClass: 'websocket-status-disconnected',\n          text: '未连接'\n        };\n      default:\n        return {\n          statusClass: 'websocket-status-disconnected',\n          text: '未知状态'\n        };\n    }\n  };\n  const statusInfo = getStatusInfo();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `websocket-status ${statusInfo.statusClass}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"websocket-status-dot\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: statusInfo.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 261,\n    columnNumber: 5\n  }, this);\n};\n\n// 定义处理步骤类型\n_s(WebSocketStatusIndicator, \"1ry5SOAcId+XUGr8R9NXwEWCvU0=\");\n_c0 = WebSocketStatusIndicator; // 定义用户反馈状态类型\n// 修改RegionOutput类型\nexport default function Text2SQL() {\n  _s2();\n  const [query, setQuery] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [processingSteps, setProcessingSteps] = useState([]);\n\n  // 数据库连接状态\n  const [connections, setConnections] = useState([]);\n  const [selectedConnectionId, setSelectedConnectionId] = useState(null);\n  const [loadingConnections, setLoadingConnections] = useState(false);\n\n  // 用户反馈启用状态\n  const [userFeedbackEnabled, setUserFeedbackEnabled] = useState(false);\n\n  // 添加分页状态\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(10);\n\n  // 按区域分类的流式输出\n  const [regionOutputs, setRegionOutputs] = useState({\n    analysis: {\n      merged: '',\n      messages: [],\n      hasContent: false,\n      streaming: false\n    },\n    sql: {\n      merged: '',\n      messages: [],\n      hasContent: false,\n      streaming: false\n    },\n    explanation: {\n      merged: '',\n      messages: [],\n      hasContent: false,\n      streaming: false\n    },\n    data: {\n      merged: '',\n      messages: [],\n      hasContent: false,\n      streaming: false\n    },\n    visualization: {\n      merged: '',\n      messages: [],\n      hasContent: false,\n      streaming: false\n    },\n    process: {\n      merged: '',\n      messages: [],\n      hasContent: false,\n      streaming: false\n    }\n  });\n\n  // 最终结果的状态\n  const [sqlResult, setSqlResult] = useState(null);\n  const [explanationResult, setExplanationResult] = useState(null);\n  const [analysisResult, setAnalysisResult] = useState(null); // 添加分析结果状态\n  const [dataResult, setDataResult] = useState(null);\n  const [visualizationResult, setVisualizationResult] = useState(null);\n\n  // 解释区域的独立状态管理\n  const [explanationState, setExplanationState] = useState({\n    hasContent: false,\n    streaming: false\n  });\n\n  // 区域折叠状态\n  const [collapsedSections, setCollapsedSections] = useState({\n    analysis: false,\n    sql: false,\n    explanation: false,\n    data: false,\n    visualization: false,\n    process: true // 默认折叠处理过程\n  });\n\n  // 添加用户反馈状态\n  const [userFeedback, setUserFeedback] = useState({\n    visible: false,\n    message: '',\n    promptMessage: ''\n  });\n\n  // 混合检索相关状态\n  const [hybridExamplesVisible, setHybridExamplesVisible] = useState(false);\n  const [similarExamples, setSimilarExamples] = useState([]);\n  const [hybridRetrievalEnabled, setHybridRetrievalEnabled] = useState(true);\n  const [schemaContext, setSchemaContext] = useState(null);\n\n  // 聊天历史和时间轴状态\n  const [chatHistories, setChatHistories] = useState([]);\n  const [selectedHistoryId, setSelectedHistoryId] = useState(null);\n  const [timelineMessages, setTimelineMessages] = useState([]);\n  const [currentSessionId, setCurrentSessionId] = useState('');\n\n  // 侧边栏折叠状态 - 默认折叠，支持 localStorage 持久化\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(() => {\n    const saved = localStorage.getItem('text2sql-sidebar-collapsed');\n    return saved !== null ? JSON.parse(saved) : true; // 默认折叠\n  });\n  const [savedSessionIds, setSavedSessionIds] = useState(new Set()); // 已保存的会话ID\n\n  // 图表引用\n  const chartRef = useRef(null);\n  // 存储EventSource实例以便在需要时关闭\n  const eventSourceRef = useRef(null);\n\n  // 在组件顶部添加计数器引用\n  const processingStepIdRef = useRef(1);\n\n  // 生成唯一ID的函数\n  const generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n\n  // 处理侧边栏切换并持久化到 localStorage\n  const handleSidebarToggle = () => {\n    const newCollapsedState = !sidebarCollapsed;\n    setSidebarCollapsed(newCollapsedState);\n    localStorage.setItem('text2sql-sidebar-collapsed', JSON.stringify(newCollapsedState));\n  };\n\n  // 创建新的聊天会话\n  const createNewChatSession = () => {\n    const sessionId = generateId();\n    setCurrentSessionId(sessionId);\n    setSelectedHistoryId(null);\n    setTimelineMessages([]);\n    resetProcessingState();\n    // 清除保存状态，允许新会话保存\n    setSavedSessionIds(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(sessionId); // 确保新会话ID不在已保存列表中\n      return newSet;\n    });\n    console.log('🆕 创建新聊天会话:', sessionId);\n    return sessionId;\n  };\n\n  // 添加时间轴消息\n  const addTimelineMessage = (type, content, metadata) => {\n    const message = {\n      id: generateId(),\n      type,\n      content,\n      timestamp: new Date(),\n      status: type === 'user' ? 'sent' : 'streaming',\n      metadata\n    };\n    setTimelineMessages(prev => [...prev, message]);\n    return message.id;\n  };\n\n  // 更新时间轴消息状态\n  const updateTimelineMessage = (messageId, updates) => {\n    setTimelineMessages(prev => prev.map(msg => msg.id === messageId ? {\n      ...msg,\n      ...updates\n    } : msg));\n  };\n\n  // 保存聊天历史\n  const saveChatHistory = async query => {\n    if (!currentSessionId || !query.trim()) return;\n    const history = {\n      id: currentSessionId,\n      title: query.length > 50 ? query.substring(0, 50) + '...' : query,\n      timestamp: new Date(),\n      query,\n      response: {\n        analysis: analysisResult || regionOutputs.analysis.merged,\n        sql: sqlResult || '',\n        explanation: explanationResult || '',\n        data: dataResult || [],\n        visualization: visualizationResult\n      },\n      connectionId: selectedConnectionId\n    };\n\n    // 更新本地状态\n    setChatHistories(prev => {\n      const existing = prev.find(h => h.id === currentSessionId);\n      if (existing) {\n        return prev.map(h => h.id === currentSessionId ? history : h);\n      }\n      return [history, ...prev];\n    });\n\n    // 保存到数据库\n    try {\n      var _history$connectionId;\n      const saveRequest = {\n        session_id: currentSessionId,\n        title: history.title,\n        query: history.query,\n        response: history.response,\n        connection_id: (_history$connectionId = history.connectionId) !== null && _history$connectionId !== void 0 ? _history$connectionId : undefined\n      };\n      await chatHistoryService.saveChatHistoryViaSSE(saveRequest);\n      console.log('聊天历史保存成功:', currentSessionId);\n    } catch (error) {\n      console.error('保存聊天历史失败:', error);\n      // 不影响用户体验，只记录错误\n    }\n  };\n\n  // 加载聊天历史列表\n  const loadChatHistories = async () => {\n    try {\n      console.log('🔄 开始加载聊天历史，连接ID:', selectedConnectionId);\n      const response = await chatHistoryService.getChatHistories(0, 50, selectedConnectionId || undefined);\n      console.log('📥 后端响应:', response);\n      const histories = response.sessions.map(session => ({\n        id: session.id,\n        title: session.title,\n        timestamp: new Date(session.timestamp),\n        query: session.query,\n        response: session.response,\n        connectionId: session.connection_id || null\n      }));\n      setChatHistories(histories);\n      console.log('✅ 聊天历史加载成功，数量:', histories.length);\n      console.log('📋 历史记录详情:', histories);\n    } catch (error) {\n      console.error('❌ 加载聊天历史失败:', error);\n    }\n  };\n\n  // 选择历史记录\n  const handleSelectHistory = async historyId => {\n    try {\n      console.log('🔍 选择历史记录:', historyId);\n\n      // 首先尝试从本地状态获取\n      let history = chatHistories.find(h => h.id === historyId);\n      console.log('📋 本地历史记录:', history ? '找到' : '未找到');\n\n      // 如果本地没有，从数据库获取\n      if (!history) {\n        console.log('🌐 从数据库获取历史记录...');\n        const response = await chatHistoryService.getChatHistory(historyId);\n        history = {\n          id: response.session_id,\n          title: response.title,\n          timestamp: new Date(response.created_at),\n          query: response.query,\n          response: response.response,\n          connectionId: response.connection_id || null\n        };\n        console.log('📥 数据库历史记录:', history);\n      }\n      if (!history) {\n        console.error('❌ 未找到历史记录:', historyId);\n        return;\n      }\n      console.log('✅ 开始恢复历史记录:', {\n        id: history.id,\n        title: history.title,\n        hasAnalysis: !!history.response.analysis,\n        hasSql: !!history.response.sql,\n        hasExplanation: !!history.response.explanation,\n        hasData: !!(history.response.data && history.response.data.length > 0),\n        hasVisualization: !!history.response.visualization\n      });\n      setSelectedHistoryId(historyId);\n      setCurrentSessionId(historyId);\n\n      // 恢复历史数据\n      setSqlResult(history.response.sql);\n      setExplanationResult(history.response.explanation);\n      setAnalysisResult(history.response.analysis); // 恢复分析结果\n      setDataResult(history.response.data);\n      setVisualizationResult(history.response.visualization);\n\n      // 重要：恢复解释区域的独立状态\n      setExplanationState({\n        hasContent: !!history.response.explanation,\n        streaming: false\n      });\n\n      // 重要：更新regionOutputs状态，确保右侧内容区域显示\n      setRegionOutputs({\n        analysis: {\n          merged: history.response.analysis || '',\n          messages: [],\n          hasContent: !!history.response.analysis,\n          streaming: false\n        },\n        sql: {\n          merged: history.response.sql || '',\n          messages: [],\n          hasContent: !!history.response.sql,\n          streaming: false\n        },\n        explanation: {\n          merged: history.response.explanation || '',\n          messages: [],\n          hasContent: !!history.response.explanation,\n          streaming: false\n        },\n        data: {\n          merged: history.response.data && history.response.data.length > 0 ? `查询返回了 ${history.response.data.length} 条数据记录` : '',\n          messages: [],\n          hasContent: !!(history.response.data && history.response.data.length > 0),\n          streaming: false\n        },\n        visualization: {\n          merged: history.response.visualization ? `生成了 ${history.response.visualization.type || '图表'} 类型的可视化` : '',\n          messages: [],\n          hasContent: !!history.response.visualization,\n          streaming: false\n        },\n        process: {\n          merged: '',\n          messages: [],\n          hasContent: false,\n          streaming: false\n        }\n      });\n      console.log('🔄 regionOutputs状态已更新，右侧内容应该显示');\n\n      // 重建时间轴消息\n      const messages = [{\n        id: generateId(),\n        type: 'user',\n        content: history.query,\n        timestamp: history.timestamp,\n        status: 'sent'\n      }];\n      if (history.response.analysis) {\n        messages.push({\n          id: generateId(),\n          type: 'assistant',\n          content: history.response.analysis,\n          timestamp: new Date(history.timestamp.getTime() + 1000),\n          status: 'completed',\n          metadata: {\n            region: 'analysis',\n            source: '查询分析智能体'\n          }\n        });\n      }\n      if (history.response.sql) {\n        messages.push({\n          id: generateId(),\n          type: 'assistant',\n          content: history.response.sql,\n          timestamp: new Date(history.timestamp.getTime() + 2000),\n          status: 'completed',\n          metadata: {\n            region: 'sql',\n            source: 'SQL生成智能体',\n            isSQL: true\n          }\n        });\n      }\n      if (history.response.explanation) {\n        messages.push({\n          id: generateId(),\n          type: 'assistant',\n          content: history.response.explanation,\n          timestamp: new Date(history.timestamp.getTime() + 3000),\n          status: 'completed',\n          metadata: {\n            region: 'explanation',\n            source: 'SQL解释智能体'\n          }\n        });\n      }\n      if (history.response.data && history.response.data.length > 0) {\n        messages.push({\n          id: generateId(),\n          type: 'assistant',\n          content: `查询返回了 ${history.response.data.length} 条数据记录`,\n          timestamp: new Date(history.timestamp.getTime() + 4000),\n          status: 'completed',\n          metadata: {\n            region: 'data',\n            source: '数据查询智能体'\n          }\n        });\n      }\n      if (history.response.visualization) {\n        messages.push({\n          id: generateId(),\n          type: 'assistant',\n          content: `生成了 ${history.response.visualization.type} 类型的可视化图表`,\n          timestamp: new Date(history.timestamp.getTime() + 5000),\n          status: 'completed',\n          metadata: {\n            region: 'visualization',\n            source: '可视化推荐智能体',\n            isVisualization: true\n          }\n        });\n      }\n      setTimelineMessages(messages);\n      console.log('✅ 历史记录恢复完成，时间轴消息数量:', messages.length);\n    } catch (error) {\n      console.error('❌ 选择历史记录失败:', error);\n    }\n  };\n\n  // 删除聊天历史\n  const handleDeleteHistory = async historyId => {\n    try {\n      console.log('🗑️ 删除聊天历史:', historyId);\n\n      // 显示确认对话框\n      const confirmed = window.confirm('确定要删除这条聊天记录吗？此操作无法撤销。');\n      if (!confirmed) {\n        return;\n      }\n\n      // 调用删除API\n      await chatHistoryService.deleteChatHistory(historyId);\n\n      // 从本地状态中移除\n      setChatHistories(prev => prev.filter(h => h.id !== historyId));\n\n      // 如果删除的是当前选中的历史记录，则创建新会话\n      if (selectedHistoryId === historyId) {\n        createNewChatSession();\n      }\n      console.log('✅ 聊天历史删除成功');\n    } catch (error) {\n      console.error('❌ 删除聊天历史失败:', error);\n      alert('删除失败，请稍后重试');\n    }\n  };\n\n  // 获取数据库连接列表\n  useEffect(() => {\n    const fetchConnections = async () => {\n      try {\n        setLoadingConnections(true);\n        const response = await getConnections();\n        setConnections(response.data);\n\n        // 如果有连接，默认选择第一个\n        if (response.data.length > 0) {\n          setSelectedConnectionId(response.data[0].id);\n        }\n      } catch (error) {\n        console.error('获取数据库连接失败:', error);\n        setError('获取数据库连接失败，请检查网络连接或联系管理员');\n      } finally {\n        setLoadingConnections(false);\n      }\n    };\n    fetchConnections();\n\n    // 初始化一个新的聊天会话\n    createNewChatSession();\n\n    // 加载聊天历史\n    loadChatHistories();\n  }, []); // 仅在组件挂载时执行一次\n\n  // 当选择的连接改变时，重新加载聊天历史\n  useEffect(() => {\n    if (selectedConnectionId) {\n      loadChatHistories();\n    }\n  }, [selectedConnectionId]);\n\n  // 监听查询完成状态，自动保存聊天历史\n  useEffect(() => {\n    const shouldSave = !loading &&\n    // 不在加载中\n    currentSessionId &&\n    // 有会话ID\n    !savedSessionIds.has(currentSessionId) &&\n    // 该会话还未保存\n    sqlResult &&\n    // 有SQL结果\n    dataResult &&\n    // 有数据结果\n    timelineMessages.length > 0; // 有时间轴消息\n\n    if (shouldSave) {\n      const userMessage = timelineMessages.find(msg => msg.type === 'user');\n      if (userMessage) {\n        console.log('🔄 检测到查询完成，自动保存聊天历史');\n\n        // 延迟保存，确保所有状态都已更新\n        const saveTimeout = setTimeout(async () => {\n          try {\n            await saveChatHistory(userMessage.content);\n            await loadChatHistories();\n            // 标记该会话已保存\n            setSavedSessionIds(prev => new Set(prev).add(currentSessionId));\n            console.log('✅ 聊天历史自动保存成功');\n          } catch (error) {\n            console.error('❌ 聊天历史自动保存失败:', error);\n          }\n        }, 2000); // 2秒延迟\n\n        return () => clearTimeout(saveTimeout);\n      }\n    }\n  }, [loading, currentSessionId, sqlResult, dataResult, timelineMessages, savedSessionIds]);\n\n  // 切换折叠状态\n  const toggleCollapse = section => {\n    setCollapsedSections(prev => ({\n      ...prev,\n      [section]: !prev[section]\n    }));\n  };\n\n  // 处理最终SQL结果\n  const handleFinalSql = sql => {\n    console.log('收到最终SQL结果，关闭流式状态', sql);\n    // 标记SQL区域流式输出结束\n    setRegionOutputs(prev => ({\n      ...prev,\n      sql: {\n        ...prev.sql,\n        streaming: false,\n        finalResult: sql,\n        hasContent: true\n      }\n    }));\n    setSqlResult(sql);\n  };\n\n  // 处理最终解释结果\n  const handleFinalExplanation = explanation => {\n    console.log('收到最终解释结果，关闭流式状态', {\n      explanationLength: explanation ? explanation.length : 0,\n      currentExplanationResult: explanationResult ? `当前内容长度 ${explanationResult.length}` : '无当前内容'\n    });\n\n    // 标记解释区域流式输出结束\n    setRegionOutputs(prev => {\n      return {\n        ...prev,\n        explanation: {\n          ...prev.explanation,\n          streaming: false,\n          hasContent: true // 确保区域被标记为有内容\n        }\n      };\n    });\n\n    // 解释内容已生成完成\n    console.log('SQL解释内容已生成完成');\n  };\n\n  // 处理最终数据结果\n  const handleFinalData = data => {\n    console.log('收到最终数据结果，关闭流式状态');\n    // 标记数据区域流式输出结束\n    setRegionOutputs(prev => ({\n      ...prev,\n      data: {\n        ...prev.data,\n        streaming: false,\n        hasContent: true // 确保区域被标记为有内容\n      }\n    }));\n    setDataResult(data);\n  };\n\n  // 处理最终可视化结果\n  const handleFinalVisualization = async visualization => {\n    console.log('🎨 handleFinalVisualization被调用，可视化结果:', visualization);\n    console.log('📊 当前数据结果:', dataResult);\n    console.log('收到最终可视化结果，关闭流式状态');\n\n    // 标记可视化区域流式输出结束\n    setRegionOutputs(prev => ({\n      ...prev,\n      visualization: {\n        ...prev.visualization,\n        streaming: false,\n        hasContent: true // 确保区域被标记为有内容\n      }\n    }));\n\n    // 设置可视化结果\n    setVisualizationResult(visualization);\n    console.log('✅ 可视化结果已设置:', visualization);\n\n    // 在收到可视化结果后直接设置 loading 状态为 false，使分析按钮可用\n    // 只有当region='visualization'的消息且is_final=true时，才设置分析按钮为可用状态\n    setLoading(false);\n    console.log('可视化数据已准备就绪，分析按钮恢复可用');\n\n    // 可视化数据已准备就绪，触发保存聊天历史\n    console.log('🔄 可视化数据已准备就绪，准备保存聊天历史');\n\n    // 延迟保存，确保所有数据都已设置完成\n    setTimeout(async () => {\n      if (currentSessionId) {\n        // 获取当前用户查询（从时间轴消息中获取）\n        const userMessage = timelineMessages.find(msg => msg.type === 'user');\n        if (userMessage) {\n          try {\n            await saveChatHistory(userMessage.content);\n            // 保存后立即刷新历史列表\n            await loadChatHistories();\n            console.log('聊天历史已保存并刷新列表（从可视化完成触发）');\n          } catch (error) {\n            console.error('保存聊天历史失败:', error);\n          }\n        }\n      }\n    }, 500);\n  };\n\n  // 处理最终分析结果\n  const handleFinalAnalysis = analysis => {\n    console.log('收到最终分析结果，关闭流式状态');\n    // 标记分析区域流式输出结束\n    setRegionOutputs(prev => ({\n      ...prev,\n      analysis: {\n        ...prev.analysis,\n        streaming: false,\n        hasContent: true\n      }\n    }));\n  };\n\n  // 处理最终结果\n  const handleResult = finalResult => {\n    console.log('🎯 handleResult被调用，最终结果:', finalResult);\n    setError(null); // 清除错误\n\n    // 检查所有区域的流式输出是否都已结束\n    const allRegionsCompleted = Object.values(regionOutputs).every(region => !region.streaming);\n\n    // 检查并处理解释结果\n    const validExplanation = finalResult.explanation && typeof finalResult.explanation === 'string' && finalResult.explanation.trim() ? finalResult.explanation : null;\n\n    // 保存聊天历史（在处理完成后）\n    setTimeout(async () => {\n      if (currentSessionId) {\n        // 获取当前用户查询（从时间轴消息中获取）\n        const userMessage = timelineMessages.find(msg => msg.type === 'user');\n        if (userMessage) {\n          await saveChatHistory(userMessage.content);\n          // 保存后立即刷新历史列表\n          await loadChatHistories();\n          console.log('聊天历史已保存并刷新列表');\n        }\n      }\n    }, 1000);\n\n    // 标记所有区域流式输出结束并设置hasContent\n    setRegionOutputs(prev => {\n      const updated = {\n        ...prev\n      };\n      Object.keys(updated).forEach(key => {\n        const region = updated[key];\n        region.streaming = false;\n\n        // 根据最终结果设置hasContent\n        if (key === 'sql' && finalResult.sql) {\n          region.hasContent = true;\n        }\n        if (key === 'explanation' && validExplanation) {\n          region.hasContent = true;\n          region.merged = validExplanation; // 确保merged字段有正确的内容\n          console.log('设置explanation区域merged字段:', validExplanation.substring(0, 50) + '...');\n        }\n        if (key === 'data' && finalResult.results && finalResult.results.length > 0) {\n          region.hasContent = true;\n        }\n        if (key === 'visualization' && (finalResult.visualization_type || finalResult.visualization_config)) {\n          region.hasContent = true;\n        }\n      });\n      return updated;\n    });\n\n    // 设置最终结果的所有部分\n    setSqlResult(finalResult.sql);\n\n    // 不在这里设置解释内容，而是依赖流式消息的累加逻辑\n    console.log('保留现有解释内容，当前长度:', explanationResult ? explanationResult.length : 0);\n    setDataResult(finalResult.results);\n    setVisualizationResult({\n      type: finalResult.visualization_type,\n      config: finalResult.visualization_config\n    });\n\n    // 打印日志，帮助调试\n    console.log('设置最终结果:', {\n      sql: finalResult.sql ? finalResult.sql.substring(0, 50) + '...' : null,\n      explanation: validExplanation ? validExplanation.substring(0, 50) + '...' : null,\n      results: finalResult.results ? `${finalResult.results.length} 条结果` : null,\n      visualization: finalResult.visualization_type\n    });\n\n    // 如果有解释内容，记录日志\n    if (validExplanation) {\n      console.log('有解释内容，内容长度:', validExplanation.length);\n    }\n\n    // 只有当收到region='visualization'的消息且is_final=true时，才将分析按钮设置为可用状态\n    // 不要根据regionOutputs.visualization的状态来判断，因为它可能被错误地标记为完成\n    // 这里不再设置分析按钮状态，而是依赖handleMessage中对region='visualization'的消息处理\n  };\n\n  // 处理错误\n  const handleError = error => {\n    console.error('处理出错:', error);\n\n    // 使用更友好的错误消息\n    let errorMessage = error.message || '请求处理过程中发生错误';\n\n    // 检查是否是WebSocket连接错误\n    if (errorMessage.includes('WebSocket') || errorMessage.includes('连接') || errorMessage.includes('服务器')) {\n      // 使用全局WebSocket错误信息（如果有）\n      if (globalWebSocketError) {\n        errorMessage = globalWebSocketError;\n      } else {\n        errorMessage = '无法连接到服务器，请稍后再试';\n      }\n    }\n    setError(errorMessage);\n    setLoading(false); // 发生错误时一定要停止加载状态\n\n    // 重置所有区域的流式状态\n    setRegionOutputs(prev => {\n      const updated = {\n        ...prev\n      };\n      Object.keys(updated).forEach(key => {\n        const region = updated[key];\n        region.streaming = false;\n      });\n      return updated;\n    });\n  };\n\n  // 添加页面切换函数\n  const handlePageChange = pageNumber => {\n    setCurrentPage(pageNumber);\n  };\n\n  // 添加计算总页数的函数\n  const getTotalPages = () => {\n    if (!dataResult) return 1;\n    return Math.ceil(dataResult.length / pageSize);\n  };\n\n  // 获取当前页的数据\n  const getCurrentPageData = () => {\n    if (!dataResult) return [];\n    const startIndex = (currentPage - 1) * pageSize;\n    const endIndex = startIndex + pageSize;\n    return dataResult.slice(startIndex, endIndex);\n  };\n\n  // 重置处理状态\n  const resetProcessingState = () => {\n    setError(null);\n    setLoading(false);\n    setProcessingSteps([]);\n    setCurrentPage(1); // 重置分页状态\n\n    // 完全重置所有状态\n    console.log('重置处理状态：完全重置所有区域');\n\n    // 重置所有区域的状态\n    setRegionOutputs({\n      analysis: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      sql: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      explanation: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      data: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      visualization: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      process: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      }\n    });\n\n    // 重置所有结果\n    setSqlResult(null);\n    setExplanationResult(null);\n    setAnalysisResult(null); // 重置分析结果\n    setDataResult(null);\n    setVisualizationResult(null);\n\n    // 重置解释区域的独立状态\n    setExplanationState({\n      hasContent: false,\n      streaming: false\n    });\n\n    // 关闭之前的EventSource连接\n    if (eventSourceRef.current) {\n      try {\n        eventSourceRef.current.close();\n        console.log('已关闭之前的EventSource连接');\n      } catch (err) {\n        console.error('关闭EventSource连接时出错:', err);\n      }\n      eventSourceRef.current = null;\n    }\n  };\n\n  // 监听WebSocket连接状态变化\n  useEffect(() => {\n    // 创建一个定时器，定期检查WebSocket状态\n    const intervalId = setInterval(() => {\n      // 如果有WebSocket错误且当前没有显示错误，则显示错误\n      if (globalWebSocketError && !error && !loading) {\n        setError(globalWebSocketError);\n      }\n\n      // 如果WebSocket恢复正常且当前显示的是WebSocket错误，则清除错误\n      if (globalWebSocketState === WebSocketConnectionState.CONNECTED && error && (error === globalWebSocketError || error.includes('连接') || error.includes('服务器'))) {\n        setError(null);\n      }\n    }, 1000); // 每秒检查一次\n\n    return () => clearInterval(intervalId);\n  }, [error, loading]);\n\n  // 流式查询处理\n  const handleStreamSearch = async () => {\n    if (loading) return;\n\n    // 检查SSE状态\n    if (globalWebSocketState === WebSocketConnectionState.ERROR || globalWebSocketState === WebSocketConnectionState.DISCONNECTED) {\n      // 尝试重新连接\n      try {\n        const sse = getWebSocketInstance();\n        const connected = await sse.connect();\n        if (!connected) {\n          setError(globalWebSocketError || '无法连接到服务器，请稍后再试');\n          return;\n        }\n      } catch (error) {\n        setError(globalWebSocketError || '无法连接到服务器，请稍后再试');\n        return;\n      }\n    }\n    setError(null);\n    setLoading(true);\n\n    // 保存当前查询内容以便发送\n    const currentQuery = query.trim();\n\n    // 清空输入框\n    setQuery('');\n\n    // 重置所有状态，包括解释内容\n    setProcessingSteps([]);\n    setCurrentPage(1);\n    setSqlResult(null);\n    setExplanationResult(null);\n    setAnalysisResult(null); // 重置分析结果\n    setDataResult(null);\n    setVisualizationResult(null);\n\n    // 重置解释区域的独立状态\n    setExplanationState({\n      hasContent: false,\n      streaming: false\n    });\n    if (!currentQuery) {\n      setError('请输入有效的查询');\n      setLoading(false);\n      return;\n    }\n\n    // 检查是否选择了数据库连接\n    if (!selectedConnectionId) {\n      setError('请选择一个数据库连接');\n      setLoading(false);\n      return;\n    }\n\n    // 创建新的聊天会话（如果需要）\n    let sessionId = currentSessionId;\n    if (!sessionId || selectedHistoryId) {\n      sessionId = createNewChatSession();\n    }\n\n    // 添加用户消息到时间轴\n    const userMessageId = addTimelineMessage('user', currentQuery);\n\n    // 初始化UI状态，确保分析区域可见\n    console.log('初始化分析区域');\n\n    // 清理消息缓存，防止重复消息检测影响新查询\n    messageCache.current.clear();\n\n    // 一次性重置所有区域的状态，避免重复设置\n    setRegionOutputs({\n      analysis: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      sql: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      explanation: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      data: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      visualization: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      process: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      }\n    });\n\n    // 强制设置分析区域为展开状态\n    setCollapsedSections(prev => ({\n      ...prev,\n      analysis: false\n    }));\n\n    // 直接在DOM上更新样式以确保分析区域可见\n    setTimeout(() => {\n      const analysisContainer = document.querySelector('.analysis-output-container');\n      if (analysisContainer) {\n        // 确保容器可见\n        analysisContainer.style.display = 'block';\n        analysisContainer.style.minHeight = '200px';\n\n        // 滚动到分析区域\n        analysisContainer.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start'\n        });\n        console.log('滚动到analysis-output-container');\n      } else {\n        console.warn('找不到分析区域容器');\n      }\n\n      // 确保内容区域可见\n      const contentArea = document.querySelector('.analysis-content');\n      if (contentArea) {\n        contentArea.style.minHeight = '100px';\n        contentArea.style.display = 'block';\n        console.log('设置analysis-content样式');\n      } else {\n        console.warn('找不到分析内容区域');\n      }\n    }, 100);\n    try {\n      // 使用SSE进行流式通信\n      console.log('正在使用SSE发送查询...');\n\n      // 导入SSE发送函数\n      const {\n        sendSSEText2SQLRequest\n      } = await import('./api');\n\n      // 发送SSE请求\n      sendSSEText2SQLRequest(currentQuery, handleMessage, handleResult, handleError, handleFinalSql, handleFinalExplanation, handleFinalData, handleFinalVisualization, selectedConnectionId, userFeedbackEnabled);\n      console.log('SSE查询已发送');\n    } catch (error) {\n      console.error('SSE请求失败:', error);\n      setError(`SSE请求失败: ${error instanceof Error ? error.message : '未知错误'}`);\n      setLoading(false);\n    }\n  };\n\n  // 组件卸载时关闭连接\n  useEffect(() => {\n    return () => {\n      // 关闭EventSource连接\n      if (eventSourceRef.current) {\n        eventSourceRef.current.close();\n        eventSourceRef.current = null;\n      }\n\n      // 关闭WebSocket连接\n      closeWebSocketConnection();\n    };\n  }, []);\n\n  // 修改图表相关逻辑，减少页面抖动\n  useEffect(() => {\n    if (visualizationResult && dataResult && dataResult.length > 0 && chartRef.current) {\n      // 添加一个标记，避免重复渲染\n      if (chartRef.current.dataset.rendered === 'true') {\n        return;\n      }\n\n      // 如果可视化类型是表格，跳过图表渲染\n      if (visualizationResult.type === 'table') {\n        console.log('表格类型可视化，跳过图表渲染');\n        // 标记为已渲染，避免重复处理\n        chartRef.current.dataset.rendered = 'true';\n\n        // 表格类型可视化已完成，但我们不在这里设置分析按钮状态\n        console.log('表格类型可视化完成');\n        return;\n      }\n\n      // 使用动态导入引入Chart.js\n      import('chart.js/auto').then(ChartModule => {\n        const Chart = ChartModule.default;\n\n        // 获取画布上下文\n        const canvas = chartRef.current;\n        if (!canvas) return;\n\n        // 销毁现有图表\n        try {\n          const chartInstance = Chart.getChart(canvas);\n          if (chartInstance) {\n            chartInstance.destroy();\n          }\n        } catch (e) {\n          console.log('No existing chart to destroy');\n        }\n\n        // 准备图表数据\n        const ctx = canvas.getContext('2d');\n        if (!ctx) return;\n        try {\n          // 标记为已渲染，避免重复渲染\n          canvas.dataset.rendered = 'true';\n          const chartType = visualizationResult.type;\n          const config = prepareChartConfig(chartType, visualizationResult.config, dataResult);\n          new Chart(ctx, config);\n\n          // 图表渲染完成，但我们不在这里设置分析按钮状态\n          console.log('图表渲染完成');\n        } catch (error) {\n          console.error('图表渲染错误:', error);\n          // 图表渲染出错，但我们不在这里设置分析按钮状态\n          console.log('图表渲染出错');\n        }\n      });\n    }\n\n    // 清理函数\n    return () => {\n      if (chartRef.current) {\n        // 重置已渲染标记\n        chartRef.current.dataset.rendered = 'false';\n\n        // 动态导入Chart.js并清理图表\n        import('chart.js/auto').then(ChartModule => {\n          const Chart = ChartModule.default;\n          try {\n            const chartInstance = Chart.getChart(chartRef.current);\n            if (chartInstance) {\n              chartInstance.destroy();\n            }\n          } catch (e) {\n            console.log('Error cleaning up chart:', e);\n          }\n        }).catch(err => {\n          console.error('清理图表时出错:', err);\n        });\n      }\n    };\n  }, [visualizationResult, dataResult]);\n\n  // 添加图表配置准备函数\n  const prepareChartConfig = (type, config, data) => {\n    // 提取数据点\n    const labels = data.map(item => {\n      // 尝试获取X轴字段值\n      const xField = config.xAxis || Object.keys(item)[0];\n      return item[xField];\n    });\n\n    // 提取数据系列\n    const yField = config.yAxis || Object.keys(data[0])[1];\n    const dataPoints = data.map(item => item[yField]);\n\n    // 生成配置\n    return {\n      type,\n      // 使用正确的类型\n      data: {\n        labels: labels,\n        datasets: [{\n          label: config.title || '数据系列',\n          data: dataPoints,\n          backgroundColor: type === 'pie' ? ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'] : 'rgba(54, 162, 235, 0.5)',\n          borderColor: 'rgba(54, 162, 235, 1)',\n          borderWidth: 1\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          title: {\n            display: !!config.title,\n            text: config.title || ''\n          },\n          tooltip: {\n            enabled: true\n          },\n          legend: {\n            display: type === 'pie'\n          }\n        }\n      }\n    };\n  };\n\n  // 优化的区域输出更新函数 - 专门为流式markdown优化\n  const updateRegionOutputs = useCallback((region, content, isFinal) => {\n    setRegionOutputs(prev => {\n      const updatedRegions = {\n        ...prev\n      };\n      const regionData = updatedRegions[region];\n      if (!regionData) {\n        console.error(`未知区域: ${region}`);\n        return prev;\n      }\n\n      // 对于分析区域和解释区域，使用专门的流式markdown重复检测策略\n      if (region === 'analysis' || region === 'explanation') {\n        // 对于分析和解释区域，几乎不进行重复检测，因为markdown流式内容可能包含重复的符号\n        // 只检测完全相同且较长的内容块（超过100字符）\n        if (content && content.length > 100 && regionData.merged === content) {\n          console.log(`跳过完全重复的${region}内容: ${region} - ${content.substring(0, 30)}...`);\n          regionData.streaming = isFinal !== true;\n          return updatedRegions;\n        }\n      } else {\n        // 其他区域保持原有的重复检测逻辑\n        if (content && regionData.merged.includes(content)) {\n          console.log(`跳过重复内容: ${region} - ${content.substring(0, 50)}...`);\n          regionData.streaming = isFinal !== true;\n          return updatedRegions;\n        }\n      }\n\n      // 标记该区域已有内容\n      regionData.hasContent = true;\n\n      // 判断streaming状态\n      regionData.streaming = isFinal !== true;\n\n      // 连续输出逻辑 - 对于分析和解释区域，不在这里累积内容，避免双重累积\n      if (region === 'analysis' || region === 'explanation') {\n        // 分析和解释区域的内容由各自的状态管理，这里只更新流式状态\n        // 不累积内容，避免与 analysisResult/explanationResult 状态重复\n        console.log(`${region}区域内容由专门状态管理，跳过regionOutputs累积`);\n      } else {\n        // 其他区域正常累积内容\n        if (regionData.merged === '' || regionData.merged.includes('正在分析您的问题')) {\n          // 如果是初始状态或占位符，直接替换\n          regionData.merged = content;\n        } else {\n          // 直接追加新内容，保持连续性\n          regionData.merged += content;\n        }\n      }\n\n      // 添加详细的调试信息，特别关注分析区域和解释区域\n      if (region === 'analysis') {\n        console.log(`🔄 [分析区域] 更新内容:`, {\n          region,\n          newContentLength: content.length,\n          totalLength: regionData.merged.length,\n          newContentPreview: content.substring(0, 50),\n          totalContentPreview: regionData.merged.substring(regionData.merged.length - 50),\n          newContentHasNewlines: content.includes('\\n'),\n          newContentHasSpaces: content.includes(' '),\n          rawNewContent: JSON.stringify(content.substring(0, 50))\n        });\n      } else if (region === 'explanation') {\n        console.log(`🔄 [解释区域] 更新内容:`, {\n          region,\n          newContentLength: content.length,\n          totalLength: regionData.merged.length,\n          newContentPreview: content.substring(0, 50),\n          totalContentPreview: regionData.merged.substring(Math.max(0, regionData.merged.length - 50)),\n          isAppending: regionData.merged.length > 0\n        });\n      } else {\n        console.log(`更新区域内容: ${region} - 新增${content.length}字符，总长度${regionData.merged.length}`);\n      }\n      return updatedRegions;\n    });\n  }, []);\n\n  // 简化的消息后处理函数\n  const handlePostMessageTasks = useCallback((region, message, source, content) => {\n    // 更新处理步骤\n    if (content && region === 'process') {\n      const step = {\n        id: processingStepIdRef.current++,\n        message: content,\n        timestamp: new Date(),\n        source: source\n      };\n      setProcessingSteps(prev => [...prev, step]);\n    }\n\n    // 检查是否是反馈请求消息\n    if (message.source === 'user_proxy' && message.content || message.type === 'feedback_request' && message.content || message.region === 'user_proxy' && message.content) {\n      console.log('🔔 检测到反馈请求消息:', {\n        source: message.source,\n        type: message.type,\n        region: message.region,\n        content: message.content,\n        timestamp: new Date().toISOString()\n      });\n      setUserFeedback({\n        visible: true,\n        message: '',\n        promptMessage: message.content\n      });\n    }\n  }, []);\n\n  // 消息去重缓存\n  const messageCache = useRef(new Set());\n\n  // 优化的消息处理函数 - 专门为流式markdown优化\n  const handleMessage = useCallback(message => {\n    // 清除错误状态\n    setError(null);\n\n    // 确定消息区域\n    let region = message.region || 'process';\n    const source = message.source || '系统';\n    let content = message.content || '';\n\n    // 对于分析区域和解释区域，不过滤包含空格和换行的内容，因为这些对markdown格式很重要\n    if (region === 'analysis' || region === 'explanation') {\n      // 只过滤完全空的内容\n      if (!content) {\n        return;\n      }\n    } else {\n      // 其他区域过滤空消息\n      if (!content.trim()) {\n        return;\n      }\n    }\n\n    // 优化消息去重逻辑 - 对分析区域和解释区域使用更精确的标识\n    let messageId;\n    if (region === 'analysis') {\n      // 对于分析区域，使用时间戳和内容长度来生成更精确的标识\n      messageId = `${region}-${source}-${Date.now()}-${content.length}-${content.substring(0, 20)}`;\n    } else if (region === 'explanation') {\n      // 对于解释区域，使用时间戳和内容哈希来生成更精确的标识，避免重复\n      const contentHash = content.length + content.substring(0, 30) + content.substring(content.length - 30);\n      messageId = `${region}-${source}-${Date.now()}-${contentHash}`;\n    } else if (region === 'user_proxy' || message.type === 'feedback_request') {\n      // 对于用户反馈请求，使用时间戳确保每次请求都能被处理，避免重复消息被忽略\n      messageId = `${region}-${source}-${Date.now()}-${Math.random()}-${content.substring(0, 50)}`;\n    } else {\n      // 其他区域保持原有逻辑\n      messageId = `${region}-${source}-${content.substring(0, 100)}`;\n    }\n    if (messageCache.current.has(messageId)) {\n      console.log(`跳过重复消息: ${region} - ${content.substring(0, 30)}...`);\n      return;\n    }\n    messageCache.current.add(messageId);\n\n    // 清理过期的缓存（保留最近1000条）\n    if (messageCache.current.size > 1000) {\n      const entries = Array.from(messageCache.current);\n      messageCache.current.clear();\n      entries.slice(-500).forEach(entry => messageCache.current.add(entry));\n    }\n\n    // 添加详细的调试信息，特别关注分析区域和解释区域的内容格式\n    if (region === 'analysis') {\n      console.log(`📋 [分析区域] 收到消息:`, {\n        region,\n        source,\n        contentLength: content.length,\n        contentPreview: content.substring(0, 100),\n        hasNewlines: content.includes('\\n'),\n        hasSpaces: content.includes(' '),\n        rawContent: JSON.stringify(content.substring(0, 100))\n      });\n    } else if (region === 'explanation') {\n      console.log(`📋 [解释区域] 收到消息:`, {\n        region,\n        source,\n        contentLength: content.length,\n        contentPreview: content.substring(0, 100),\n        hasNewlines: content.includes('\\n'),\n        hasSpaces: content.includes(' ')\n      });\n    } else {\n      console.log(`📋 收到消息: ${region} - ${source} - ${content.substring(0, 50)}...`);\n    }\n\n    // 更新区域输出状态（主要显示逻辑）\n    // 解释区域完全跳过 regionOutputs 处理，只使用 explanationResult 状态\n    if (region !== 'explanation') {\n      updateRegionOutputs(region, content, message.is_final);\n    }\n\n    // 异步更新其他状态，避免阻塞渲染\n    setTimeout(() => {\n      // 处理特殊区域的消息\n      if (region === 'analysis') {\n        // 更新分析结果 - 保持原始格式，直接累加\n        setAnalysisResult(prev => {\n          const prevContent = prev || '';\n          // 对于分析区域，直接累加内容，保持markdown格式\n          return prevContent + content;\n        });\n      } else if (region === 'explanation') {\n        // 更新解释结果 - 与分析区域保持完全一致的处理逻辑\n        setExplanationResult(prev => {\n          const prevContent = prev || '';\n          // 对于解释区域，直接累加内容，保持markdown格式（与分析区域完全一致）\n          return prevContent + content;\n        });\n\n        // 同时更新解释区域的独立状态\n        setExplanationState({\n          hasContent: true,\n          streaming: message.is_final !== true\n        });\n      }\n\n      // 如果是可视化区域的最终消息，停止加载状态\n      if (message.is_final === true && region === 'visualization') {\n        setLoading(false);\n        console.log('收到可视化区域的最终消息，分析按钮恢复可用');\n      }\n\n      // 处理其他消息后的任务\n      handlePostMessageTasks(region, message, source, content);\n    }, 0);\n  }, [updateRegionOutputs, handlePostMessageTasks]);\n\n  // 在组件内添加useEffect来监控SQL区域的显示条件\n  useEffect(() => {}, [sqlResult, regionOutputs.sql.hasContent, regionOutputs.analysis.streaming, regionOutputs.analysis.hasContent]);\n\n  // 添加MutationObserver来监控分析区域内容变化\n  useEffect(() => {\n    // 如果分析区域没有内容或已折叠，则不需要监控\n    if (!regionOutputs.analysis.hasContent || collapsedSections.analysis) {\n      return;\n    }\n\n    // 创建MutationObserver来监控内容变化\n    const observer = new MutationObserver(mutations => {\n      // 检查是否正在流式输出，只有流式输出时才自动滚动到底部\n      if (regionOutputs.analysis.streaming) {\n        scrollAnalysisAreaToBottom();\n      }\n    });\n\n    // 延迟一下再开始监控，确保元素已经渲染\n    setTimeout(() => {\n      // 查找分析区域容器\n      const analysisContainer = document.querySelector('.analysis-content-container');\n      if (analysisContainer) {\n        // 配置监视选项，监视子树变化和子节点变化\n        observer.observe(analysisContainer, {\n          childList: true,\n          subtree: true,\n          characterData: true\n        });\n\n        // 初始化时滚动到底部，但只在流式输出时\n        if (regionOutputs.analysis.streaming) {\n          scrollAnalysisAreaToBottom();\n        }\n      }\n    }, 100);\n\n    // 清理函数\n    return () => {\n      observer.disconnect();\n    };\n  }, [regionOutputs.analysis.hasContent, regionOutputs.analysis.merged, regionOutputs.analysis.streaming, collapsedSections.analysis]);\n\n  // 在 Text2SQL 组件内添加一个处理回车键的函数\n  const handleKeyDown = e => {\n    if (e.key === 'Enter' && !loading && query.trim() !== '') {\n      e.preventDefault();\n      handleStreamSearch();\n    }\n  };\n\n  // 混合检索相关函数\n  const handleShowExamples = async () => {\n    if (!query.trim() || !selectedConnectionId) {\n      return;\n    }\n    setHybridExamplesVisible(true);\n  };\n  const handleExampleSelect = example => {\n    // 将选中的示例应用到查询中\n    setQuery(example.qa_pair.question);\n    setHybridExamplesVisible(false);\n\n    // 可以选择自动执行查询\n    // handleStreamSearch();\n  };\n  const handleCloseExamples = () => {\n    setHybridExamplesVisible(false);\n  };\n\n  // 处理用户反馈提交\n  const handleFeedbackSubmit = async () => {\n    if (!userFeedback.message.trim()) return;\n    try {\n      console.log('发送用户反馈:', userFeedback.message);\n\n      // 获取当前反馈消息\n      const currentFeedback = userFeedback.message;\n\n      // 在前端添加分隔符\n      setRegionOutputs(prev => {\n        const updatedRegions = {\n          ...prev\n        };\n        const analysisRegion = updatedRegions.analysis;\n\n        // 构建分隔符标记\n        const separator = \"\\n\\n----------------------------\\n### 用户反馈：\" + currentFeedback + \"\\n----------------------------\\n\\n\";\n\n        // 检查是否已经存在相同的反馈内容\n        if (!analysisRegion.merged.includes(`用户反馈：${currentFeedback}`)) {\n          analysisRegion.merged += separator;\n        } else {\n          console.log('该反馈已存在，不重复添加');\n        }\n        return updatedRegions;\n      });\n\n      // 使用SSE发送反馈\n      const {\n        sendUserFeedback\n      } = await import('./api');\n      const sseInstance = getWebSocketInstance();\n      const sessionId = sseInstance.getCurrentSessionId();\n      console.log('📤 准备发送用户反馈:', {\n        sessionId,\n        feedback: currentFeedback,\n        hasSession: !!sessionId,\n        timestamp: new Date().toISOString()\n      });\n      if (sessionId) {\n        await sendUserFeedback(sessionId, currentFeedback, error => {\n          console.error('发送反馈失败:', error);\n          setError(`发送反馈失败: ${error.message}`);\n        });\n        console.log('✅ 反馈发送成功');\n      } else {\n        console.error('❌ 没有活动的SSE会话');\n        setError('没有活动的会话，无法发送反馈');\n      }\n\n      // 清空并隐藏反馈区\n      console.log('🔄 反馈提交完成，清空反馈状态');\n      setUserFeedback({\n        visible: false,\n        message: '',\n        promptMessage: ''\n      });\n\n      // 确保内容滚动到底部\n      setTimeout(() => {\n        scrollAnalysisAreaToBottom();\n      }, 200);\n    } catch (err) {\n      console.error('发送用户反馈出错:', err);\n      setError(`发送反馈失败: ${err}`);\n    }\n  };\n\n  // 处理用户反馈取消\n  const handleFeedbackCancel = () => {\n    try {\n      console.log('用户取消反馈');\n      const ws = getWebSocketInstance();\n      ws.sendMessage('取消操作');\n\n      // 清空并隐藏反馈区\n      setUserFeedback({\n        visible: false,\n        message: '',\n        promptMessage: ''\n      });\n    } catch (err) {\n      console.error('取消用户反馈出错:', err);\n      setError(`取消反馈失败: ${err}`);\n    }\n  };\n\n  // 处理用户同意操作\n  const handleFeedbackApprove = async () => {\n    try {\n      console.log('发送用户同意反馈: APPROVE');\n\n      // 在前端添加分隔符 - 确保只添加一次\n      setRegionOutputs(prev => {\n        const updatedRegions = {\n          ...prev\n        };\n        const analysisRegion = updatedRegions.analysis;\n\n        // 只有在当前内容中不包含分隔符时才添加\n        if (!analysisRegion.merged.includes(\"用户已同意操作\") && !analysisRegion.merged.includes(\"----------------------------\")) {\n          const separator = \"\\n\\n----------------------------\\n### 用户已同意操作\\n----------------------------\\n\\n\";\n          analysisRegion.merged += separator;\n        }\n        return updatedRegions;\n      });\n\n      // 使用SSE发送同意反馈\n      const {\n        sendUserApproval\n      } = await import('./api');\n      const sseInstance = getWebSocketInstance();\n      const sessionId = sseInstance.getCurrentSessionId();\n      if (sessionId) {\n        await sendUserApproval(sessionId, error => {\n          console.error('发送同意反馈失败:', error);\n          setError(`发送同意反馈失败: ${error.message}`);\n        });\n      } else {\n        console.error('没有活动的SSE会话');\n        setError('没有活动的会话，无法发送同意反馈');\n      }\n\n      // 清空并隐藏反馈区\n      setUserFeedback({\n        visible: false,\n        message: '',\n        promptMessage: ''\n      });\n\n      // 确保内容滚动到底部\n      setTimeout(() => {\n        scrollAnalysisAreaToBottom();\n      }, 200);\n    } catch (err) {\n      console.error('发送同意反馈出错:', err);\n      setError(`发送同意反馈失败: ${err}`);\n    }\n  };\n\n  // 滚动分析区域到底部的函数 - 优化版\n  const scrollAnalysisAreaToBottom = () => {\n    // 检查是否正在流式输出，只有流式输出时才自动滚动\n    if (!regionOutputs.analysis.streaming) {\n      return; // 如果不是流式输出，不进行自动滚动\n    }\n\n    // 首先尝试滚动分析区域容器\n    const analysisContainer = document.querySelector('.analysis-content-container');\n    if (analysisContainer && analysisContainer instanceof HTMLElement) {\n      // 检查用户是否手动滚动了内容\n      // 如果用户已经向上滚动了超过100像素，则不强制滚动到底部\n      const scrollPosition = analysisContainer.scrollTop;\n      const scrollHeight = analysisContainer.scrollHeight;\n      const clientHeight = analysisContainer.clientHeight;\n\n      // 如果用户已经向上滚动了超过200像素，则不强制滚动\n      if (scrollHeight - scrollPosition - clientHeight > 200) {\n        console.log('用户已手动滚动，不强制滚动到底部');\n        return;\n      }\n\n      // 滚动到底部\n      analysisContainer.scrollTop = analysisContainer.scrollHeight;\n\n      // 尝试常见的内容容器选择器\n      const analysisContent = document.querySelector('.analysis-content');\n      if (analysisContent && analysisContent instanceof HTMLElement) {\n        // 确保内容区域可见并可滚动\n        analysisContent.style.display = 'block';\n        analysisContent.style.overflow = 'auto';\n        analysisContent.style.minHeight = '100px';\n\n        // 延迟滚动以确保内容已经渲染\n        setTimeout(() => {\n          // 再次检查是否正在流式输出\n          if (regionOutputs.analysis.streaming) {\n            analysisContent.scrollTop = analysisContent.scrollHeight;\n          }\n        }, 100);\n      }\n    }\n  };\n\n  // 处理内容复制\n  const handleCopyContent = useCallback((content, regionId) => {\n    // 这里可以添加复制成功的提示\n    console.log(`${regionId} 内容已复制到剪贴板`);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `gemini-chat-layout ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"gemini-sidebar\",\n      children: /*#__PURE__*/_jsxDEV(ChatHistorySidebar, {\n        histories: chatHistories,\n        selectedHistoryId: selectedHistoryId,\n        onSelectHistory: handleSelectHistory,\n        onDeleteHistory: handleDeleteHistory,\n        onNewChat: createNewChatSession,\n        collapsed: sidebarCollapsed,\n        onToggleCollapse: handleSidebarToggle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2041,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2040,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"gemini-main-area\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gemini-chat-content\",\n        children: [connections.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '16px',\n            left: '24px',\n            zIndex: 10\n          },\n          children: /*#__PURE__*/_jsxDEV(ConnectionSelector, {\n            connections: connections,\n            selectedConnectionId: selectedConnectionId,\n            setSelectedConnectionId: setSelectedConnectionId,\n            loadingConnections: loadingConnections\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2064,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2058,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            overflow: 'auto',\n            minHeight: 0,\n            padding: '24px',\n            paddingTop: '80px' // 为数据库选择器留出空间\n          },\n          children: [/*#__PURE__*/_jsxDEV(ErrorMessage, {\n            error: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2082,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '16px'\n            },\n            children: [(regionOutputs.analysis.hasContent || regionOutputs.analysis.streaming) && /*#__PURE__*/_jsxDEV(RegionPanel, {\n              title: \"\\u67E5\\u8BE2\\u5206\\u6790\",\n              content: analysisResult || regionOutputs.analysis.merged,\n              isStreaming: regionOutputs.analysis.streaming,\n              hasContent: regionOutputs.analysis.hasContent,\n              region: \"analysis\",\n              onCopyContent: handleCopyContent\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2088,\n              columnNumber: 17\n            }, this), (regionOutputs.sql.hasContent || regionOutputs.sql.streaming) && /*#__PURE__*/_jsxDEV(RegionPanel, {\n              title: \"SQL\\u8BED\\u53E5\",\n              content: sqlResult || regionOutputs.sql.merged,\n              isStreaming: regionOutputs.sql.streaming,\n              hasContent: regionOutputs.sql.hasContent,\n              region: \"sql\",\n              onCopyContent: handleCopyContent\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2100,\n              columnNumber: 17\n            }, this), (explanationState.hasContent || explanationState.streaming) && /*#__PURE__*/_jsxDEV(RegionPanel, {\n              title: \"\\u8BED\\u53E5\\u89E3\\u91CA\",\n              content: explanationResult || '',\n              isStreaming: explanationState.streaming,\n              hasContent: explanationState.hasContent,\n              region: \"explanation\",\n              onCopyContent: handleCopyContent\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2112,\n              columnNumber: 17\n            }, this), (regionOutputs.data.hasContent || regionOutputs.data.streaming) && /*#__PURE__*/_jsxDEV(RegionPanel, {\n              title: \"\\u67E5\\u8BE2\\u7ED3\\u679C\",\n              content: dataResult ? JSON.stringify(dataResult, null, 2) : regionOutputs.data.merged,\n              isStreaming: regionOutputs.data.streaming,\n              hasContent: regionOutputs.data.hasContent,\n              region: \"data\",\n              onCopyContent: handleCopyContent,\n              dataResult: dataResult,\n              currentPage: currentPage,\n              pageSize: pageSize,\n              handlePageChange: handlePageChange,\n              getTotalPages: getTotalPages,\n              getCurrentPageData: getCurrentPageData,\n              convertToCSV: csvConverter\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2124,\n              columnNumber: 17\n            }, this), (regionOutputs.visualization.hasContent || regionOutputs.visualization.streaming) && /*#__PURE__*/_jsxDEV(RegionPanel, {\n              title: \"\\u6570\\u636E\\u53EF\\u89C6\\u5316\",\n              content: visualizationResult ? JSON.stringify(visualizationResult, null, 2) : regionOutputs.visualization.merged,\n              isStreaming: regionOutputs.visualization.streaming,\n              hasContent: regionOutputs.visualization.hasContent,\n              region: \"visualization\",\n              onCopyContent: handleCopyContent,\n              visualizationResult: visualizationResult,\n              dataResult: dataResult\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2143,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2085,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(UserFeedback, {\n            visible: userFeedback.visible,\n            message: userFeedback.message,\n            promptMessage: userFeedback.promptMessage,\n            setMessage: message => setUserFeedback(prev => ({\n              ...prev,\n              message\n            })),\n            handleSubmit: handleFeedbackSubmit,\n            handleApprove: handleFeedbackApprove,\n            handleCancel: handleFeedbackCancel\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2074,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2055,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gemini-input-area\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gemini-input-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gemini-input-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"left-controls\",\n              children: /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: hybridRetrievalEnabled ? \"智能推荐已启用\" : \"智能推荐已禁用\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `control-button ${hybridRetrievalEnabled ? 'active' : ''}`,\n                  onClick: () => setHybridRetrievalEnabled(!hybridRetrievalEnabled),\n                  disabled: loading,\n                  children: /*#__PURE__*/_jsxDEV(BulbOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2181,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2176,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2175,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: query,\n                onChange: e => setQuery(e.target.value),\n                onKeyDown: handleKeyDown,\n                placeholder: \"\\u5411\\u667A\\u80FD\\u52A9\\u624B\\u63D0\\u95EE\",\n                className: \"gemini-input\",\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2188,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-feedback-checkbox\",\n                children: /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"\\u542F\\u7528\\u7528\\u6237\\u53CD\\u9988\\u529F\\u80FD\",\n                  children: /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"inline-feedback-checkbox-label\",\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: userFeedbackEnabled,\n                      onChange: e => setUserFeedbackEnabled(e.target.checked),\n                      disabled: loading,\n                      className: \"inline-feedback-checkbox-input\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2202,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2201,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2200,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"right-controls\",\n              children: [hybridRetrievalEnabled && /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"\\u67E5\\u770B\\u667A\\u80FD\\u63A8\\u8350\\u793A\\u4F8B\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleShowExamples,\n                  disabled: loading || query.trim() === '' || !selectedConnectionId,\n                  className: \"control-button\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 11H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2225,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M15 11h4a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2226,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 2v20\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2227,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"8\",\n                      r: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2228,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2224,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2219,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"\\u53D1\\u9001\\u67E5\\u8BE2\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleStreamSearch,\n                  disabled: loading || query.trim() === '' || !selectedConnectionId,\n                  className: \"send-button\",\n                  children: loading ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"animate-spin\",\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                      className: \"opacity-25\",\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"4\",\n                      fill: \"none\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2243,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      className: \"opacity-75\",\n                      fill: \"currentColor\",\n                      d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2244,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2242,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(SendOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2247,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2236,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2235,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2172,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '8px',\n              color: '#ff4d4f',\n              fontSize: '12px',\n              textAlign: 'center'\n            },\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2255,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2171,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2170,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2053,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HybridExamplesPanel, {\n      query: query,\n      connectionId: selectedConnectionId,\n      schemaContext: schemaContext,\n      visible: hybridExamplesVisible,\n      onExampleSelect: handleExampleSelect,\n      onClose: handleCloseExamples\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2269,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 2038,\n    columnNumber: 5\n  }, this);\n}\n_s2(Text2SQL, \"y+zfihEossg0ro3I/7Mq93h1Vt0=\");\n_c1 = Text2SQL;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"Brain\");\n$RefreshReg$(_c2, \"Database\");\n$RefreshReg$(_c3, \"Search\");\n$RefreshReg$(_c4, \"BarChart\");\n$RefreshReg$(_c5, \"FileText\");\n$RefreshReg$(_c6, \"Code\");\n$RefreshReg$(_c7, \"CodeIcon\");\n$RefreshReg$(_c8, \"AlertCircle\");\n$RefreshReg$(_c9, \"WifiIcon\");\n$RefreshReg$(_c0, \"WebSocketStatusIndicator\");\n$RefreshReg$(_c1, \"Text2SQL\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "closeWebSocketConnection", "getWebSocketInstance", "WebSocketConnectionState", "globalWebSocketState", "globalWebSocketError", "getConnections", "UserFeedback", "ErrorMessage", "ConnectionSelector", "HybridExamplesPanel", "ChatHistorySidebar", "RegionPanel", "convertToCSV", "csvConverter", "<PERSON><PERSON><PERSON>", "BulbOutlined", "SendOutlined", "chatHistoryService", "jsxDEV", "_jsxDEV", "Brain", "props", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "children", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "Database", "cx", "cy", "rx", "ry", "_c2", "Search", "r", "_c3", "<PERSON><PERSON><PERSON>", "x1", "y1", "x2", "y2", "_c4", "FileText", "points", "_c5", "Code", "_c6", "CodeIcon", "_c7", "AlertCircle", "_c8", "WifiIcon", "_c9", "WebSocketStatusIndicator", "_s", "status", "setStatus", "intervalId", "setInterval", "clearInterval", "getStatusInfo", "CONNECTED", "statusClass", "text", "CONNECTING", "RECONNECTING", "ERROR", "DISCONNECTED", "statusInfo", "className", "_c0", "Text2SQL", "_s2", "query", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "processingSteps", "setProcessingSteps", "connections", "setConnections", "selectedConnectionId", "setSelectedConnectionId", "loadingConnections", "setLoadingConnections", "userFeedbackEnabled", "setUserFeedbackEnabled", "currentPage", "setCurrentPage", "pageSize", "setPageSize", "regionOutputs", "setRegionOutputs", "analysis", "merged", "messages", "<PERSON><PERSON><PERSON><PERSON>", "streaming", "sql", "explanation", "data", "visualization", "process", "sqlResult", "setSqlResult", "explanationResult", "setExplanationResult", "analysisResult", "setAnalysisResult", "dataResult", "setDataResult", "visualizationResult", "setVisualizationResult", "explanationState", "setExplanationState", "collapsedSections", "setCollapsedSections", "userFeedback", "setUserFeedback", "visible", "message", "promptMessage", "hybridExamplesVisible", "setHybridExamplesVisible", "similarExamples", "set<PERSON><PERSON>ilar<PERSON><PERSON><PERSON><PERSON>", "hybridRetrievalEnabled", "setHybridRetrievalEnabled", "schemaContext", "setSchemaContext", "chatHistories", "setChatHistories", "selectedHistoryId", "setSelectedHistoryId", "timelineMessages", "setTimelineMessages", "currentSessionId", "setCurrentSessionId", "sidebarCollapsed", "setSidebarCollapsed", "saved", "localStorage", "getItem", "JSON", "parse", "savedSessionIds", "setSavedSessionIds", "Set", "chartRef", "eventSourceRef", "processingStepIdRef", "generateId", "Date", "now", "Math", "random", "toString", "substr", "handleSidebarToggle", "newCollapsedState", "setItem", "stringify", "createNewChatSession", "sessionId", "resetProcessingState", "prev", "newSet", "delete", "console", "log", "addTimelineMessage", "type", "content", "metadata", "id", "timestamp", "updateTimelineMessage", "messageId", "updates", "map", "msg", "saveChatHistory", "trim", "history", "title", "length", "substring", "response", "connectionId", "existing", "find", "h", "_history$connectionId", "saveRequest", "session_id", "connection_id", "undefined", "saveChatHistoryViaSSE", "loadChatHistories", "getChatHistories", "histories", "sessions", "session", "handleSelectHistory", "historyId", "getChatHistory", "created_at", "hasAnalysis", "hasSql", "hasExplanation", "hasData", "hasVisualization", "push", "getTime", "region", "source", "isSQL", "isVisualization", "handleDeleteHistory", "confirmed", "window", "confirm", "deleteChatHistory", "filter", "alert", "fetchConnections", "shouldSave", "has", "userMessage", "saveTimeout", "setTimeout", "add", "clearTimeout", "toggleCollapse", "section", "handleFinalSql", "finalResult", "handleFinalExplanation", "<PERSON><PERSON><PERSON><PERSON>", "currentExplanationResult", "handleFinalData", "handleFinalVisualization", "handleFinalAnalysis", "handleResult", "allRegionsCompleted", "Object", "values", "every", "validExplanation", "updated", "keys", "for<PERSON>ach", "key", "results", "visualization_type", "visualization_config", "config", "handleError", "errorMessage", "includes", "handlePageChange", "pageNumber", "getTotalPages", "ceil", "getCurrentPageData", "startIndex", "endIndex", "slice", "current", "close", "err", "handleStreamSearch", "sse", "connected", "connect", "<PERSON><PERSON><PERSON><PERSON>", "userMessageId", "messageCache", "clear", "analysisContainer", "document", "querySelector", "style", "display", "minHeight", "scrollIntoView", "behavior", "block", "warn", "contentArea", "sendSSEText2SQLRequest", "handleMessage", "Error", "dataset", "rendered", "then", "ChartModule", "Chart", "default", "canvas", "chartInstance", "<PERSON><PERSON><PERSON>", "destroy", "e", "ctx", "getContext", "chartType", "prepareChartConfig", "catch", "labels", "item", "xField", "xAxis", "yField", "yAxis", "dataPoints", "datasets", "label", "backgroundColor", "borderColor", "borderWidth", "options", "responsive", "maintainAspectRatio", "plugins", "tooltip", "enabled", "legend", "updateRegionOutputs", "isFinal", "updatedRegions", "regionData", "newContentLength", "totalLength", "newContentPreview", "totalContentPreview", "newContentHasNewlines", "newContentHasSpaces", "rawNewContent", "max", "isAppending", "handlePostMessageTasks", "step", "toISOString", "contentHash", "size", "entries", "Array", "from", "entry", "contentLength", "contentPreview", "hasNewlines", "hasSpaces", "rawContent", "is_final", "prevContent", "observer", "MutationObserver", "mutations", "scrollAnalysisAreaToBottom", "observe", "childList", "subtree", "characterData", "disconnect", "handleKeyDown", "preventDefault", "handleShowExamples", "handleExampleSelect", "example", "qa_pair", "question", "handleCloseExamples", "handleFeedbackSubmit", "currentFeedback", "analysisRegion", "separator", "sendUserFeedback", "sseInstance", "getCurrentSessionId", "feedback", "hasSession", "handleFeedbackCancel", "ws", "sendMessage", "handleFeedbackApprove", "sendUserApproval", "HTMLElement", "scrollPosition", "scrollTop", "scrollHeight", "clientHeight", "analysisContent", "overflow", "handleCopyContent", "regionId", "onSelectHistory", "onDeleteHistory", "onNewChat", "collapsed", "onToggleCollapse", "position", "top", "left", "zIndex", "flex", "padding", "paddingTop", "flexDirection", "gap", "isStreaming", "onCopyContent", "setMessage", "handleSubmit", "handleApprove", "handleCancel", "onClick", "disabled", "value", "onChange", "target", "onKeyDown", "placeholder", "checked", "marginTop", "color", "fontSize", "textAlign", "onExampleSelect", "onClose", "_c1", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/pages/text2sql/page.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react'\nimport ReactDOM from 'react-dom'\nimport { Link } from 'react-router-dom'\nimport '../../styles/Text2SQL.css'\nimport '../../styles/Text2SQLTabs.css'\nimport '../../styles/ChatStyle.css'\nimport '../../styles/HybridExamples.css'\nimport '../../styles/EnterpriseChatStyle.css'\nimport '../../styles/TimelineChatStyle.css'\nimport {\n  StreamResponseMessage,\n  FinalVisualizationData,\n  closeWebSocketConnection,\n  getWebSocketInstance,\n  WebSocketConnectionState,\n  globalWebSocketState,\n  globalWebSocketError,\n  getConnections\n} from './api'\nimport { Text2SQLResponse } from './types'\n\n// 导入组件\nimport TabPanel from './components/TabPanel'\nimport Tabs from './components/Tabs'\nimport AnalysisTab from './components/AnalysisTab'\nimport SQLTab from './components/SQLTab'\nimport VisualizationTab from './components/VisualizationTab'\nimport ControlPanel from './components/ControlPanel'\nimport UserFeedback from './components/UserFeedback'\nimport ErrorMessage from './components/ErrorMessage'\nimport ConnectionSelector from './components/ConnectionSelector'\nimport HybridExamplesPanel from './components/HybridExamplesPanel'\nimport ChatHistorySidebar from './components/ChatHistorySidebar'\nimport TimelineChat from './components/TimelineChat'\nimport RegionPanel from './components/RegionPanel'\n// 导入工具函数\nimport { convertToCSV as csvConverter, FormattedOutput as OutputFormatter } from './utils'\n\n// 导入混合检索相关\nimport { hybridQAService, enhancedText2SQLService } from '../../services/hybridQA'\nimport type { SimilarQAPair } from '../../types/hybridQA'\nimport { Switch, Tooltip } from 'antd'\nimport { BulbOutlined, SendOutlined } from '@ant-design/icons'\n// 导入聊天历史服务\nimport { chatHistoryService } from '../../services/chatHistoryService'\nimport type { SaveChatHistoryRequest } from '../../services/chatHistoryService'\n// 导入共享类型\nimport type { ChatHistory, TimelineMessage, Connection } from '../../types/chat'\n\n\n\n\n// 内联定义图标组件\nconst Brain = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    {...props}\n  >\n    <path d=\"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z\" />\n    <path d=\"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z\" />\n  </svg>\n)\n\nconst Database = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    {...props}\n  >\n    <ellipse cx=\"12\" cy=\"5\" rx=\"9\" ry=\"3\"></ellipse>\n    <path d=\"M21 12c0 1.66-4 3-9 3s-9-1.34-9-3\"></path>\n    <path d=\"M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5\"></path>\n  </svg>\n)\n\nconst Search = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    {...props}\n  >\n    <circle cx=\"11\" cy=\"11\" r=\"8\"></circle>\n    <path d=\"m21 21-4.3-4.3\"></path>\n  </svg>\n)\n\nconst BarChart = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    {...props}\n  >\n    <line x1=\"12\" y1=\"20\" x2=\"12\" y2=\"10\"></line>\n    <line x1=\"18\" y1=\"20\" x2=\"18\" y2=\"4\"></line>\n    <line x1=\"6\" y1=\"20\" x2=\"6\" y2=\"16\"></line>\n  </svg>\n)\n\nconst FileText = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    {...props}\n  >\n    <path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"></path>\n    <polyline points=\"14 2 14 8 20 8\"></polyline>\n    <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"></line>\n    <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"></line>\n    <line x1=\"10\" y1=\"9\" x2=\"8\" y2=\"9\"></line>\n  </svg>\n)\n\nconst Code = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    {...props}\n  >\n    <polyline points=\"16 18 22 12 16 6\"></polyline>\n    <polyline points=\"8 6 2 12 8 18\"></polyline>\n  </svg>\n)\n\nconst CodeIcon = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    {...props}\n  >\n    <path d=\"m18 16 4-4-4-4\"></path>\n    <path d=\"m6 8-4 4 4 4\"></path>\n    <path d=\"m14.5 4-5 16\"></path>\n  </svg>\n)\n\n// 导入错误图标\nconst AlertCircle = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    {...props}\n  >\n    <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n    <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"12\"></line>\n    <line x1=\"12\" y1=\"16\" x2=\"12\" y2=\"16\"></line>\n  </svg>\n)\n\n// 添加连接状态图标\nconst WifiIcon = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    {...props}\n  >\n    <path d=\"M5 12.55a11 11 0 0 1 14.08 0\"></path>\n    <path d=\"M1.42 9a16 16 0 0 1 21.16 0\"></path>\n    <path d=\"M8.53 16.11a6 6 0 0 1 6.95 0\"></path>\n    <line x1=\"12\" y1=\"20\" x2=\"12.01\" y2=\"20\"></line>\n  </svg>\n)\n\n// WebSocket连接状态指示器组件\nconst WebSocketStatusIndicator = () => {\n  const [status, setStatus] = useState(globalWebSocketState);\n\n  useEffect(() => {\n    // 创建一个定时器，定期检查WebSocket状态\n    const intervalId = setInterval(() => {\n      setStatus(globalWebSocketState);\n    }, 500); // 每500ms检查一次\n\n    return () => clearInterval(intervalId);\n  }, []);\n\n  // 根据状态返回不同的样式类和文本\n  const getStatusInfo = () => {\n    switch (status) {\n      case WebSocketConnectionState.CONNECTED:\n        return { statusClass: 'websocket-status-connected', text: '已连接' };\n      case WebSocketConnectionState.CONNECTING:\n        return { statusClass: 'websocket-status-connecting', text: '连接中' };\n      case WebSocketConnectionState.RECONNECTING:\n        return { statusClass: 'websocket-status-reconnecting', text: '重连中' };\n      case WebSocketConnectionState.ERROR:\n        return { statusClass: 'websocket-status-error', text: '连接错误' };\n      case WebSocketConnectionState.DISCONNECTED:\n        return { statusClass: 'websocket-status-disconnected', text: '未连接' };\n      default:\n        return { statusClass: 'websocket-status-disconnected', text: '未知状态' };\n    }\n  };\n\n  const statusInfo = getStatusInfo();\n\n  return (\n    <div className={`websocket-status ${statusInfo.statusClass}`}>\n      <div className=\"websocket-status-dot\"></div>\n      <span>{statusInfo.text}</span>\n    </div>\n  );\n}\n\n// 定义处理步骤类型\ntype ProcessingStep = {\n  id: number;\n  message: string;\n  timestamp: Date;\n  source: string;\n};\n\n// 定义用户反馈状态类型\ntype UserFeedbackState = {\n  visible: boolean;\n  message: string;\n  promptMessage: string;\n};\n\n// 修改RegionOutput类型\ntype RegionOutputs = {\n  analysis: {\n    merged: string;\n    messages: StreamResponseMessage[];\n    hasContent: boolean;\n    streaming: boolean;\n  };\n  sql: {\n    merged: string;\n    messages: StreamResponseMessage[];\n    hasContent: boolean;\n    streaming: boolean;\n  };\n  explanation: {\n    merged: string;\n    messages: StreamResponseMessage[];\n    hasContent: boolean;\n    streaming: boolean;\n  };\n  data: {\n    merged: string;\n    messages: StreamResponseMessage[];\n    hasContent: boolean;\n    streaming: boolean;\n  };\n  visualization: {\n    merged: string;\n    messages: StreamResponseMessage[];\n    hasContent: boolean;\n    streaming: boolean;\n  };\n  process: {\n    merged: string;\n    messages: StreamResponseMessage[];\n    hasContent: boolean;\n    streaming: boolean;\n  };\n};\n\n\n\nexport default function Text2SQL() {\n  const [query, setQuery] = useState<string>('');\n  const [loading, setLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const [processingSteps, setProcessingSteps] = useState<ProcessingStep[]>([]);\n\n\n\n  // 数据库连接状态\n  const [connections, setConnections] = useState<Connection[]>([]);\n  const [selectedConnectionId, setSelectedConnectionId] = useState<number | null>(null);\n  const [loadingConnections, setLoadingConnections] = useState<boolean>(false);\n\n  // 用户反馈启用状态\n  const [userFeedbackEnabled, setUserFeedbackEnabled] = useState<boolean>(false);\n\n  // 添加分页状态\n  const [currentPage, setCurrentPage] = useState(1)\n  const [pageSize, setPageSize] = useState(10)\n\n  // 按区域分类的流式输出\n  const [regionOutputs, setRegionOutputs] = useState<RegionOutputs>({\n    analysis: {\n      merged: '',\n      messages: [],\n      hasContent: false,\n      streaming: false\n    },\n    sql: {\n      merged: '',\n      messages: [],\n      hasContent: false,\n      streaming: false\n    },\n    explanation: {\n      merged: '',\n      messages: [],\n      hasContent: false,\n      streaming: false\n    },\n    data: {\n      merged: '',\n      messages: [],\n      hasContent: false,\n      streaming: false\n    },\n    visualization: {\n      merged: '',\n      messages: [],\n      hasContent: false,\n      streaming: false\n    },\n    process: {\n      merged: '',\n      messages: [],\n      hasContent: false,\n      streaming: false\n    }\n  })\n\n  // 最终结果的状态\n  const [sqlResult, setSqlResult] = useState<string | null>(null)\n  const [explanationResult, setExplanationResult] = useState<string | null>(null)\n  const [analysisResult, setAnalysisResult] = useState<string | null>(null) // 添加分析结果状态\n  const [dataResult, setDataResult] = useState<any[] | null>(null)\n  const [visualizationResult, setVisualizationResult] = useState<{\n    type: string;\n    config: any;\n  } | null>(null)\n\n  // 解释区域的独立状态管理\n  const [explanationState, setExplanationState] = useState({\n    hasContent: false,\n    streaming: false\n  })\n\n  // 区域折叠状态\n  const [collapsedSections, setCollapsedSections] = useState({\n    analysis: false,\n    sql: false,\n    explanation: false,\n    data: false,\n    visualization: false,\n    process: true // 默认折叠处理过程\n  })\n\n  // 添加用户反馈状态\n  const [userFeedback, setUserFeedback] = useState<UserFeedbackState>({\n    visible: false,\n    message: '',\n    promptMessage: ''\n  });\n\n  // 混合检索相关状态\n  const [hybridExamplesVisible, setHybridExamplesVisible] = useState(false);\n  const [similarExamples, setSimilarExamples] = useState<SimilarQAPair[]>([]);\n  const [hybridRetrievalEnabled, setHybridRetrievalEnabled] = useState(true);\n  const [schemaContext, setSchemaContext] = useState<any>(null);\n\n  // 聊天历史和时间轴状态\n  const [chatHistories, setChatHistories] = useState<ChatHistory[]>([]);\n  const [selectedHistoryId, setSelectedHistoryId] = useState<string | null>(null);\n  const [timelineMessages, setTimelineMessages] = useState<TimelineMessage[]>([]);\n  const [currentSessionId, setCurrentSessionId] = useState<string>('');\n\n  // 侧边栏折叠状态 - 默认折叠，支持 localStorage 持久化\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(() => {\n    const saved = localStorage.getItem('text2sql-sidebar-collapsed');\n    return saved !== null ? JSON.parse(saved) : true; // 默认折叠\n  });\n\n  const [savedSessionIds, setSavedSessionIds] = useState<Set<string>>(new Set()); // 已保存的会话ID\n\n  // 图表引用\n  const chartRef = useRef<HTMLCanvasElement>(null)\n  // 存储EventSource实例以便在需要时关闭\n  const eventSourceRef = useRef<EventSource | null>(null)\n\n  // 在组件顶部添加计数器引用\n  const processingStepIdRef = useRef(1)\n\n  // 生成唯一ID的函数\n  const generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n\n  // 处理侧边栏切换并持久化到 localStorage\n  const handleSidebarToggle = () => {\n    const newCollapsedState = !sidebarCollapsed;\n    setSidebarCollapsed(newCollapsedState);\n    localStorage.setItem('text2sql-sidebar-collapsed', JSON.stringify(newCollapsedState));\n  };\n\n  // 创建新的聊天会话\n  const createNewChatSession = () => {\n    const sessionId = generateId();\n    setCurrentSessionId(sessionId);\n    setSelectedHistoryId(null);\n    setTimelineMessages([]);\n    resetProcessingState();\n    // 清除保存状态，允许新会话保存\n    setSavedSessionIds(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(sessionId); // 确保新会话ID不在已保存列表中\n      return newSet;\n    });\n    console.log('🆕 创建新聊天会话:', sessionId);\n    return sessionId;\n  };\n\n  // 添加时间轴消息\n  const addTimelineMessage = (\n    type: 'user' | 'assistant' | 'system',\n    content: string,\n    metadata?: any\n  ) => {\n    const message: TimelineMessage = {\n      id: generateId(),\n      type,\n      content,\n      timestamp: new Date(),\n      status: type === 'user' ? 'sent' : 'streaming',\n      metadata\n    };\n\n    setTimelineMessages(prev => [...prev, message]);\n    return message.id;\n  };\n\n  // 更新时间轴消息状态\n  const updateTimelineMessage = (\n    messageId: string,\n    updates: Partial<TimelineMessage>\n  ) => {\n    setTimelineMessages(prev =>\n      prev.map(msg =>\n        msg.id === messageId ? { ...msg, ...updates } : msg\n      )\n    );\n  };\n\n  // 保存聊天历史\n  const saveChatHistory = async (query: string) => {\n    if (!currentSessionId || !query.trim()) return;\n\n    const history: ChatHistory = {\n      id: currentSessionId,\n      title: query.length > 50 ? query.substring(0, 50) + '...' : query,\n      timestamp: new Date(),\n      query,\n      response: {\n        analysis: analysisResult || regionOutputs.analysis.merged,\n        sql: sqlResult || '',\n        explanation: explanationResult || '',\n        data: dataResult || [],\n        visualization: visualizationResult\n      },\n      connectionId: selectedConnectionId\n    };\n\n    // 更新本地状态\n    setChatHistories(prev => {\n      const existing = prev.find(h => h.id === currentSessionId);\n      if (existing) {\n        return prev.map(h => h.id === currentSessionId ? history : h);\n      }\n      return [history, ...prev];\n    });\n\n    // 保存到数据库\n    try {\n      const saveRequest: SaveChatHistoryRequest = {\n        session_id: currentSessionId,\n        title: history.title,\n        query: history.query,\n        response: history.response,\n        connection_id: history.connectionId ?? undefined\n      };\n\n      await chatHistoryService.saveChatHistoryViaSSE(saveRequest);\n      console.log('聊天历史保存成功:', currentSessionId);\n    } catch (error) {\n      console.error('保存聊天历史失败:', error);\n      // 不影响用户体验，只记录错误\n    }\n  };\n\n  // 加载聊天历史列表\n  const loadChatHistories = async () => {\n    try {\n      console.log('🔄 开始加载聊天历史，连接ID:', selectedConnectionId);\n      const response = await chatHistoryService.getChatHistories(\n        0,\n        50,\n        selectedConnectionId || undefined\n      );\n      console.log('📥 后端响应:', response);\n\n      const histories: ChatHistory[] = response.sessions.map(session => ({\n        id: session.id,\n        title: session.title,\n        timestamp: new Date(session.timestamp),\n        query: session.query,\n        response: session.response,\n        connectionId: session.connection_id || null\n      }));\n\n      setChatHistories(histories);\n      console.log('✅ 聊天历史加载成功，数量:', histories.length);\n      console.log('📋 历史记录详情:', histories);\n    } catch (error) {\n      console.error('❌ 加载聊天历史失败:', error);\n    }\n  };\n\n  // 选择历史记录\n  const handleSelectHistory = async (historyId: string) => {\n    try {\n      console.log('🔍 选择历史记录:', historyId);\n\n      // 首先尝试从本地状态获取\n      let history = chatHistories.find(h => h.id === historyId);\n      console.log('📋 本地历史记录:', history ? '找到' : '未找到');\n\n      // 如果本地没有，从数据库获取\n      if (!history) {\n        console.log('🌐 从数据库获取历史记录...');\n        const response = await chatHistoryService.getChatHistory(historyId);\n        history = {\n          id: response.session_id,\n          title: response.title,\n          timestamp: new Date(response.created_at),\n          query: response.query,\n          response: response.response,\n          connectionId: response.connection_id || null\n        };\n        console.log('📥 数据库历史记录:', history);\n      }\n\n      if (!history) {\n        console.error('❌ 未找到历史记录:', historyId);\n        return;\n      }\n\n      console.log('✅ 开始恢复历史记录:', {\n        id: history.id,\n        title: history.title,\n        hasAnalysis: !!history.response.analysis,\n        hasSql: !!history.response.sql,\n        hasExplanation: !!history.response.explanation,\n        hasData: !!(history.response.data && history.response.data.length > 0),\n        hasVisualization: !!history.response.visualization\n      });\n\n      setSelectedHistoryId(historyId);\n      setCurrentSessionId(historyId);\n\n      // 恢复历史数据\n      setSqlResult(history.response.sql);\n      setExplanationResult(history.response.explanation);\n      setAnalysisResult(history.response.analysis); // 恢复分析结果\n      setDataResult(history.response.data);\n      setVisualizationResult(history.response.visualization);\n\n      // 重要：恢复解释区域的独立状态\n      setExplanationState({\n        hasContent: !!history.response.explanation,\n        streaming: false\n      });\n\n      // 重要：更新regionOutputs状态，确保右侧内容区域显示\n      setRegionOutputs({\n        analysis: {\n          merged: history.response.analysis || '',\n          messages: [],\n          hasContent: !!history.response.analysis,\n          streaming: false\n        },\n        sql: {\n          merged: history.response.sql || '',\n          messages: [],\n          hasContent: !!history.response.sql,\n          streaming: false\n        },\n        explanation: {\n          merged: history.response.explanation || '',\n          messages: [],\n          hasContent: !!history.response.explanation,\n          streaming: false\n        },\n        data: {\n          merged: history.response.data && history.response.data.length > 0 ?\n                  `查询返回了 ${history.response.data.length} 条数据记录` : '',\n          messages: [],\n          hasContent: !!(history.response.data && history.response.data.length > 0),\n          streaming: false\n        },\n        visualization: {\n          merged: history.response.visualization ?\n                  `生成了 ${history.response.visualization.type || '图表'} 类型的可视化` : '',\n          messages: [],\n          hasContent: !!history.response.visualization,\n          streaming: false\n        },\n        process: {\n          merged: '',\n          messages: [],\n          hasContent: false,\n          streaming: false\n        }\n      });\n\n      console.log('🔄 regionOutputs状态已更新，右侧内容应该显示');\n\n      // 重建时间轴消息\n      const messages: TimelineMessage[] = [\n        {\n        id: generateId(),\n        type: 'user',\n        content: history.query,\n        timestamp: history.timestamp,\n        status: 'sent'\n      }\n    ];\n\n    if (history.response.analysis) {\n      messages.push({\n        id: generateId(),\n        type: 'assistant',\n        content: history.response.analysis,\n        timestamp: new Date(history.timestamp.getTime() + 1000),\n        status: 'completed',\n        metadata: { region: 'analysis', source: '查询分析智能体' }\n      });\n    }\n\n    if (history.response.sql) {\n      messages.push({\n        id: generateId(),\n        type: 'assistant',\n        content: history.response.sql,\n        timestamp: new Date(history.timestamp.getTime() + 2000),\n        status: 'completed',\n        metadata: { region: 'sql', source: 'SQL生成智能体', isSQL: true }\n      });\n    }\n\n    if (history.response.explanation) {\n      messages.push({\n        id: generateId(),\n        type: 'assistant',\n        content: history.response.explanation,\n        timestamp: new Date(history.timestamp.getTime() + 3000),\n        status: 'completed',\n        metadata: { region: 'explanation', source: 'SQL解释智能体' }\n      });\n    }\n\n    if (history.response.data && history.response.data.length > 0) {\n      messages.push({\n        id: generateId(),\n        type: 'assistant',\n        content: `查询返回了 ${history.response.data.length} 条数据记录`,\n        timestamp: new Date(history.timestamp.getTime() + 4000),\n        status: 'completed',\n        metadata: { region: 'data', source: '数据查询智能体' }\n      });\n    }\n\n    if (history.response.visualization) {\n      messages.push({\n        id: generateId(),\n        type: 'assistant',\n        content: `生成了 ${history.response.visualization.type} 类型的可视化图表`,\n        timestamp: new Date(history.timestamp.getTime() + 5000),\n        status: 'completed',\n        metadata: { region: 'visualization', source: '可视化推荐智能体', isVisualization: true }\n      });\n    }\n\n    setTimelineMessages(messages);\n    console.log('✅ 历史记录恢复完成，时间轴消息数量:', messages.length);\n    } catch (error) {\n      console.error('❌ 选择历史记录失败:', error);\n    }\n  };\n\n  // 删除聊天历史\n  const handleDeleteHistory = async (historyId: string) => {\n    try {\n      console.log('🗑️ 删除聊天历史:', historyId);\n\n      // 显示确认对话框\n      const confirmed = window.confirm('确定要删除这条聊天记录吗？此操作无法撤销。');\n      if (!confirmed) {\n        return;\n      }\n\n      // 调用删除API\n      await chatHistoryService.deleteChatHistory(historyId);\n\n      // 从本地状态中移除\n      setChatHistories(prev => prev.filter(h => h.id !== historyId));\n\n      // 如果删除的是当前选中的历史记录，则创建新会话\n      if (selectedHistoryId === historyId) {\n        createNewChatSession();\n      }\n\n      console.log('✅ 聊天历史删除成功');\n    } catch (error) {\n      console.error('❌ 删除聊天历史失败:', error);\n      alert('删除失败，请稍后重试');\n    }\n  };\n\n  // 获取数据库连接列表\n  useEffect(() => {\n    const fetchConnections = async () => {\n      try {\n        setLoadingConnections(true);\n        const response = await getConnections();\n        setConnections(response.data);\n\n        // 如果有连接，默认选择第一个\n        if (response.data.length > 0) {\n          setSelectedConnectionId(response.data[0].id);\n        }\n      } catch (error) {\n        console.error('获取数据库连接失败:', error);\n        setError('获取数据库连接失败，请检查网络连接或联系管理员');\n      } finally {\n        setLoadingConnections(false);\n      }\n    };\n\n    fetchConnections();\n\n    // 初始化一个新的聊天会话\n    createNewChatSession();\n\n    // 加载聊天历史\n    loadChatHistories();\n  }, []); // 仅在组件挂载时执行一次\n\n  // 当选择的连接改变时，重新加载聊天历史\n  useEffect(() => {\n    if (selectedConnectionId) {\n      loadChatHistories();\n    }\n  }, [selectedConnectionId]);\n\n  // 监听查询完成状态，自动保存聊天历史\n  useEffect(() => {\n    const shouldSave =\n      !loading && // 不在加载中\n      currentSessionId && // 有会话ID\n      !savedSessionIds.has(currentSessionId) && // 该会话还未保存\n      sqlResult && // 有SQL结果\n      dataResult && // 有数据结果\n      timelineMessages.length > 0; // 有时间轴消息\n\n    if (shouldSave) {\n      const userMessage = timelineMessages.find(msg => msg.type === 'user');\n      if (userMessage) {\n        console.log('🔄 检测到查询完成，自动保存聊天历史');\n\n        // 延迟保存，确保所有状态都已更新\n        const saveTimeout = setTimeout(async () => {\n          try {\n            await saveChatHistory(userMessage.content);\n            await loadChatHistories();\n            // 标记该会话已保存\n            setSavedSessionIds(prev => new Set(prev).add(currentSessionId));\n            console.log('✅ 聊天历史自动保存成功');\n          } catch (error) {\n            console.error('❌ 聊天历史自动保存失败:', error);\n          }\n        }, 2000); // 2秒延迟\n\n        return () => clearTimeout(saveTimeout);\n      }\n    }\n  }, [loading, currentSessionId, sqlResult, dataResult, timelineMessages, savedSessionIds]);\n\n  // 切换折叠状态\n  const toggleCollapse = (section: string) => {\n    setCollapsedSections(prev => ({\n      ...prev,\n      [section]: !prev[section as keyof typeof prev]\n    }));\n  }\n\n  // 处理最终SQL结果\n  const handleFinalSql = (sql: string) => {\n    console.log('收到最终SQL结果，关闭流式状态', sql);\n    // 标记SQL区域流式输出结束\n    setRegionOutputs(prev => ({\n      ...prev,\n      sql: {\n        ...prev.sql,\n        streaming: false,\n        finalResult: sql,\n        hasContent: true\n      }\n    }));\n    setSqlResult(sql);\n  };\n\n  // 处理最终解释结果\n  const handleFinalExplanation = (explanation: string) => {\n    console.log('收到最终解释结果，关闭流式状态', {\n      explanationLength: explanation ? explanation.length : 0,\n      currentExplanationResult: explanationResult ? `当前内容长度 ${explanationResult.length}` : '无当前内容'\n    });\n\n    // 标记解释区域流式输出结束\n    setRegionOutputs(prev => {\n      return {\n        ...prev,\n        explanation: {\n          ...prev.explanation,\n          streaming: false,\n          hasContent: true  // 确保区域被标记为有内容\n        }\n      };\n    });\n\n    // 解释内容已生成完成\n    console.log('SQL解释内容已生成完成');\n  };\n\n  // 处理最终数据结果\n  const handleFinalData = (data: any[]) => {\n    console.log('收到最终数据结果，关闭流式状态');\n    // 标记数据区域流式输出结束\n    setRegionOutputs(prev => ({\n      ...prev,\n      data: {\n        ...prev.data,\n        streaming: false,\n        hasContent: true  // 确保区域被标记为有内容\n      }\n    }));\n    setDataResult(data);\n  }\n\n  // 处理最终可视化结果\n  const handleFinalVisualization = async (visualization: FinalVisualizationData) => {\n    console.log('🎨 handleFinalVisualization被调用，可视化结果:', visualization);\n    console.log('📊 当前数据结果:', dataResult);\n    console.log('收到最终可视化结果，关闭流式状态');\n\n    // 标记可视化区域流式输出结束\n    setRegionOutputs(prev => ({\n      ...prev,\n      visualization: {\n        ...prev.visualization,\n        streaming: false,\n        hasContent: true  // 确保区域被标记为有内容\n      }\n    }));\n\n    // 设置可视化结果\n    setVisualizationResult(visualization);\n    console.log('✅ 可视化结果已设置:', visualization);\n\n    // 在收到可视化结果后直接设置 loading 状态为 false，使分析按钮可用\n    // 只有当region='visualization'的消息且is_final=true时，才设置分析按钮为可用状态\n    setLoading(false);\n    console.log('可视化数据已准备就绪，分析按钮恢复可用');\n\n    // 可视化数据已准备就绪，触发保存聊天历史\n    console.log('🔄 可视化数据已准备就绪，准备保存聊天历史');\n\n    // 延迟保存，确保所有数据都已设置完成\n    setTimeout(async () => {\n      if (currentSessionId) {\n        // 获取当前用户查询（从时间轴消息中获取）\n        const userMessage = timelineMessages.find(msg => msg.type === 'user');\n        if (userMessage) {\n          try {\n            await saveChatHistory(userMessage.content);\n            // 保存后立即刷新历史列表\n            await loadChatHistories();\n            console.log('聊天历史已保存并刷新列表（从可视化完成触发）');\n          } catch (error) {\n            console.error('保存聊天历史失败:', error);\n          }\n        }\n      }\n    }, 500);\n  }\n\n  // 处理最终分析结果\n  const handleFinalAnalysis = (analysis: string) => {\n    console.log('收到最终分析结果，关闭流式状态');\n    // 标记分析区域流式输出结束\n    setRegionOutputs(prev => ({\n      ...prev,\n      analysis: {\n        ...prev.analysis,\n        streaming: false,\n        hasContent: true\n      }\n    }));\n  }\n\n  // 处理最终结果\n  const handleResult = (finalResult: Text2SQLResponse) => {\n    console.log('🎯 handleResult被调用，最终结果:', finalResult);\n    setError(null); // 清除错误\n\n    // 检查所有区域的流式输出是否都已结束\n    const allRegionsCompleted = Object.values(regionOutputs).every(region => !region.streaming);\n\n    // 检查并处理解释结果\n    const validExplanation = finalResult.explanation &&\n                            typeof finalResult.explanation === 'string' &&\n                            finalResult.explanation.trim() ?\n                            finalResult.explanation : null;\n\n    // 保存聊天历史（在处理完成后）\n    setTimeout(async () => {\n      if (currentSessionId) {\n        // 获取当前用户查询（从时间轴消息中获取）\n        const userMessage = timelineMessages.find(msg => msg.type === 'user');\n        if (userMessage) {\n          await saveChatHistory(userMessage.content);\n          // 保存后立即刷新历史列表\n          await loadChatHistories();\n          console.log('聊天历史已保存并刷新列表');\n        }\n      }\n    }, 1000);\n\n    // 标记所有区域流式输出结束并设置hasContent\n    setRegionOutputs(prev => {\n      const updated = { ...prev };\n      Object.keys(updated).forEach(key => {\n        const region = updated[key as keyof typeof updated];\n        region.streaming = false;\n\n        // 根据最终结果设置hasContent\n        if (key === 'sql' && finalResult.sql) {\n          region.hasContent = true;\n        }\n        if (key === 'explanation' && validExplanation) {\n          region.hasContent = true;\n          region.merged = validExplanation; // 确保merged字段有正确的内容\n          console.log('设置explanation区域merged字段:', validExplanation.substring(0, 50) + '...');\n        }\n        if (key === 'data' && finalResult.results && finalResult.results.length > 0) {\n          region.hasContent = true;\n        }\n        if (key === 'visualization' && (finalResult.visualization_type || finalResult.visualization_config)) {\n          region.hasContent = true;\n        }\n      });\n      return updated;\n    });\n\n    // 设置最终结果的所有部分\n    setSqlResult(finalResult.sql);\n\n    // 不在这里设置解释内容，而是依赖流式消息的累加逻辑\n    console.log('保留现有解释内容，当前长度:', explanationResult ? explanationResult.length : 0);\n\n    setDataResult(finalResult.results);\n    setVisualizationResult({\n      type: finalResult.visualization_type,\n      config: finalResult.visualization_config\n    });\n\n    // 打印日志，帮助调试\n    console.log('设置最终结果:', {\n      sql: finalResult.sql ? finalResult.sql.substring(0, 50) + '...' : null,\n      explanation: validExplanation ? validExplanation.substring(0, 50) + '...' : null,\n      results: finalResult.results ? `${finalResult.results.length} 条结果` : null,\n      visualization: finalResult.visualization_type\n    });\n\n    // 如果有解释内容，记录日志\n    if (validExplanation) {\n      console.log('有解释内容，内容长度:', validExplanation.length);\n    }\n\n    // 只有当收到region='visualization'的消息且is_final=true时，才将分析按钮设置为可用状态\n    // 不要根据regionOutputs.visualization的状态来判断，因为它可能被错误地标记为完成\n    // 这里不再设置分析按钮状态，而是依赖handleMessage中对region='visualization'的消息处理\n  }\n\n  // 处理错误\n  const handleError = (error: Error) => {\n    console.error('处理出错:', error);\n\n    // 使用更友好的错误消息\n    let errorMessage = error.message || '请求处理过程中发生错误';\n\n    // 检查是否是WebSocket连接错误\n    if (errorMessage.includes('WebSocket') ||\n        errorMessage.includes('连接') ||\n        errorMessage.includes('服务器')) {\n      // 使用全局WebSocket错误信息（如果有）\n      if (globalWebSocketError) {\n        errorMessage = globalWebSocketError;\n      } else {\n        errorMessage = '无法连接到服务器，请稍后再试';\n      }\n    }\n\n    setError(errorMessage);\n    setLoading(false); // 发生错误时一定要停止加载状态\n\n    // 重置所有区域的流式状态\n    setRegionOutputs(prev => {\n      const updated = { ...prev };\n      Object.keys(updated).forEach(key => {\n        const region = updated[key as keyof typeof updated];\n        region.streaming = false;\n      });\n      return updated;\n    });\n  }\n\n  // 添加页面切换函数\n  const handlePageChange = (pageNumber: number) => {\n    setCurrentPage(pageNumber);\n  };\n\n  // 添加计算总页数的函数\n  const getTotalPages = () => {\n    if (!dataResult) return 1;\n    return Math.ceil(dataResult.length / pageSize);\n  };\n\n  // 获取当前页的数据\n  const getCurrentPageData = () => {\n    if (!dataResult) return [];\n    const startIndex = (currentPage - 1) * pageSize;\n    const endIndex = startIndex + pageSize;\n    return dataResult.slice(startIndex, endIndex);\n  };\n\n  // 重置处理状态\n  const resetProcessingState = () => {\n    setError(null);\n    setLoading(false);\n    setProcessingSteps([]);\n    setCurrentPage(1); // 重置分页状态\n\n\n    // 完全重置所有状态\n    console.log('重置处理状态：完全重置所有区域');\n\n    // 重置所有区域的状态\n    setRegionOutputs({\n      analysis: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      sql: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      explanation: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      data: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      visualization: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      process: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      }\n    });\n\n    // 重置所有结果\n    setSqlResult(null);\n    setExplanationResult(null);\n    setAnalysisResult(null); // 重置分析结果\n    setDataResult(null);\n    setVisualizationResult(null);\n\n    // 重置解释区域的独立状态\n    setExplanationState({\n      hasContent: false,\n      streaming: false\n    });\n\n    // 关闭之前的EventSource连接\n    if (eventSourceRef.current) {\n      try {\n        eventSourceRef.current.close();\n        console.log('已关闭之前的EventSource连接');\n      } catch (err) {\n        console.error('关闭EventSource连接时出错:', err);\n      }\n      eventSourceRef.current = null;\n    }\n  }\n\n  // 监听WebSocket连接状态变化\n  useEffect(() => {\n    // 创建一个定时器，定期检查WebSocket状态\n    const intervalId = setInterval(() => {\n      // 如果有WebSocket错误且当前没有显示错误，则显示错误\n      if (globalWebSocketError && !error && !loading) {\n        setError(globalWebSocketError);\n      }\n\n      // 如果WebSocket恢复正常且当前显示的是WebSocket错误，则清除错误\n      if (globalWebSocketState === WebSocketConnectionState.CONNECTED &&\n          error && (error === globalWebSocketError || error.includes('连接') || error.includes('服务器'))) {\n        setError(null);\n      }\n    }, 1000); // 每秒检查一次\n\n    return () => clearInterval(intervalId);\n  }, [error, loading]);\n\n  // 流式查询处理\n  const handleStreamSearch = async () => {\n    if (loading) return;\n\n    // 检查SSE状态\n    if (globalWebSocketState === WebSocketConnectionState.ERROR ||\n        globalWebSocketState === WebSocketConnectionState.DISCONNECTED) {\n      // 尝试重新连接\n      try {\n        const sse = getWebSocketInstance();\n        const connected = await sse.connect();\n        if (!connected) {\n          setError(globalWebSocketError || '无法连接到服务器，请稍后再试');\n          return;\n        }\n      } catch (error) {\n        setError(globalWebSocketError || '无法连接到服务器，请稍后再试');\n        return;\n      }\n    }\n\n    setError(null);\n    setLoading(true);\n\n    // 保存当前查询内容以便发送\n    const currentQuery = query.trim();\n\n    // 清空输入框\n    setQuery('');\n\n    // 重置所有状态，包括解释内容\n    setProcessingSteps([]);\n    setCurrentPage(1);\n    setSqlResult(null);\n    setExplanationResult(null);\n    setAnalysisResult(null); // 重置分析结果\n    setDataResult(null);\n    setVisualizationResult(null);\n\n    // 重置解释区域的独立状态\n    setExplanationState({\n      hasContent: false,\n      streaming: false\n    });\n\n    if (!currentQuery) {\n      setError('请输入有效的查询');\n      setLoading(false);\n      return;\n    }\n\n    // 检查是否选择了数据库连接\n    if (!selectedConnectionId) {\n      setError('请选择一个数据库连接');\n      setLoading(false);\n      return;\n    }\n\n    // 创建新的聊天会话（如果需要）\n    let sessionId = currentSessionId;\n    if (!sessionId || selectedHistoryId) {\n      sessionId = createNewChatSession();\n    }\n\n    // 添加用户消息到时间轴\n    const userMessageId = addTimelineMessage('user', currentQuery);\n\n    // 初始化UI状态，确保分析区域可见\n    console.log('初始化分析区域');\n\n    // 清理消息缓存，防止重复消息检测影响新查询\n    messageCache.current.clear();\n\n    // 一次性重置所有区域的状态，避免重复设置\n    setRegionOutputs({\n      analysis: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      sql: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      explanation: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      data: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      visualization: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      },\n      process: {\n        merged: '',\n        messages: [],\n        hasContent: false,\n        streaming: false\n      }\n    });\n\n    // 强制设置分析区域为展开状态\n    setCollapsedSections(prev => ({\n      ...prev,\n      analysis: false\n    }));\n\n    // 直接在DOM上更新样式以确保分析区域可见\n    setTimeout(() => {\n      const analysisContainer = document.querySelector('.analysis-output-container');\n      if (analysisContainer) {\n        // 确保容器可见\n        (analysisContainer as HTMLElement).style.display = 'block';\n        (analysisContainer as HTMLElement).style.minHeight = '200px';\n\n        // 滚动到分析区域\n        analysisContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });\n        console.log('滚动到analysis-output-container');\n      } else {\n        console.warn('找不到分析区域容器');\n      }\n\n      // 确保内容区域可见\n      const contentArea = document.querySelector('.analysis-content');\n      if (contentArea) {\n        (contentArea as HTMLElement).style.minHeight = '100px';\n        (contentArea as HTMLElement).style.display = 'block';\n        console.log('设置analysis-content样式');\n      } else {\n        console.warn('找不到分析内容区域');\n      }\n    }, 100);\n\n    try {\n      // 使用SSE进行流式通信\n      console.log('正在使用SSE发送查询...');\n\n      // 导入SSE发送函数\n      const { sendSSEText2SQLRequest } = await import('./api');\n\n      // 发送SSE请求\n      sendSSEText2SQLRequest(\n        currentQuery,\n        handleMessage,\n        handleResult,\n        handleError,\n        handleFinalSql,\n        handleFinalExplanation,\n        handleFinalData,\n        handleFinalVisualization,\n        selectedConnectionId,\n        userFeedbackEnabled\n      );\n\n      console.log('SSE查询已发送');\n    } catch (error) {\n      console.error('SSE请求失败:', error);\n      setError(`SSE请求失败: ${error instanceof Error ? error.message : '未知错误'}`);\n      setLoading(false);\n    }\n  };\n\n  // 组件卸载时关闭连接\n  useEffect(() => {\n    return () => {\n      // 关闭EventSource连接\n      if (eventSourceRef.current) {\n        eventSourceRef.current.close();\n        eventSourceRef.current = null;\n      }\n\n      // 关闭WebSocket连接\n      closeWebSocketConnection();\n    };\n  }, []);\n\n  // 修改图表相关逻辑，减少页面抖动\n  useEffect(() => {\n    if (visualizationResult && dataResult && dataResult.length > 0 && chartRef.current) {\n      // 添加一个标记，避免重复渲染\n      if (chartRef.current.dataset.rendered === 'true') {\n        return;\n      }\n\n      // 如果可视化类型是表格，跳过图表渲染\n      if (visualizationResult.type === 'table') {\n        console.log('表格类型可视化，跳过图表渲染');\n        // 标记为已渲染，避免重复处理\n        chartRef.current.dataset.rendered = 'true';\n\n        // 表格类型可视化已完成，但我们不在这里设置分析按钮状态\n        console.log('表格类型可视化完成');\n        return;\n      }\n\n      // 使用动态导入引入Chart.js\n      import('chart.js/auto').then((ChartModule) => {\n        const Chart = ChartModule.default;\n\n        // 获取画布上下文\n        const canvas = chartRef.current;\n        if (!canvas) return;\n\n        // 销毁现有图表\n        try {\n          const chartInstance = Chart.getChart(canvas);\n          if (chartInstance) {\n            chartInstance.destroy();\n          }\n        } catch (e) {\n          console.log('No existing chart to destroy');\n        }\n\n        // 准备图表数据\n        const ctx = canvas.getContext('2d');\n        if (!ctx) return;\n\n        try {\n          // 标记为已渲染，避免重复渲染\n          canvas.dataset.rendered = 'true';\n\n          const chartType = visualizationResult.type as 'bar' | 'line' | 'pie' | 'scatter';\n          const config = prepareChartConfig(chartType, visualizationResult.config, dataResult);\n          new Chart(ctx, config);\n\n          // 图表渲染完成，但我们不在这里设置分析按钮状态\n          console.log('图表渲染完成');\n        } catch (error) {\n          console.error('图表渲染错误:', error);\n          // 图表渲染出错，但我们不在这里设置分析按钮状态\n          console.log('图表渲染出错');\n        }\n      });\n    }\n\n    // 清理函数\n    return () => {\n      if (chartRef.current) {\n        // 重置已渲染标记\n        chartRef.current.dataset.rendered = 'false';\n\n        // 动态导入Chart.js并清理图表\n        import('chart.js/auto').then((ChartModule) => {\n          const Chart = ChartModule.default;\n          try {\n            const chartInstance = Chart.getChart(chartRef.current!);\n            if (chartInstance) {\n              chartInstance.destroy();\n            }\n          } catch (e) {\n            console.log('Error cleaning up chart:', e);\n          }\n        }).catch(err => {\n          console.error('清理图表时出错:', err);\n        });\n      }\n    };\n  }, [visualizationResult, dataResult]);\n\n  // 添加图表配置准备函数\n  const prepareChartConfig = (\n    type: 'bar' | 'line' | 'pie' | 'scatter',\n    config: any,\n    data: any[]\n  ) => {\n    // 提取数据点\n    const labels = data.map(item => {\n      // 尝试获取X轴字段值\n      const xField = config.xAxis || Object.keys(item)[0];\n      return item[xField];\n    });\n\n    // 提取数据系列\n    const yField = config.yAxis || Object.keys(data[0])[1];\n    const dataPoints = data.map(item => item[yField]);\n\n    // 生成配置\n    return {\n      type, // 使用正确的类型\n      data: {\n        labels: labels,\n        datasets: [{\n          label: config.title || '数据系列',\n          data: dataPoints,\n          backgroundColor: type === 'pie' ?\n            ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'] :\n            'rgba(54, 162, 235, 0.5)',\n          borderColor: 'rgba(54, 162, 235, 1)',\n          borderWidth: 1\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          title: {\n            display: !!config.title,\n            text: config.title || ''\n          },\n          tooltip: {\n            enabled: true\n          },\n          legend: {\n            display: type === 'pie'\n          }\n        }\n      }\n    };\n  };\n\n  // 优化的区域输出更新函数 - 专门为流式markdown优化\n  const updateRegionOutputs = useCallback((region: string, content: string, isFinal?: boolean) => {\n    setRegionOutputs(prev => {\n      const updatedRegions = { ...prev };\n      const regionData = updatedRegions[region as keyof typeof updatedRegions];\n\n      if (!regionData) {\n        console.error(`未知区域: ${region}`);\n        return prev;\n      }\n\n      // 对于分析区域和解释区域，使用专门的流式markdown重复检测策略\n      if (region === 'analysis' || region === 'explanation') {\n        // 对于分析和解释区域，几乎不进行重复检测，因为markdown流式内容可能包含重复的符号\n        // 只检测完全相同且较长的内容块（超过100字符）\n        if (content && content.length > 100 && regionData.merged === content) {\n          console.log(`跳过完全重复的${region}内容: ${region} - ${content.substring(0, 30)}...`);\n          regionData.streaming = isFinal !== true;\n          return updatedRegions;\n        }\n      } else {\n        // 其他区域保持原有的重复检测逻辑\n        if (content && regionData.merged.includes(content)) {\n          console.log(`跳过重复内容: ${region} - ${content.substring(0, 50)}...`);\n          regionData.streaming = isFinal !== true;\n          return updatedRegions;\n        }\n      }\n\n      // 标记该区域已有内容\n      regionData.hasContent = true;\n\n      // 判断streaming状态\n      regionData.streaming = isFinal !== true;\n\n      // 连续输出逻辑 - 对于分析和解释区域，不在这里累积内容，避免双重累积\n      if (region === 'analysis' || region === 'explanation') {\n        // 分析和解释区域的内容由各自的状态管理，这里只更新流式状态\n        // 不累积内容，避免与 analysisResult/explanationResult 状态重复\n        console.log(`${region}区域内容由专门状态管理，跳过regionOutputs累积`);\n      } else {\n        // 其他区域正常累积内容\n        if (regionData.merged === '' || regionData.merged.includes('正在分析您的问题')) {\n          // 如果是初始状态或占位符，直接替换\n          regionData.merged = content;\n        } else {\n          // 直接追加新内容，保持连续性\n          regionData.merged += content;\n        }\n      }\n\n      // 添加详细的调试信息，特别关注分析区域和解释区域\n      if (region === 'analysis') {\n        console.log(`🔄 [分析区域] 更新内容:`, {\n          region,\n          newContentLength: content.length,\n          totalLength: regionData.merged.length,\n          newContentPreview: content.substring(0, 50),\n          totalContentPreview: regionData.merged.substring(regionData.merged.length - 50),\n          newContentHasNewlines: content.includes('\\n'),\n          newContentHasSpaces: content.includes(' '),\n          rawNewContent: JSON.stringify(content.substring(0, 50))\n        });\n      } else if (region === 'explanation') {\n        console.log(`🔄 [解释区域] 更新内容:`, {\n          region,\n          newContentLength: content.length,\n          totalLength: regionData.merged.length,\n          newContentPreview: content.substring(0, 50),\n          totalContentPreview: regionData.merged.substring(Math.max(0, regionData.merged.length - 50)),\n          isAppending: regionData.merged.length > 0\n        });\n      } else {\n        console.log(`更新区域内容: ${region} - 新增${content.length}字符，总长度${regionData.merged.length}`);\n      }\n\n      return updatedRegions;\n    });\n  }, []);\n\n  // 简化的消息后处理函数\n  const handlePostMessageTasks = useCallback((region: string, message: StreamResponseMessage, source: string, content: string) => {\n    // 更新处理步骤\n    if (content && region === 'process') {\n      const step: ProcessingStep = {\n        id: processingStepIdRef.current++,\n        message: content,\n        timestamp: new Date(),\n        source: source\n      };\n      setProcessingSteps(prev => [...prev, step]);\n    }\n\n    // 检查是否是反馈请求消息\n    if ((message.source === 'user_proxy' && message.content) ||\n        (message.type === 'feedback_request' && message.content) ||\n        (message.region === 'user_proxy' && message.content)) {\n      console.log('🔔 检测到反馈请求消息:', {\n        source: message.source,\n        type: message.type,\n        region: message.region,\n        content: message.content,\n        timestamp: new Date().toISOString()\n      });\n      setUserFeedback({\n        visible: true,\n        message: '',\n        promptMessage: message.content\n      });\n    }\n  }, []);\n\n  // 消息去重缓存\n  const messageCache = useRef(new Set<string>());\n\n  // 优化的消息处理函数 - 专门为流式markdown优化\n  const handleMessage = useCallback((message: StreamResponseMessage) => {\n    // 清除错误状态\n    setError(null);\n\n    // 确定消息区域\n    let region = message.region || 'process';\n    const source = message.source || '系统';\n    let content = message.content || '';\n\n    // 对于分析区域和解释区域，不过滤包含空格和换行的内容，因为这些对markdown格式很重要\n    if (region === 'analysis' || region === 'explanation') {\n      // 只过滤完全空的内容\n      if (!content) {\n        return;\n      }\n    } else {\n      // 其他区域过滤空消息\n      if (!content.trim()) {\n        return;\n      }\n    }\n\n    // 优化消息去重逻辑 - 对分析区域和解释区域使用更精确的标识\n    let messageId: string;\n    if (region === 'analysis') {\n      // 对于分析区域，使用时间戳和内容长度来生成更精确的标识\n      messageId = `${region}-${source}-${Date.now()}-${content.length}-${content.substring(0, 20)}`;\n    } else if (region === 'explanation') {\n      // 对于解释区域，使用时间戳和内容哈希来生成更精确的标识，避免重复\n      const contentHash = content.length + content.substring(0, 30) + content.substring(content.length - 30);\n      messageId = `${region}-${source}-${Date.now()}-${contentHash}`;\n    } else if (region === 'user_proxy' || message.type === 'feedback_request') {\n      // 对于用户反馈请求，使用时间戳确保每次请求都能被处理，避免重复消息被忽略\n      messageId = `${region}-${source}-${Date.now()}-${Math.random()}-${content.substring(0, 50)}`;\n    } else {\n      // 其他区域保持原有逻辑\n      messageId = `${region}-${source}-${content.substring(0, 100)}`;\n    }\n\n    if (messageCache.current.has(messageId)) {\n      console.log(`跳过重复消息: ${region} - ${content.substring(0, 30)}...`);\n      return;\n    }\n    messageCache.current.add(messageId);\n\n    // 清理过期的缓存（保留最近1000条）\n    if (messageCache.current.size > 1000) {\n      const entries = Array.from(messageCache.current);\n      messageCache.current.clear();\n      entries.slice(-500).forEach(entry => messageCache.current.add(entry));\n    }\n\n    // 添加详细的调试信息，特别关注分析区域和解释区域的内容格式\n    if (region === 'analysis') {\n      console.log(`📋 [分析区域] 收到消息:`, {\n        region,\n        source,\n        contentLength: content.length,\n        contentPreview: content.substring(0, 100),\n        hasNewlines: content.includes('\\n'),\n        hasSpaces: content.includes(' '),\n        rawContent: JSON.stringify(content.substring(0, 100))\n      });\n    } else if (region === 'explanation') {\n      console.log(`📋 [解释区域] 收到消息:`, {\n        region,\n        source,\n        contentLength: content.length,\n        contentPreview: content.substring(0, 100),\n        hasNewlines: content.includes('\\n'),\n        hasSpaces: content.includes(' ')\n      });\n    } else {\n      console.log(`📋 收到消息: ${region} - ${source} - ${content.substring(0, 50)}...`);\n    }\n\n    // 更新区域输出状态（主要显示逻辑）\n    // 解释区域完全跳过 regionOutputs 处理，只使用 explanationResult 状态\n    if (region !== 'explanation') {\n      updateRegionOutputs(region, content, message.is_final);\n    }\n\n    // 异步更新其他状态，避免阻塞渲染\n    setTimeout(() => {\n      // 处理特殊区域的消息\n      if (region === 'analysis') {\n        // 更新分析结果 - 保持原始格式，直接累加\n        setAnalysisResult(prev => {\n          const prevContent = prev || '';\n          // 对于分析区域，直接累加内容，保持markdown格式\n          return prevContent + content;\n        });\n      } else if (region === 'explanation') {\n        // 更新解释结果 - 与分析区域保持完全一致的处理逻辑\n        setExplanationResult(prev => {\n          const prevContent = prev || '';\n          // 对于解释区域，直接累加内容，保持markdown格式（与分析区域完全一致）\n          return prevContent + content;\n        });\n\n        // 同时更新解释区域的独立状态\n        setExplanationState({\n          hasContent: true,\n          streaming: message.is_final !== true\n        });\n      }\n\n      // 如果是可视化区域的最终消息，停止加载状态\n      if (message.is_final === true && region === 'visualization') {\n        setLoading(false);\n        console.log('收到可视化区域的最终消息，分析按钮恢复可用');\n      }\n\n      // 处理其他消息后的任务\n      handlePostMessageTasks(region, message, source, content);\n    }, 0);\n  }, [updateRegionOutputs, handlePostMessageTasks]);\n\n\n\n\n\n\n\n  // 在组件内添加useEffect来监控SQL区域的显示条件\n  useEffect(() => {\n\n  }, [sqlResult, regionOutputs.sql.hasContent, regionOutputs.analysis.streaming, regionOutputs.analysis.hasContent]);\n\n  // 添加MutationObserver来监控分析区域内容变化\n  useEffect(() => {\n    // 如果分析区域没有内容或已折叠，则不需要监控\n    if (!regionOutputs.analysis.hasContent || collapsedSections.analysis) {\n      return;\n    }\n\n    // 创建MutationObserver来监控内容变化\n    const observer = new MutationObserver((mutations) => {\n      // 检查是否正在流式输出，只有流式输出时才自动滚动到底部\n      if (regionOutputs.analysis.streaming) {\n        scrollAnalysisAreaToBottom();\n      }\n    });\n\n    // 延迟一下再开始监控，确保元素已经渲染\n    setTimeout(() => {\n      // 查找分析区域容器\n      const analysisContainer = document.querySelector('.analysis-content-container');\n      if (analysisContainer) {\n        // 配置监视选项，监视子树变化和子节点变化\n        observer.observe(analysisContainer, {\n          childList: true,\n          subtree: true,\n          characterData: true\n        });\n\n        // 初始化时滚动到底部，但只在流式输出时\n        if (regionOutputs.analysis.streaming) {\n          scrollAnalysisAreaToBottom();\n        }\n      }\n    }, 100);\n\n    // 清理函数\n    return () => {\n      observer.disconnect();\n    };\n  }, [regionOutputs.analysis.hasContent, regionOutputs.analysis.merged, regionOutputs.analysis.streaming, collapsedSections.analysis]);\n\n  // 在 Text2SQL 组件内添加一个处理回车键的函数\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\n    if (e.key === 'Enter' && !loading && query.trim() !== '') {\n      e.preventDefault();\n      handleStreamSearch();\n    }\n  };\n\n  // 混合检索相关函数\n  const handleShowExamples = async () => {\n    if (!query.trim() || !selectedConnectionId) {\n      return;\n    }\n\n    setHybridExamplesVisible(true);\n  };\n\n  const handleExampleSelect = (example: SimilarQAPair) => {\n    // 将选中的示例应用到查询中\n    setQuery(example.qa_pair.question);\n    setHybridExamplesVisible(false);\n\n    // 可以选择自动执行查询\n    // handleStreamSearch();\n  };\n\n  const handleCloseExamples = () => {\n    setHybridExamplesVisible(false);\n  };\n\n  // 处理用户反馈提交\n  const handleFeedbackSubmit = async () => {\n    if (!userFeedback.message.trim()) return;\n\n    try {\n      console.log('发送用户反馈:', userFeedback.message);\n\n      // 获取当前反馈消息\n      const currentFeedback = userFeedback.message;\n\n      // 在前端添加分隔符\n      setRegionOutputs(prev => {\n        const updatedRegions = { ...prev };\n        const analysisRegion = updatedRegions.analysis;\n\n        // 构建分隔符标记\n        const separator = \"\\n\\n----------------------------\\n### 用户反馈：\" + currentFeedback + \"\\n----------------------------\\n\\n\";\n\n        // 检查是否已经存在相同的反馈内容\n        if (!analysisRegion.merged.includes(`用户反馈：${currentFeedback}`)) {\n          analysisRegion.merged += separator;\n        } else {\n          console.log('该反馈已存在，不重复添加');\n        }\n\n        return updatedRegions;\n      });\n\n      // 使用SSE发送反馈\n      const { sendUserFeedback } = await import('./api');\n      const sseInstance = getWebSocketInstance();\n      const sessionId = sseInstance.getCurrentSessionId();\n\n      console.log('📤 准备发送用户反馈:', {\n        sessionId,\n        feedback: currentFeedback,\n        hasSession: !!sessionId,\n        timestamp: new Date().toISOString()\n      });\n\n      if (sessionId) {\n        await sendUserFeedback(sessionId, currentFeedback, (error) => {\n          console.error('发送反馈失败:', error);\n          setError(`发送反馈失败: ${error.message}`);\n        });\n        console.log('✅ 反馈发送成功');\n      } else {\n        console.error('❌ 没有活动的SSE会话');\n        setError('没有活动的会话，无法发送反馈');\n      }\n\n      // 清空并隐藏反馈区\n      console.log('🔄 反馈提交完成，清空反馈状态');\n      setUserFeedback({\n        visible: false,\n        message: '',\n        promptMessage: ''\n      });\n\n      // 确保内容滚动到底部\n      setTimeout(() => {\n        scrollAnalysisAreaToBottom();\n      }, 200);\n    } catch (err) {\n      console.error('发送用户反馈出错:', err);\n      setError(`发送反馈失败: ${err}`);\n    }\n  };\n\n  // 处理用户反馈取消\n  const handleFeedbackCancel = () => {\n    try {\n      console.log('用户取消反馈');\n      const ws = getWebSocketInstance();\n      ws.sendMessage('取消操作');\n\n      // 清空并隐藏反馈区\n      setUserFeedback({\n        visible: false,\n        message: '',\n        promptMessage: ''\n      });\n    } catch (err) {\n      console.error('取消用户反馈出错:', err);\n      setError(`取消反馈失败: ${err}`);\n    }\n  };\n\n  // 处理用户同意操作\n  const handleFeedbackApprove = async () => {\n    try {\n      console.log('发送用户同意反馈: APPROVE');\n\n      // 在前端添加分隔符 - 确保只添加一次\n      setRegionOutputs(prev => {\n        const updatedRegions = { ...prev };\n        const analysisRegion = updatedRegions.analysis;\n\n        // 只有在当前内容中不包含分隔符时才添加\n        if (!analysisRegion.merged.includes(\"用户已同意操作\") &&\n            !analysisRegion.merged.includes(\"----------------------------\")) {\n          const separator = \"\\n\\n----------------------------\\n### 用户已同意操作\\n----------------------------\\n\\n\";\n          analysisRegion.merged += separator;\n        }\n\n        return updatedRegions;\n      });\n\n      // 使用SSE发送同意反馈\n      const { sendUserApproval } = await import('./api');\n      const sseInstance = getWebSocketInstance();\n      const sessionId = sseInstance.getCurrentSessionId();\n\n      if (sessionId) {\n        await sendUserApproval(sessionId, (error) => {\n          console.error('发送同意反馈失败:', error);\n          setError(`发送同意反馈失败: ${error.message}`);\n        });\n      } else {\n        console.error('没有活动的SSE会话');\n        setError('没有活动的会话，无法发送同意反馈');\n      }\n\n      // 清空并隐藏反馈区\n      setUserFeedback({\n        visible: false,\n        message: '',\n        promptMessage: ''\n      });\n\n      // 确保内容滚动到底部\n      setTimeout(() => {\n        scrollAnalysisAreaToBottom();\n      }, 200);\n    } catch (err) {\n      console.error('发送同意反馈出错:', err);\n      setError(`发送同意反馈失败: ${err}`);\n    }\n  };\n\n\n\n  // 滚动分析区域到底部的函数 - 优化版\n  const scrollAnalysisAreaToBottom = () => {\n    // 检查是否正在流式输出，只有流式输出时才自动滚动\n    if (!regionOutputs.analysis.streaming) {\n      return; // 如果不是流式输出，不进行自动滚动\n    }\n\n    // 首先尝试滚动分析区域容器\n    const analysisContainer = document.querySelector('.analysis-content-container');\n    if (analysisContainer && analysisContainer instanceof HTMLElement) {\n      // 检查用户是否手动滚动了内容\n      // 如果用户已经向上滚动了超过100像素，则不强制滚动到底部\n      const scrollPosition = analysisContainer.scrollTop;\n      const scrollHeight = analysisContainer.scrollHeight;\n      const clientHeight = analysisContainer.clientHeight;\n\n      // 如果用户已经向上滚动了超过200像素，则不强制滚动\n      if (scrollHeight - scrollPosition - clientHeight > 200) {\n        console.log('用户已手动滚动，不强制滚动到底部');\n        return;\n      }\n\n      // 滚动到底部\n      analysisContainer.scrollTop = analysisContainer.scrollHeight;\n\n      // 尝试常见的内容容器选择器\n      const analysisContent = document.querySelector('.analysis-content');\n      if (analysisContent && analysisContent instanceof HTMLElement) {\n        // 确保内容区域可见并可滚动\n        analysisContent.style.display = 'block';\n        analysisContent.style.overflow = 'auto';\n        analysisContent.style.minHeight = '100px';\n\n        // 延迟滚动以确保内容已经渲染\n        setTimeout(() => {\n          // 再次检查是否正在流式输出\n          if (regionOutputs.analysis.streaming) {\n            analysisContent.scrollTop = analysisContent.scrollHeight;\n          }\n        }, 100);\n      }\n    }\n  };\n\n\n\n  // 处理内容复制\n  const handleCopyContent = useCallback((content: string, regionId: string) => {\n    // 这里可以添加复制成功的提示\n    console.log(`${regionId} 内容已复制到剪贴板`);\n  }, []);\n\n\n\n\n\n  return (\n    <div className={`gemini-chat-layout ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>\n      {/* 左侧边栏 - 固定宽度，按照Gemini标准 */}\n      <div className=\"gemini-sidebar\">\n        <ChatHistorySidebar\n          histories={chatHistories}\n          selectedHistoryId={selectedHistoryId}\n          onSelectHistory={handleSelectHistory}\n          onDeleteHistory={handleDeleteHistory}\n          onNewChat={createNewChatSession}\n          collapsed={sidebarCollapsed}\n          onToggleCollapse={handleSidebarToggle}\n        />\n      </div>\n\n      {/* 右侧主区域 - 按照Gemini标准分为上下两部分 */}\n      <div className=\"gemini-main-area\">\n        {/* 上部分：聊天内容区域 - 可滚动 */}\n        <div className=\"gemini-chat-content\">\n          {/* 数据库连接选择器 - 左上角位置，与NewText2SQLPage一致 */}\n          {connections.length > 0 && (\n            <div style={{\n              position: 'absolute',\n              top: '16px',\n              left: '24px',\n              zIndex: 10\n            }}>\n              <ConnectionSelector\n                connections={connections}\n                selectedConnectionId={selectedConnectionId}\n                setSelectedConnectionId={setSelectedConnectionId}\n                loadingConnections={loadingConnections}\n              />\n            </div>\n          )}\n\n          {/* 内容显示区域 - 与NewText2SQLPage一致 */}\n          <div style={{\n            flex: 1,\n            overflow: 'auto',\n            minHeight: 0,\n            padding: '24px',\n            paddingTop: '80px' // 为数据库选择器留出空间\n          }}>\n            {/* 错误消息 */}\n            <ErrorMessage error={error} />\n\n            {/* 使用折叠面板显示5个区域 */}\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>\n              {/* 查询分析区域 */}\n              {(regionOutputs.analysis.hasContent || regionOutputs.analysis.streaming) && (\n                <RegionPanel\n                  title=\"查询分析\"\n                  content={analysisResult || regionOutputs.analysis.merged}\n                  isStreaming={regionOutputs.analysis.streaming}\n                  hasContent={regionOutputs.analysis.hasContent}\n                  region=\"analysis\"\n                  onCopyContent={handleCopyContent}\n                />\n              )}\n\n              {/* SQL语句区域 */}\n              {(regionOutputs.sql.hasContent || regionOutputs.sql.streaming) && (\n                <RegionPanel\n                  title=\"SQL语句\"\n                  content={sqlResult || regionOutputs.sql.merged}\n                  isStreaming={regionOutputs.sql.streaming}\n                  hasContent={regionOutputs.sql.hasContent}\n                  region=\"sql\"\n                  onCopyContent={handleCopyContent}\n                />\n              )}\n\n              {/* 语句解释区域 */}\n              {(explanationState.hasContent || explanationState.streaming) && (\n                <RegionPanel\n                  title=\"语句解释\"\n                  content={explanationResult || ''}\n                  isStreaming={explanationState.streaming}\n                  hasContent={explanationState.hasContent}\n                  region=\"explanation\"\n                  onCopyContent={handleCopyContent}\n                />\n              )}\n\n              {/* 查询结果区域 */}\n              {(regionOutputs.data.hasContent || regionOutputs.data.streaming) && (\n                <RegionPanel\n                  title=\"查询结果\"\n                  content={dataResult ? JSON.stringify(dataResult, null, 2) : regionOutputs.data.merged}\n                  isStreaming={regionOutputs.data.streaming}\n                  hasContent={regionOutputs.data.hasContent}\n                  region=\"data\"\n                  onCopyContent={handleCopyContent}\n                  dataResult={dataResult}\n                  currentPage={currentPage}\n                  pageSize={pageSize}\n                  handlePageChange={handlePageChange}\n                  getTotalPages={getTotalPages}\n                  getCurrentPageData={getCurrentPageData}\n                  convertToCSV={csvConverter}\n                />\n              )}\n\n              {/* 数据可视化区域 */}\n              {(regionOutputs.visualization.hasContent || regionOutputs.visualization.streaming) && (\n                <RegionPanel\n                  title=\"数据可视化\"\n                  content={visualizationResult ? JSON.stringify(visualizationResult, null, 2) : regionOutputs.visualization.merged}\n                  isStreaming={regionOutputs.visualization.streaming}\n                  hasContent={regionOutputs.visualization.hasContent}\n                  region=\"visualization\"\n                  onCopyContent={handleCopyContent}\n                  visualizationResult={visualizationResult}\n                  dataResult={dataResult}\n                />\n              )}\n            </div>\n\n            {/* 用户反馈区域 */}\n            <UserFeedback\n              visible={userFeedback.visible}\n              message={userFeedback.message}\n              promptMessage={userFeedback.promptMessage}\n              setMessage={(message) => setUserFeedback(prev => ({ ...prev, message }))}\n              handleSubmit={handleFeedbackSubmit}\n              handleApprove={handleFeedbackApprove}\n              handleCancel={handleFeedbackCancel}\n            />\n          </div>\n        </div>\n\n        {/* 下部分：固定输入区域 - 按照Gemini标准 */}\n        <div className=\"gemini-input-area\">\n          <div className=\"gemini-input-container\">\n            <div className=\"gemini-input-box\">\n              {/* 左侧控制按钮组 - 智能推荐 */}\n              <div className=\"left-controls\">\n                <Tooltip title={hybridRetrievalEnabled ? \"智能推荐已启用\" : \"智能推荐已禁用\"}>\n                  <button\n                    className={`control-button ${hybridRetrievalEnabled ? 'active' : ''}`}\n                    onClick={() => setHybridRetrievalEnabled(!hybridRetrievalEnabled)}\n                    disabled={loading}\n                  >\n                    <BulbOutlined />\n                  </button>\n                </Tooltip>\n              </div>\n\n              {/* 输入框 */}\n              <div className=\"input-wrapper\">\n                <input\n                  type=\"text\"\n                  value={query}\n                  onChange={(e) => setQuery(e.target.value)}\n                  onKeyDown={handleKeyDown}\n                  placeholder=\"向智能助手提问\"\n                  className=\"gemini-input\"\n                  disabled={loading}\n                />\n\n                {/* 用户反馈勾选框 - 内置在输入框内 */}\n                <div className=\"input-feedback-checkbox\">\n                  <Tooltip title=\"启用用户反馈功能\">\n                    <label className=\"inline-feedback-checkbox-label\">\n                      <input\n                        type=\"checkbox\"\n                        checked={userFeedbackEnabled}\n                        onChange={(e) => setUserFeedbackEnabled(e.target.checked)}\n                        disabled={loading}\n                        className=\"inline-feedback-checkbox-input\"\n                      />\n                    </label>\n                  </Tooltip>\n                </div>\n              </div>\n\n              {/* 右侧按钮组 */}\n              <div className=\"right-controls\">\n                {/* 智能示例按钮 */}\n                {hybridRetrievalEnabled && (\n                  <Tooltip title=\"查看智能推荐示例\">\n                    <button\n                      onClick={handleShowExamples}\n                      disabled={loading || query.trim() === '' || !selectedConnectionId}\n                      className=\"control-button\"\n                    >\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                        <path d=\"M9 11H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h4\"/>\n                        <path d=\"M15 11h4a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-4\"/>\n                        <path d=\"M12 2v20\"/>\n                        <circle cx=\"12\" cy=\"8\" r=\"2\"/>\n                      </svg>\n                    </button>\n                  </Tooltip>\n                )}\n\n                {/* 发送按钮 */}\n                <Tooltip title=\"发送查询\">\n                  <button\n                    onClick={handleStreamSearch}\n                    disabled={loading || query.trim() === '' || !selectedConnectionId}\n                    className=\"send-button\"\n                  >\n                    {loading ? (\n                      <svg className=\"animate-spin\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" fill=\"none\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                    ) : (\n                      <SendOutlined />\n                    )}\n                  </button>\n                </Tooltip>\n              </div>\n            </div>\n\n            {error && (\n              <div style={{\n                marginTop: '8px',\n                color: '#ff4d4f',\n                fontSize: '12px',\n                textAlign: 'center'\n              }}>\n                {error}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* 混合检索示例面板 */}\n      <HybridExamplesPanel\n        query={query}\n        connectionId={selectedConnectionId}\n        schemaContext={schemaContext}\n        visible={hybridExamplesVisible}\n        onExampleSelect={handleExampleSelect}\n        onClose={handleCloseExamples}\n      />\n    </div>\n  );\n}"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAGvE,OAAO,2BAA2B;AAClC,OAAO,+BAA+B;AACtC,OAAO,4BAA4B;AACnC,OAAO,iCAAiC;AACxC,OAAO,sCAAsC;AAC7C,OAAO,oCAAoC;AAC3C,SAGEC,wBAAwB,EACxBC,oBAAoB,EACpBC,wBAAwB,EACxBC,oBAAoB,EACpBC,oBAAoB,EACpBC,cAAc,QACT,OAAO;;AAGd;;AAOA,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,kBAAkB,MAAM,iCAAiC;AAEhE,OAAOC,WAAW,MAAM,0BAA0B;AAClD;AACA,SAASC,YAAY,IAAIC,YAAY,QAA4C,SAAS;;AAE1F;;AAGA,SAAiBC,OAAO,QAAQ,MAAM;AACtC,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D;AACA,SAASC,kBAAkB,QAAQ,mCAAmC;;AAEtE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAMA;AACA,MAAMC,KAAK,GAAIC,KAAoC,iBACjDF,OAAA;EACEG,KAAK,EAAC,4BAA4B;EAClCC,KAAK,EAAC,IAAI;EACVC,MAAM,EAAC,IAAI;EACXC,OAAO,EAAC,WAAW;EACnBC,IAAI,EAAC,MAAM;EACXC,MAAM,EAAC,cAAc;EACrBC,WAAW,EAAC,GAAG;EACfC,aAAa,EAAC,OAAO;EACrBC,cAAc,EAAC,OAAO;EAAA,GAClBT,KAAK;EAAAU,QAAA,gBAETZ,OAAA;IAAMa,CAAC,EAAC;EAAmK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC9KjB,OAAA;IAAMa,CAAC,EAAC;EAAqK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC7K,CACN;AAAAC,EAAA,GAhBKjB,KAAK;AAkBX,MAAMkB,QAAQ,GAAIjB,KAAoC,iBACpDF,OAAA;EACEG,KAAK,EAAC,4BAA4B;EAClCC,KAAK,EAAC,IAAI;EACVC,MAAM,EAAC,IAAI;EACXC,OAAO,EAAC,WAAW;EACnBC,IAAI,EAAC,MAAM;EACXC,MAAM,EAAC,cAAc;EACrBC,WAAW,EAAC,GAAG;EACfC,aAAa,EAAC,OAAO;EACrBC,cAAc,EAAC,OAAO;EAAA,GAClBT,KAAK;EAAAU,QAAA,gBAETZ,OAAA;IAASoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC;EAAG;IAAAT,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAU,CAAC,eAChDjB,OAAA;IAAMa,CAAC,EAAC;EAAmC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC,eACnDjB,OAAA;IAAMa,CAAC,EAAC;EAAqC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAClD,CACN;AAAAO,GAAA,GAjBKL,QAAQ;AAmBd,MAAMM,MAAM,GAAIvB,KAAoC,iBAClDF,OAAA;EACEG,KAAK,EAAC,4BAA4B;EAClCC,KAAK,EAAC,IAAI;EACVC,MAAM,EAAC,IAAI;EACXC,OAAO,EAAC,WAAW;EACnBC,IAAI,EAAC,MAAM;EACXC,MAAM,EAAC,cAAc;EACrBC,WAAW,EAAC,GAAG;EACfC,aAAa,EAAC,OAAO;EACrBC,cAAc,EAAC,OAAO;EAAA,GAClBT,KAAK;EAAAU,QAAA,gBAETZ,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACK,CAAC,EAAC;EAAG;IAAAZ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAS,CAAC,eACvCjB,OAAA;IAAMa,CAAC,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC7B,CACN;AAAAU,GAAA,GAhBKF,MAAM;AAkBZ,MAAMG,QAAQ,GAAI1B,KAAoC,iBACpDF,OAAA;EACEG,KAAK,EAAC,4BAA4B;EAClCC,KAAK,EAAC,IAAI;EACVC,MAAM,EAAC,IAAI;EACXC,OAAO,EAAC,WAAW;EACnBC,IAAI,EAAC,MAAM;EACXC,MAAM,EAAC,cAAc;EACrBC,WAAW,EAAC,GAAG;EACfC,aAAa,EAAC,OAAO;EACrBC,cAAc,EAAC,OAAO;EAAA,GAClBT,KAAK;EAAAU,QAAA,gBAETZ,OAAA;IAAM6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC;EAAI;IAAAlB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC,eAC7CjB,OAAA;IAAM6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC;EAAG;IAAAlB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC,eAC5CjB,OAAA;IAAM6B,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC;EAAI;IAAAlB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACxC,CACN;AAAAgB,GAAA,GAjBKL,QAAQ;AAmBd,MAAMM,QAAQ,GAAIhC,KAAoC,iBACpDF,OAAA;EACEG,KAAK,EAAC,4BAA4B;EAClCC,KAAK,EAAC,IAAI;EACVC,MAAM,EAAC,IAAI;EACXC,OAAO,EAAC,WAAW;EACnBC,IAAI,EAAC,MAAM;EACXC,MAAM,EAAC,cAAc;EACrBC,WAAW,EAAC,GAAG;EACfC,aAAa,EAAC,OAAO;EACrBC,cAAc,EAAC,OAAO;EAAA,GAClBT,KAAK;EAAAU,QAAA,gBAETZ,OAAA;IAAMa,CAAC,EAAC;EAAuE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC,eACvFjB,OAAA;IAAUmC,MAAM,EAAC;EAAgB;IAAArB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAW,CAAC,eAC7CjB,OAAA;IAAM6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC;EAAI;IAAAlB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC,eAC5CjB,OAAA;IAAM6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC;EAAI;IAAAlB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC,eAC5CjB,OAAA;IAAM6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC;EAAG;IAAAlB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACvC,CACN;AAAAmB,GAAA,GAnBKF,QAAQ;AAqBd,MAAMG,IAAI,GAAInC,KAAoC,iBAChDF,OAAA;EACEG,KAAK,EAAC,4BAA4B;EAClCC,KAAK,EAAC,IAAI;EACVC,MAAM,EAAC,IAAI;EACXC,OAAO,EAAC,WAAW;EACnBC,IAAI,EAAC,MAAM;EACXC,MAAM,EAAC,cAAc;EACrBC,WAAW,EAAC,GAAG;EACfC,aAAa,EAAC,OAAO;EACrBC,cAAc,EAAC,OAAO;EAAA,GAClBT,KAAK;EAAAU,QAAA,gBAETZ,OAAA;IAAUmC,MAAM,EAAC;EAAkB;IAAArB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAW,CAAC,eAC/CjB,OAAA;IAAUmC,MAAM,EAAC;EAAe;IAAArB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAW,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACzC,CACN;AAAAqB,GAAA,GAhBKD,IAAI;AAkBV,MAAME,QAAQ,GAAIrC,KAAoC,iBACpDF,OAAA;EACEG,KAAK,EAAC,4BAA4B;EAClCC,KAAK,EAAC,IAAI;EACVC,MAAM,EAAC,IAAI;EACXC,OAAO,EAAC,WAAW;EACnBC,IAAI,EAAC,MAAM;EACXC,MAAM,EAAC,cAAc;EACrBC,WAAW,EAAC,GAAG;EACfC,aAAa,EAAC,OAAO;EACrBC,cAAc,EAAC,OAAO;EAAA,GAClBT,KAAK;EAAAU,QAAA,gBAETZ,OAAA;IAAMa,CAAC,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC,eAChCjB,OAAA;IAAMa,CAAC,EAAC;EAAc;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC,eAC9BjB,OAAA;IAAMa,CAAC,EAAC;EAAc;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC3B,CACN;;AAED;AAAAuB,GAAA,GAnBMD,QAAQ;AAoBd,MAAME,WAAW,GAAIvC,KAAoC,iBACvDF,OAAA;EACEG,KAAK,EAAC,4BAA4B;EAClCC,KAAK,EAAC,IAAI;EACVC,MAAM,EAAC,IAAI;EACXC,OAAO,EAAC,WAAW;EACnBC,IAAI,EAAC,MAAM;EACXC,MAAM,EAAC,cAAc;EACrBC,WAAW,EAAC,GAAG;EACfC,aAAa,EAAC,OAAO;EACrBC,cAAc,EAAC,OAAO;EAAA,GAClBT,KAAK;EAAAU,QAAA,gBAETZ,OAAA;IAAQoB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACK,CAAC,EAAC;EAAI;IAAAZ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAS,CAAC,eACxCjB,OAAA;IAAM6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC;EAAI;IAAAlB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC,eAC5CjB,OAAA;IAAM6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC;EAAI;IAAAlB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC1C,CACN;;AAED;AAAAyB,GAAA,GAnBMD,WAAW;AAoBjB,MAAME,QAAQ,GAAIzC,KAAoC,iBACpDF,OAAA;EACEG,KAAK,EAAC,4BAA4B;EAClCC,KAAK,EAAC,IAAI;EACVC,MAAM,EAAC,IAAI;EACXC,OAAO,EAAC,WAAW;EACnBC,IAAI,EAAC,MAAM;EACXC,MAAM,EAAC,cAAc;EACrBC,WAAW,EAAC,GAAG;EACfC,aAAa,EAAC,OAAO;EACrBC,cAAc,EAAC,OAAO;EAAA,GAClBT,KAAK;EAAAU,QAAA,gBAETZ,OAAA;IAAMa,CAAC,EAAC;EAA8B;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC,eAC9CjB,OAAA;IAAMa,CAAC,EAAC;EAA6B;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC,eAC7CjB,OAAA;IAAMa,CAAC,EAAC;EAA8B;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC,eAC9CjB,OAAA;IAAM6B,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,OAAO;IAACC,EAAE,EAAC;EAAI;IAAAlB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC7C,CACN;;AAED;AAAA2B,GAAA,GApBMD,QAAQ;AAqBd,MAAME,wBAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGvE,QAAQ,CAACO,oBAAoB,CAAC;EAE1DN,SAAS,CAAC,MAAM;IACd;IACA,MAAMuE,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnCF,SAAS,CAAChE,oBAAoB,CAAC;IACjC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMmE,aAAa,CAACF,UAAU,CAAC;EACxC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQL,MAAM;MACZ,KAAKhE,wBAAwB,CAACsE,SAAS;QACrC,OAAO;UAAEC,WAAW,EAAE,4BAA4B;UAAEC,IAAI,EAAE;QAAM,CAAC;MACnE,KAAKxE,wBAAwB,CAACyE,UAAU;QACtC,OAAO;UAAEF,WAAW,EAAE,6BAA6B;UAAEC,IAAI,EAAE;QAAM,CAAC;MACpE,KAAKxE,wBAAwB,CAAC0E,YAAY;QACxC,OAAO;UAAEH,WAAW,EAAE,+BAA+B;UAAEC,IAAI,EAAE;QAAM,CAAC;MACtE,KAAKxE,wBAAwB,CAAC2E,KAAK;QACjC,OAAO;UAAEJ,WAAW,EAAE,wBAAwB;UAAEC,IAAI,EAAE;QAAO,CAAC;MAChE,KAAKxE,wBAAwB,CAAC4E,YAAY;QACxC,OAAO;UAAEL,WAAW,EAAE,+BAA+B;UAAEC,IAAI,EAAE;QAAM,CAAC;MACtE;QACE,OAAO;UAAED,WAAW,EAAE,+BAA+B;UAAEC,IAAI,EAAE;QAAO,CAAC;IACzE;EACF,CAAC;EAED,MAAMK,UAAU,GAAGR,aAAa,CAAC,CAAC;EAElC,oBACEpD,OAAA;IAAK6D,SAAS,EAAE,oBAAoBD,UAAU,CAACN,WAAW,EAAG;IAAA1C,QAAA,gBAC3DZ,OAAA;MAAK6D,SAAS,EAAC;IAAsB;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC5CjB,OAAA;MAAAY,QAAA,EAAOgD,UAAU,CAACL;IAAI;MAAAzC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3B,CAAC;AAEV,CAAC;;AAED;AAAA6B,EAAA,CAxCMD,wBAAwB;AAAAiB,GAAA,GAAxBjB,wBAAwB,EAgD9B;AAOA;AA0CA,eAAe,SAASkB,QAAQA,CAAA,EAAG;EAAAC,GAAA;EACjC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGzF,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC0F,OAAO,EAAEC,UAAU,CAAC,GAAG3F,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAAC4F,KAAK,EAAEC,QAAQ,CAAC,GAAG7F,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC8F,eAAe,EAAEC,kBAAkB,CAAC,GAAG/F,QAAQ,CAAmB,EAAE,CAAC;;EAI5E;EACA,MAAM,CAACgG,WAAW,EAAEC,cAAc,CAAC,GAAGjG,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACkG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnG,QAAQ,CAAgB,IAAI,CAAC;EACrF,MAAM,CAACoG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrG,QAAQ,CAAU,KAAK,CAAC;;EAE5E;EACA,MAAM,CAACsG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvG,QAAQ,CAAU,KAAK,CAAC;;EAE9E;EACA,MAAM,CAACwG,WAAW,EAAEC,cAAc,CAAC,GAAGzG,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC0G,QAAQ,EAAEC,WAAW,CAAC,GAAG3G,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAM,CAAC4G,aAAa,EAAEC,gBAAgB,CAAC,GAAG7G,QAAQ,CAAgB;IAChE8G,QAAQ,EAAE;MACRC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE;IACb,CAAC;IACDC,GAAG,EAAE;MACHJ,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE;IACb,CAAC;IACDE,WAAW,EAAE;MACXL,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE;IACb,CAAC;IACDG,IAAI,EAAE;MACJN,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE;IACb,CAAC;IACDI,aAAa,EAAE;MACbP,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE;IACb,CAAC;IACDK,OAAO,EAAE;MACPR,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE;IACb;EACF,CAAC,CAAC;;EAEF;EACA,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAGzH,QAAQ,CAAgB,IAAI,CAAC;EAC/D,MAAM,CAAC0H,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3H,QAAQ,CAAgB,IAAI,CAAC;EAC/E,MAAM,CAAC4H,cAAc,EAAEC,iBAAiB,CAAC,GAAG7H,QAAQ,CAAgB,IAAI,CAAC,EAAC;EAC1E,MAAM,CAAC8H,UAAU,EAAEC,aAAa,CAAC,GAAG/H,QAAQ,CAAe,IAAI,CAAC;EAChE,MAAM,CAACgI,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjI,QAAQ,CAGpD,IAAI,CAAC;;EAEf;EACA,MAAM,CAACkI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnI,QAAQ,CAAC;IACvDiH,UAAU,EAAE,KAAK;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACkB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrI,QAAQ,CAAC;IACzD8G,QAAQ,EAAE,KAAK;IACfK,GAAG,EAAE,KAAK;IACVC,WAAW,EAAE,KAAK;IAClBC,IAAI,EAAE,KAAK;IACXC,aAAa,EAAE,KAAK;IACpBC,OAAO,EAAE,IAAI,CAAC;EAChB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGvI,QAAQ,CAAoB;IAClEwI,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG5I,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC6I,eAAe,EAAEC,kBAAkB,CAAC,GAAG9I,QAAQ,CAAkB,EAAE,CAAC;EAC3E,MAAM,CAAC+I,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGhJ,QAAQ,CAAC,IAAI,CAAC;EAC1E,MAAM,CAACiJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGlJ,QAAQ,CAAM,IAAI,CAAC;;EAE7D;EACA,MAAM,CAACmJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGpJ,QAAQ,CAAgB,EAAE,CAAC;EACrE,MAAM,CAACqJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtJ,QAAQ,CAAgB,IAAI,CAAC;EAC/E,MAAM,CAACuJ,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxJ,QAAQ,CAAoB,EAAE,CAAC;EAC/E,MAAM,CAACyJ,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1J,QAAQ,CAAS,EAAE,CAAC;;EAEpE;EACA,MAAM,CAAC2J,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5J,QAAQ,CAAC,MAAM;IAC7D,MAAM6J,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC;IAChE,OAAOF,KAAK,KAAK,IAAI,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;EACpD,CAAC,CAAC;EAEF,MAAM,CAACK,eAAe,EAAEC,kBAAkB,CAAC,GAAGnK,QAAQ,CAAc,IAAIoK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhF;EACA,MAAMC,QAAQ,GAAGnK,MAAM,CAAoB,IAAI,CAAC;EAChD;EACA,MAAMoK,cAAc,GAAGpK,MAAM,CAAqB,IAAI,CAAC;;EAEvD;EACA,MAAMqK,mBAAmB,GAAGrK,MAAM,CAAC,CAAC,CAAC;;EAErC;EACA,MAAMsK,UAAU,GAAGA,CAAA,KAAM,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;;EAEnF;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,iBAAiB,GAAG,CAACrB,gBAAgB;IAC3CC,mBAAmB,CAACoB,iBAAiB,CAAC;IACtClB,YAAY,CAACmB,OAAO,CAAC,4BAA4B,EAAEjB,IAAI,CAACkB,SAAS,CAACF,iBAAiB,CAAC,CAAC;EACvF,CAAC;;EAED;EACA,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,SAAS,GAAGZ,UAAU,CAAC,CAAC;IAC9Bd,mBAAmB,CAAC0B,SAAS,CAAC;IAC9B9B,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,mBAAmB,CAAC,EAAE,CAAC;IACvB6B,oBAAoB,CAAC,CAAC;IACtB;IACAlB,kBAAkB,CAACmB,IAAI,IAAI;MACzB,MAAMC,MAAM,GAAG,IAAInB,GAAG,CAACkB,IAAI,CAAC;MAC5BC,MAAM,CAACC,MAAM,CAACJ,SAAS,CAAC,CAAC,CAAC;MAC1B,OAAOG,MAAM;IACf,CAAC,CAAC;IACFE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEN,SAAS,CAAC;IACrC,OAAOA,SAAS;EAClB,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAGA,CACzBC,IAAqC,EACrCC,OAAe,EACfC,QAAc,KACX;IACH,MAAMrD,OAAwB,GAAG;MAC/BsD,EAAE,EAAEvB,UAAU,CAAC,CAAC;MAChBoB,IAAI;MACJC,OAAO;MACPG,SAAS,EAAE,IAAIvB,IAAI,CAAC,CAAC;MACrBnG,MAAM,EAAEsH,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,WAAW;MAC9CE;IACF,CAAC;IAEDtC,mBAAmB,CAAC8B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE7C,OAAO,CAAC,CAAC;IAC/C,OAAOA,OAAO,CAACsD,EAAE;EACnB,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAGA,CAC5BC,SAAiB,EACjBC,OAAiC,KAC9B;IACH3C,mBAAmB,CAAC8B,IAAI,IACtBA,IAAI,CAACc,GAAG,CAACC,GAAG,IACVA,GAAG,CAACN,EAAE,KAAKG,SAAS,GAAG;MAAE,GAAGG,GAAG;MAAE,GAAGF;IAAQ,CAAC,GAAGE,GAClD,CACF,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,eAAe,GAAG,MAAO9G,KAAa,IAAK;IAC/C,IAAI,CAACiE,gBAAgB,IAAI,CAACjE,KAAK,CAAC+G,IAAI,CAAC,CAAC,EAAE;IAExC,MAAMC,OAAoB,GAAG;MAC3BT,EAAE,EAAEtC,gBAAgB;MACpBgD,KAAK,EAAEjH,KAAK,CAACkH,MAAM,GAAG,EAAE,GAAGlH,KAAK,CAACmH,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGnH,KAAK;MACjEwG,SAAS,EAAE,IAAIvB,IAAI,CAAC,CAAC;MACrBjF,KAAK;MACLoH,QAAQ,EAAE;QACR9F,QAAQ,EAAEc,cAAc,IAAIhB,aAAa,CAACE,QAAQ,CAACC,MAAM;QACzDI,GAAG,EAAEK,SAAS,IAAI,EAAE;QACpBJ,WAAW,EAAEM,iBAAiB,IAAI,EAAE;QACpCL,IAAI,EAAES,UAAU,IAAI,EAAE;QACtBR,aAAa,EAAEU;MACjB,CAAC;MACD6E,YAAY,EAAE3G;IAChB,CAAC;;IAED;IACAkD,gBAAgB,CAACkC,IAAI,IAAI;MACvB,MAAMwB,QAAQ,GAAGxB,IAAI,CAACyB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAKtC,gBAAgB,CAAC;MAC1D,IAAIqD,QAAQ,EAAE;QACZ,OAAOxB,IAAI,CAACc,GAAG,CAACY,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAKtC,gBAAgB,GAAG+C,OAAO,GAAGQ,CAAC,CAAC;MAC/D;MACA,OAAO,CAACR,OAAO,EAAE,GAAGlB,IAAI,CAAC;IAC3B,CAAC,CAAC;;IAEF;IACA,IAAI;MAAA,IAAA2B,qBAAA;MACF,MAAMC,WAAmC,GAAG;QAC1CC,UAAU,EAAE1D,gBAAgB;QAC5BgD,KAAK,EAAED,OAAO,CAACC,KAAK;QACpBjH,KAAK,EAAEgH,OAAO,CAAChH,KAAK;QACpBoH,QAAQ,EAAEJ,OAAO,CAACI,QAAQ;QAC1BQ,aAAa,GAAAH,qBAAA,GAAET,OAAO,CAACK,YAAY,cAAAI,qBAAA,cAAAA,qBAAA,GAAII;MACzC,CAAC;MAED,MAAMhM,kBAAkB,CAACiM,qBAAqB,CAACJ,WAAW,CAAC;MAC3DzB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEjC,gBAAgB,CAAC;IAC5C,CAAC,CAAC,OAAO7D,KAAK,EAAE;MACd6F,OAAO,CAAC7F,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC;IACF;EACF,CAAC;;EAED;EACA,MAAM2H,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF9B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAExF,oBAAoB,CAAC;MACtD,MAAM0G,QAAQ,GAAG,MAAMvL,kBAAkB,CAACmM,gBAAgB,CACxD,CAAC,EACD,EAAE,EACFtH,oBAAoB,IAAImH,SAC1B,CAAC;MACD5B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEkB,QAAQ,CAAC;MAEjC,MAAMa,SAAwB,GAAGb,QAAQ,CAACc,QAAQ,CAACtB,GAAG,CAACuB,OAAO,KAAK;QACjE5B,EAAE,EAAE4B,OAAO,CAAC5B,EAAE;QACdU,KAAK,EAAEkB,OAAO,CAAClB,KAAK;QACpBT,SAAS,EAAE,IAAIvB,IAAI,CAACkD,OAAO,CAAC3B,SAAS,CAAC;QACtCxG,KAAK,EAAEmI,OAAO,CAACnI,KAAK;QACpBoH,QAAQ,EAAEe,OAAO,CAACf,QAAQ;QAC1BC,YAAY,EAAEc,OAAO,CAACP,aAAa,IAAI;MACzC,CAAC,CAAC,CAAC;MAEHhE,gBAAgB,CAACqE,SAAS,CAAC;MAC3BhC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE+B,SAAS,CAACf,MAAM,CAAC;MAC/CjB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE+B,SAAS,CAAC;IACtC,CAAC,CAAC,OAAO7H,KAAK,EAAE;MACd6F,OAAO,CAAC7F,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAMgI,mBAAmB,GAAG,MAAOC,SAAiB,IAAK;IACvD,IAAI;MACFpC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEmC,SAAS,CAAC;;MAEpC;MACA,IAAIrB,OAAO,GAAGrD,aAAa,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAK8B,SAAS,CAAC;MACzDpC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEc,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC;;MAEjD;MACA,IAAI,CAACA,OAAO,EAAE;QACZf,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;QAC/B,MAAMkB,QAAQ,GAAG,MAAMvL,kBAAkB,CAACyM,cAAc,CAACD,SAAS,CAAC;QACnErB,OAAO,GAAG;UACRT,EAAE,EAAEa,QAAQ,CAACO,UAAU;UACvBV,KAAK,EAAEG,QAAQ,CAACH,KAAK;UACrBT,SAAS,EAAE,IAAIvB,IAAI,CAACmC,QAAQ,CAACmB,UAAU,CAAC;UACxCvI,KAAK,EAAEoH,QAAQ,CAACpH,KAAK;UACrBoH,QAAQ,EAAEA,QAAQ,CAACA,QAAQ;UAC3BC,YAAY,EAAED,QAAQ,CAACQ,aAAa,IAAI;QAC1C,CAAC;QACD3B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEc,OAAO,CAAC;MACrC;MAEA,IAAI,CAACA,OAAO,EAAE;QACZf,OAAO,CAAC7F,KAAK,CAAC,YAAY,EAAEiI,SAAS,CAAC;QACtC;MACF;MAEApC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBK,EAAE,EAAES,OAAO,CAACT,EAAE;QACdU,KAAK,EAAED,OAAO,CAACC,KAAK;QACpBuB,WAAW,EAAE,CAAC,CAACxB,OAAO,CAACI,QAAQ,CAAC9F,QAAQ;QACxCmH,MAAM,EAAE,CAAC,CAACzB,OAAO,CAACI,QAAQ,CAACzF,GAAG;QAC9B+G,cAAc,EAAE,CAAC,CAAC1B,OAAO,CAACI,QAAQ,CAACxF,WAAW;QAC9C+G,OAAO,EAAE,CAAC,EAAE3B,OAAO,CAACI,QAAQ,CAACvF,IAAI,IAAImF,OAAO,CAACI,QAAQ,CAACvF,IAAI,CAACqF,MAAM,GAAG,CAAC,CAAC;QACtE0B,gBAAgB,EAAE,CAAC,CAAC5B,OAAO,CAACI,QAAQ,CAACtF;MACvC,CAAC,CAAC;MAEFgC,oBAAoB,CAACuE,SAAS,CAAC;MAC/BnE,mBAAmB,CAACmE,SAAS,CAAC;;MAE9B;MACApG,YAAY,CAAC+E,OAAO,CAACI,QAAQ,CAACzF,GAAG,CAAC;MAClCQ,oBAAoB,CAAC6E,OAAO,CAACI,QAAQ,CAACxF,WAAW,CAAC;MAClDS,iBAAiB,CAAC2E,OAAO,CAACI,QAAQ,CAAC9F,QAAQ,CAAC,CAAC,CAAC;MAC9CiB,aAAa,CAACyE,OAAO,CAACI,QAAQ,CAACvF,IAAI,CAAC;MACpCY,sBAAsB,CAACuE,OAAO,CAACI,QAAQ,CAACtF,aAAa,CAAC;;MAEtD;MACAa,mBAAmB,CAAC;QAClBlB,UAAU,EAAE,CAAC,CAACuF,OAAO,CAACI,QAAQ,CAACxF,WAAW;QAC1CF,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACAL,gBAAgB,CAAC;QACfC,QAAQ,EAAE;UACRC,MAAM,EAAEyF,OAAO,CAACI,QAAQ,CAAC9F,QAAQ,IAAI,EAAE;UACvCE,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE,CAAC,CAACuF,OAAO,CAACI,QAAQ,CAAC9F,QAAQ;UACvCI,SAAS,EAAE;QACb,CAAC;QACDC,GAAG,EAAE;UACHJ,MAAM,EAAEyF,OAAO,CAACI,QAAQ,CAACzF,GAAG,IAAI,EAAE;UAClCH,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE,CAAC,CAACuF,OAAO,CAACI,QAAQ,CAACzF,GAAG;UAClCD,SAAS,EAAE;QACb,CAAC;QACDE,WAAW,EAAE;UACXL,MAAM,EAAEyF,OAAO,CAACI,QAAQ,CAACxF,WAAW,IAAI,EAAE;UAC1CJ,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE,CAAC,CAACuF,OAAO,CAACI,QAAQ,CAACxF,WAAW;UAC1CF,SAAS,EAAE;QACb,CAAC;QACDG,IAAI,EAAE;UACJN,MAAM,EAAEyF,OAAO,CAACI,QAAQ,CAACvF,IAAI,IAAImF,OAAO,CAACI,QAAQ,CAACvF,IAAI,CAACqF,MAAM,GAAG,CAAC,GACzD,SAASF,OAAO,CAACI,QAAQ,CAACvF,IAAI,CAACqF,MAAM,QAAQ,GAAG,EAAE;UAC1D1F,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE,CAAC,EAAEuF,OAAO,CAACI,QAAQ,CAACvF,IAAI,IAAImF,OAAO,CAACI,QAAQ,CAACvF,IAAI,CAACqF,MAAM,GAAG,CAAC,CAAC;UACzExF,SAAS,EAAE;QACb,CAAC;QACDI,aAAa,EAAE;UACbP,MAAM,EAAEyF,OAAO,CAACI,QAAQ,CAACtF,aAAa,GAC9B,OAAOkF,OAAO,CAACI,QAAQ,CAACtF,aAAa,CAACsE,IAAI,IAAI,IAAI,SAAS,GAAG,EAAE;UACxE5E,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE,CAAC,CAACuF,OAAO,CAACI,QAAQ,CAACtF,aAAa;UAC5CJ,SAAS,EAAE;QACb,CAAC;QACDK,OAAO,EAAE;UACPR,MAAM,EAAE,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE,KAAK;UACjBC,SAAS,EAAE;QACb;MACF,CAAC,CAAC;MAEFuE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAM1E,QAA2B,GAAG,CAClC;QACA+E,EAAE,EAAEvB,UAAU,CAAC,CAAC;QAChBoB,IAAI,EAAE,MAAM;QACZC,OAAO,EAAEW,OAAO,CAAChH,KAAK;QACtBwG,SAAS,EAAEQ,OAAO,CAACR,SAAS;QAC5B1H,MAAM,EAAE;MACV,CAAC,CACF;MAED,IAAIkI,OAAO,CAACI,QAAQ,CAAC9F,QAAQ,EAAE;QAC7BE,QAAQ,CAACqH,IAAI,CAAC;UACZtC,EAAE,EAAEvB,UAAU,CAAC,CAAC;UAChBoB,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEW,OAAO,CAACI,QAAQ,CAAC9F,QAAQ;UAClCkF,SAAS,EAAE,IAAIvB,IAAI,CAAC+B,OAAO,CAACR,SAAS,CAACsC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;UACvDhK,MAAM,EAAE,WAAW;UACnBwH,QAAQ,EAAE;YAAEyC,MAAM,EAAE,UAAU;YAAEC,MAAM,EAAE;UAAU;QACpD,CAAC,CAAC;MACJ;MAEA,IAAIhC,OAAO,CAACI,QAAQ,CAACzF,GAAG,EAAE;QACxBH,QAAQ,CAACqH,IAAI,CAAC;UACZtC,EAAE,EAAEvB,UAAU,CAAC,CAAC;UAChBoB,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEW,OAAO,CAACI,QAAQ,CAACzF,GAAG;UAC7B6E,SAAS,EAAE,IAAIvB,IAAI,CAAC+B,OAAO,CAACR,SAAS,CAACsC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;UACvDhK,MAAM,EAAE,WAAW;UACnBwH,QAAQ,EAAE;YAAEyC,MAAM,EAAE,KAAK;YAAEC,MAAM,EAAE,UAAU;YAAEC,KAAK,EAAE;UAAK;QAC7D,CAAC,CAAC;MACJ;MAEA,IAAIjC,OAAO,CAACI,QAAQ,CAACxF,WAAW,EAAE;QAChCJ,QAAQ,CAACqH,IAAI,CAAC;UACZtC,EAAE,EAAEvB,UAAU,CAAC,CAAC;UAChBoB,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEW,OAAO,CAACI,QAAQ,CAACxF,WAAW;UACrC4E,SAAS,EAAE,IAAIvB,IAAI,CAAC+B,OAAO,CAACR,SAAS,CAACsC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;UACvDhK,MAAM,EAAE,WAAW;UACnBwH,QAAQ,EAAE;YAAEyC,MAAM,EAAE,aAAa;YAAEC,MAAM,EAAE;UAAW;QACxD,CAAC,CAAC;MACJ;MAEA,IAAIhC,OAAO,CAACI,QAAQ,CAACvF,IAAI,IAAImF,OAAO,CAACI,QAAQ,CAACvF,IAAI,CAACqF,MAAM,GAAG,CAAC,EAAE;QAC7D1F,QAAQ,CAACqH,IAAI,CAAC;UACZtC,EAAE,EAAEvB,UAAU,CAAC,CAAC;UAChBoB,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAE,SAASW,OAAO,CAACI,QAAQ,CAACvF,IAAI,CAACqF,MAAM,QAAQ;UACtDV,SAAS,EAAE,IAAIvB,IAAI,CAAC+B,OAAO,CAACR,SAAS,CAACsC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;UACvDhK,MAAM,EAAE,WAAW;UACnBwH,QAAQ,EAAE;YAAEyC,MAAM,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAU;QAChD,CAAC,CAAC;MACJ;MAEA,IAAIhC,OAAO,CAACI,QAAQ,CAACtF,aAAa,EAAE;QAClCN,QAAQ,CAACqH,IAAI,CAAC;UACZtC,EAAE,EAAEvB,UAAU,CAAC,CAAC;UAChBoB,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAE,OAAOW,OAAO,CAACI,QAAQ,CAACtF,aAAa,CAACsE,IAAI,WAAW;UAC9DI,SAAS,EAAE,IAAIvB,IAAI,CAAC+B,OAAO,CAACR,SAAS,CAACsC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;UACvDhK,MAAM,EAAE,WAAW;UACnBwH,QAAQ,EAAE;YAAEyC,MAAM,EAAE,eAAe;YAAEC,MAAM,EAAE,UAAU;YAAEE,eAAe,EAAE;UAAK;QACjF,CAAC,CAAC;MACJ;MAEAlF,mBAAmB,CAACxC,QAAQ,CAAC;MAC7ByE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE1E,QAAQ,CAAC0F,MAAM,CAAC;IACnD,CAAC,CAAC,OAAO9G,KAAK,EAAE;MACd6F,OAAO,CAAC7F,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAM+I,mBAAmB,GAAG,MAAOd,SAAiB,IAAK;IACvD,IAAI;MACFpC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEmC,SAAS,CAAC;;MAErC;MACA,MAAMe,SAAS,GAAGC,MAAM,CAACC,OAAO,CAAC,uBAAuB,CAAC;MACzD,IAAI,CAACF,SAAS,EAAE;QACd;MACF;;MAEA;MACA,MAAMvN,kBAAkB,CAAC0N,iBAAiB,CAAClB,SAAS,CAAC;;MAErD;MACAzE,gBAAgB,CAACkC,IAAI,IAAIA,IAAI,CAAC0D,MAAM,CAAChC,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAK8B,SAAS,CAAC,CAAC;;MAE9D;MACA,IAAIxE,iBAAiB,KAAKwE,SAAS,EAAE;QACnC1C,oBAAoB,CAAC,CAAC;MACxB;MAEAM,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAO9F,KAAK,EAAE;MACd6F,OAAO,CAAC7F,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCqJ,KAAK,CAAC,YAAY,CAAC;IACrB;EACF,CAAC;;EAED;EACAhP,SAAS,CAAC,MAAM;IACd,MAAMiP,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF7I,qBAAqB,CAAC,IAAI,CAAC;QAC3B,MAAMuG,QAAQ,GAAG,MAAMnM,cAAc,CAAC,CAAC;QACvCwF,cAAc,CAAC2G,QAAQ,CAACvF,IAAI,CAAC;;QAE7B;QACA,IAAIuF,QAAQ,CAACvF,IAAI,CAACqF,MAAM,GAAG,CAAC,EAAE;UAC5BvG,uBAAuB,CAACyG,QAAQ,CAACvF,IAAI,CAAC,CAAC,CAAC,CAAC0E,EAAE,CAAC;QAC9C;MACF,CAAC,CAAC,OAAOnG,KAAK,EAAE;QACd6F,OAAO,CAAC7F,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QAClCC,QAAQ,CAAC,yBAAyB,CAAC;MACrC,CAAC,SAAS;QACRQ,qBAAqB,CAAC,KAAK,CAAC;MAC9B;IACF,CAAC;IAED6I,gBAAgB,CAAC,CAAC;;IAElB;IACA/D,oBAAoB,CAAC,CAAC;;IAEtB;IACAoC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAtN,SAAS,CAAC,MAAM;IACd,IAAIiG,oBAAoB,EAAE;MACxBqH,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACrH,oBAAoB,CAAC,CAAC;;EAE1B;EACAjG,SAAS,CAAC,MAAM;IACd,MAAMkP,UAAU,GACd,CAACzJ,OAAO;IAAI;IACZ+D,gBAAgB;IAAI;IACpB,CAACS,eAAe,CAACkF,GAAG,CAAC3F,gBAAgB,CAAC;IAAI;IAC1CjC,SAAS;IAAI;IACbM,UAAU;IAAI;IACdyB,gBAAgB,CAACmD,MAAM,GAAG,CAAC,CAAC,CAAC;;IAE/B,IAAIyC,UAAU,EAAE;MACd,MAAME,WAAW,GAAG9F,gBAAgB,CAACwD,IAAI,CAACV,GAAG,IAAIA,GAAG,CAACT,IAAI,KAAK,MAAM,CAAC;MACrE,IAAIyD,WAAW,EAAE;QACf5D,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;;QAElC;QACA,MAAM4D,WAAW,GAAGC,UAAU,CAAC,YAAY;UACzC,IAAI;YACF,MAAMjD,eAAe,CAAC+C,WAAW,CAACxD,OAAO,CAAC;YAC1C,MAAM0B,iBAAiB,CAAC,CAAC;YACzB;YACApD,kBAAkB,CAACmB,IAAI,IAAI,IAAIlB,GAAG,CAACkB,IAAI,CAAC,CAACkE,GAAG,CAAC/F,gBAAgB,CAAC,CAAC;YAC/DgC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;UAC7B,CAAC,CAAC,OAAO9F,KAAK,EAAE;YACd6F,OAAO,CAAC7F,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACvC;QACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;QAEV,OAAO,MAAM6J,YAAY,CAACH,WAAW,CAAC;MACxC;IACF;EACF,CAAC,EAAE,CAAC5J,OAAO,EAAE+D,gBAAgB,EAAEjC,SAAS,EAAEM,UAAU,EAAEyB,gBAAgB,EAAEW,eAAe,CAAC,CAAC;;EAEzF;EACA,MAAMwF,cAAc,GAAIC,OAAe,IAAK;IAC1CtH,oBAAoB,CAACiD,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACP,CAACqE,OAAO,GAAG,CAACrE,IAAI,CAACqE,OAAO;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIzI,GAAW,IAAK;IACtCsE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEvE,GAAG,CAAC;IACpC;IACAN,gBAAgB,CAACyE,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPnE,GAAG,EAAE;QACH,GAAGmE,IAAI,CAACnE,GAAG;QACXD,SAAS,EAAE,KAAK;QAChB2I,WAAW,EAAE1I,GAAG;QAChBF,UAAU,EAAE;MACd;IACF,CAAC,CAAC,CAAC;IACHQ,YAAY,CAACN,GAAG,CAAC;EACnB,CAAC;;EAED;EACA,MAAM2I,sBAAsB,GAAI1I,WAAmB,IAAK;IACtDqE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;MAC7BqE,iBAAiB,EAAE3I,WAAW,GAAGA,WAAW,CAACsF,MAAM,GAAG,CAAC;MACvDsD,wBAAwB,EAAEtI,iBAAiB,GAAG,UAAUA,iBAAiB,CAACgF,MAAM,EAAE,GAAG;IACvF,CAAC,CAAC;;IAEF;IACA7F,gBAAgB,CAACyE,IAAI,IAAI;MACvB,OAAO;QACL,GAAGA,IAAI;QACPlE,WAAW,EAAE;UACX,GAAGkE,IAAI,CAAClE,WAAW;UACnBF,SAAS,EAAE,KAAK;UAChBD,UAAU,EAAE,IAAI,CAAE;QACpB;MACF,CAAC;IACH,CAAC,CAAC;;IAEF;IACAwE,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMuE,eAAe,GAAI5I,IAAW,IAAK;IACvCoE,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9B;IACA7E,gBAAgB,CAACyE,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPjE,IAAI,EAAE;QACJ,GAAGiE,IAAI,CAACjE,IAAI;QACZH,SAAS,EAAE,KAAK;QAChBD,UAAU,EAAE,IAAI,CAAE;MACpB;IACF,CAAC,CAAC,CAAC;IACHc,aAAa,CAACV,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,MAAM6I,wBAAwB,GAAG,MAAO5I,aAAqC,IAAK;IAChFmE,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEpE,aAAa,CAAC;IACnEmE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE5D,UAAU,CAAC;IACrC2D,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;;IAE/B;IACA7E,gBAAgB,CAACyE,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPhE,aAAa,EAAE;QACb,GAAGgE,IAAI,CAAChE,aAAa;QACrBJ,SAAS,EAAE,KAAK;QAChBD,UAAU,EAAE,IAAI,CAAE;MACpB;IACF,CAAC,CAAC,CAAC;;IAEH;IACAgB,sBAAsB,CAACX,aAAa,CAAC;IACrCmE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEpE,aAAa,CAAC;;IAEzC;IACA;IACA3B,UAAU,CAAC,KAAK,CAAC;IACjB8F,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;;IAElC;IACAD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;;IAErC;IACA6D,UAAU,CAAC,YAAY;MACrB,IAAI9F,gBAAgB,EAAE;QACpB;QACA,MAAM4F,WAAW,GAAG9F,gBAAgB,CAACwD,IAAI,CAACV,GAAG,IAAIA,GAAG,CAACT,IAAI,KAAK,MAAM,CAAC;QACrE,IAAIyD,WAAW,EAAE;UACf,IAAI;YACF,MAAM/C,eAAe,CAAC+C,WAAW,CAACxD,OAAO,CAAC;YAC1C;YACA,MAAM0B,iBAAiB,CAAC,CAAC;YACzB9B,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;UACvC,CAAC,CAAC,OAAO9F,KAAK,EAAE;YACd6F,OAAO,CAAC7F,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;UACnC;QACF;MACF;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMuK,mBAAmB,GAAIrJ,QAAgB,IAAK;IAChD2E,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9B;IACA7E,gBAAgB,CAACyE,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPxE,QAAQ,EAAE;QACR,GAAGwE,IAAI,CAACxE,QAAQ;QAChBI,SAAS,EAAE,KAAK;QAChBD,UAAU,EAAE;MACd;IACF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMmJ,YAAY,GAAIP,WAA6B,IAAK;IACtDpE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEmE,WAAW,CAAC;IACpDhK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;IAEhB;IACA,MAAMwK,mBAAmB,GAAGC,MAAM,CAACC,MAAM,CAAC3J,aAAa,CAAC,CAAC4J,KAAK,CAACjC,MAAM,IAAI,CAACA,MAAM,CAACrH,SAAS,CAAC;;IAE3F;IACA,MAAMuJ,gBAAgB,GAAGZ,WAAW,CAACzI,WAAW,IACxB,OAAOyI,WAAW,CAACzI,WAAW,KAAK,QAAQ,IAC3CyI,WAAW,CAACzI,WAAW,CAACmF,IAAI,CAAC,CAAC,GAC9BsD,WAAW,CAACzI,WAAW,GAAG,IAAI;;IAEtD;IACAmI,UAAU,CAAC,YAAY;MACrB,IAAI9F,gBAAgB,EAAE;QACpB;QACA,MAAM4F,WAAW,GAAG9F,gBAAgB,CAACwD,IAAI,CAACV,GAAG,IAAIA,GAAG,CAACT,IAAI,KAAK,MAAM,CAAC;QACrE,IAAIyD,WAAW,EAAE;UACf,MAAM/C,eAAe,CAAC+C,WAAW,CAACxD,OAAO,CAAC;UAC1C;UACA,MAAM0B,iBAAiB,CAAC,CAAC;UACzB9B,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;QAC7B;MACF;IACF,CAAC,EAAE,IAAI,CAAC;;IAER;IACA7E,gBAAgB,CAACyE,IAAI,IAAI;MACvB,MAAMoF,OAAO,GAAG;QAAE,GAAGpF;MAAK,CAAC;MAC3BgF,MAAM,CAACK,IAAI,CAACD,OAAO,CAAC,CAACE,OAAO,CAACC,GAAG,IAAI;QAClC,MAAMtC,MAAM,GAAGmC,OAAO,CAACG,GAAG,CAAyB;QACnDtC,MAAM,CAACrH,SAAS,GAAG,KAAK;;QAExB;QACA,IAAI2J,GAAG,KAAK,KAAK,IAAIhB,WAAW,CAAC1I,GAAG,EAAE;UACpCoH,MAAM,CAACtH,UAAU,GAAG,IAAI;QAC1B;QACA,IAAI4J,GAAG,KAAK,aAAa,IAAIJ,gBAAgB,EAAE;UAC7ClC,MAAM,CAACtH,UAAU,GAAG,IAAI;UACxBsH,MAAM,CAACxH,MAAM,GAAG0J,gBAAgB,CAAC,CAAC;UAClChF,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE+E,gBAAgB,CAAC9D,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;QACpF;QACA,IAAIkE,GAAG,KAAK,MAAM,IAAIhB,WAAW,CAACiB,OAAO,IAAIjB,WAAW,CAACiB,OAAO,CAACpE,MAAM,GAAG,CAAC,EAAE;UAC3E6B,MAAM,CAACtH,UAAU,GAAG,IAAI;QAC1B;QACA,IAAI4J,GAAG,KAAK,eAAe,KAAKhB,WAAW,CAACkB,kBAAkB,IAAIlB,WAAW,CAACmB,oBAAoB,CAAC,EAAE;UACnGzC,MAAM,CAACtH,UAAU,GAAG,IAAI;QAC1B;MACF,CAAC,CAAC;MACF,OAAOyJ,OAAO;IAChB,CAAC,CAAC;;IAEF;IACAjJ,YAAY,CAACoI,WAAW,CAAC1I,GAAG,CAAC;;IAE7B;IACAsE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEhE,iBAAiB,GAAGA,iBAAiB,CAACgF,MAAM,GAAG,CAAC,CAAC;IAE/E3E,aAAa,CAAC8H,WAAW,CAACiB,OAAO,CAAC;IAClC7I,sBAAsB,CAAC;MACrB2D,IAAI,EAAEiE,WAAW,CAACkB,kBAAkB;MACpCE,MAAM,EAAEpB,WAAW,CAACmB;IACtB,CAAC,CAAC;;IAEF;IACAvF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBvE,GAAG,EAAE0I,WAAW,CAAC1I,GAAG,GAAG0I,WAAW,CAAC1I,GAAG,CAACwF,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG,IAAI;MACtEvF,WAAW,EAAEqJ,gBAAgB,GAAGA,gBAAgB,CAAC9D,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG,IAAI;MAChFmE,OAAO,EAAEjB,WAAW,CAACiB,OAAO,GAAG,GAAGjB,WAAW,CAACiB,OAAO,CAACpE,MAAM,MAAM,GAAG,IAAI;MACzEpF,aAAa,EAAEuI,WAAW,CAACkB;IAC7B,CAAC,CAAC;;IAEF;IACA,IAAIN,gBAAgB,EAAE;MACpBhF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE+E,gBAAgB,CAAC/D,MAAM,CAAC;IACrD;;IAEA;IACA;IACA;EACF,CAAC;;EAED;EACA,MAAMwE,WAAW,GAAItL,KAAY,IAAK;IACpC6F,OAAO,CAAC7F,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;;IAE7B;IACA,IAAIuL,YAAY,GAAGvL,KAAK,CAAC6C,OAAO,IAAI,aAAa;;IAEjD;IACA,IAAI0I,YAAY,CAACC,QAAQ,CAAC,WAAW,CAAC,IAClCD,YAAY,CAACC,QAAQ,CAAC,IAAI,CAAC,IAC3BD,YAAY,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;MAChC;MACA,IAAI5Q,oBAAoB,EAAE;QACxB2Q,YAAY,GAAG3Q,oBAAoB;MACrC,CAAC,MAAM;QACL2Q,YAAY,GAAG,gBAAgB;MACjC;IACF;IAEAtL,QAAQ,CAACsL,YAAY,CAAC;IACtBxL,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;;IAEnB;IACAkB,gBAAgB,CAACyE,IAAI,IAAI;MACvB,MAAMoF,OAAO,GAAG;QAAE,GAAGpF;MAAK,CAAC;MAC3BgF,MAAM,CAACK,IAAI,CAACD,OAAO,CAAC,CAACE,OAAO,CAACC,GAAG,IAAI;QAClC,MAAMtC,MAAM,GAAGmC,OAAO,CAACG,GAAG,CAAyB;QACnDtC,MAAM,CAACrH,SAAS,GAAG,KAAK;MAC1B,CAAC,CAAC;MACF,OAAOwJ,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAIC,UAAkB,IAAK;IAC/C7K,cAAc,CAAC6K,UAAU,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACzJ,UAAU,EAAE,OAAO,CAAC;IACzB,OAAO6C,IAAI,CAAC6G,IAAI,CAAC1J,UAAU,CAAC4E,MAAM,GAAGhG,QAAQ,CAAC;EAChD,CAAC;;EAED;EACA,MAAM+K,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAAC3J,UAAU,EAAE,OAAO,EAAE;IAC1B,MAAM4J,UAAU,GAAG,CAAClL,WAAW,GAAG,CAAC,IAAIE,QAAQ;IAC/C,MAAMiL,QAAQ,GAAGD,UAAU,GAAGhL,QAAQ;IACtC,OAAOoB,UAAU,CAAC8J,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC/C,CAAC;;EAED;EACA,MAAMtG,oBAAoB,GAAGA,CAAA,KAAM;IACjCxF,QAAQ,CAAC,IAAI,CAAC;IACdF,UAAU,CAAC,KAAK,CAAC;IACjBI,kBAAkB,CAAC,EAAE,CAAC;IACtBU,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;;IAGnB;IACAgF,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;;IAE9B;IACA7E,gBAAgB,CAAC;MACfC,QAAQ,EAAE;QACRC,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE;MACb,CAAC;MACDC,GAAG,EAAE;QACHJ,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE;MACb,CAAC;MACDE,WAAW,EAAE;QACXL,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE;MACb,CAAC;MACDG,IAAI,EAAE;QACJN,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE;MACb,CAAC;MACDI,aAAa,EAAE;QACbP,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE;MACb,CAAC;MACDK,OAAO,EAAE;QACPR,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE;MACb;IACF,CAAC,CAAC;;IAEF;IACAO,YAAY,CAAC,IAAI,CAAC;IAClBE,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;IACnBE,sBAAsB,CAAC,IAAI,CAAC;;IAE5B;IACAE,mBAAmB,CAAC;MAClBlB,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE;IACb,CAAC,CAAC;;IAEF;IACA,IAAIoD,cAAc,CAACuH,OAAO,EAAE;MAC1B,IAAI;QACFvH,cAAc,CAACuH,OAAO,CAACC,KAAK,CAAC,CAAC;QAC9BrG,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MACpC,CAAC,CAAC,OAAOqG,GAAG,EAAE;QACZtG,OAAO,CAAC7F,KAAK,CAAC,qBAAqB,EAAEmM,GAAG,CAAC;MAC3C;MACAzH,cAAc,CAACuH,OAAO,GAAG,IAAI;IAC/B;EACF,CAAC;;EAED;EACA5R,SAAS,CAAC,MAAM;IACd;IACA,MAAMuE,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnC;MACA,IAAIjE,oBAAoB,IAAI,CAACoF,KAAK,IAAI,CAACF,OAAO,EAAE;QAC9CG,QAAQ,CAACrF,oBAAoB,CAAC;MAChC;;MAEA;MACA,IAAID,oBAAoB,KAAKD,wBAAwB,CAACsE,SAAS,IAC3DgB,KAAK,KAAKA,KAAK,KAAKpF,oBAAoB,IAAIoF,KAAK,CAACwL,QAAQ,CAAC,IAAI,CAAC,IAAIxL,KAAK,CAACwL,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;QAC9FvL,QAAQ,CAAC,IAAI,CAAC;MAChB;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMnB,aAAa,CAACF,UAAU,CAAC;EACxC,CAAC,EAAE,CAACoB,KAAK,EAAEF,OAAO,CAAC,CAAC;;EAEpB;EACA,MAAMsM,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAItM,OAAO,EAAE;;IAEb;IACA,IAAInF,oBAAoB,KAAKD,wBAAwB,CAAC2E,KAAK,IACvD1E,oBAAoB,KAAKD,wBAAwB,CAAC4E,YAAY,EAAE;MAClE;MACA,IAAI;QACF,MAAM+M,GAAG,GAAG5R,oBAAoB,CAAC,CAAC;QAClC,MAAM6R,SAAS,GAAG,MAAMD,GAAG,CAACE,OAAO,CAAC,CAAC;QACrC,IAAI,CAACD,SAAS,EAAE;UACdrM,QAAQ,CAACrF,oBAAoB,IAAI,gBAAgB,CAAC;UAClD;QACF;MACF,CAAC,CAAC,OAAOoF,KAAK,EAAE;QACdC,QAAQ,CAACrF,oBAAoB,IAAI,gBAAgB,CAAC;QAClD;MACF;IACF;IAEAqF,QAAQ,CAAC,IAAI,CAAC;IACdF,UAAU,CAAC,IAAI,CAAC;;IAEhB;IACA,MAAMyM,YAAY,GAAG5M,KAAK,CAAC+G,IAAI,CAAC,CAAC;;IAEjC;IACA9G,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACAM,kBAAkB,CAAC,EAAE,CAAC;IACtBU,cAAc,CAAC,CAAC,CAAC;IACjBgB,YAAY,CAAC,IAAI,CAAC;IAClBE,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;IACnBE,sBAAsB,CAAC,IAAI,CAAC;;IAE5B;IACAE,mBAAmB,CAAC;MAClBlB,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE;IACb,CAAC,CAAC;IAEF,IAAI,CAACkL,YAAY,EAAE;MACjBvM,QAAQ,CAAC,UAAU,CAAC;MACpBF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;;IAEA;IACA,IAAI,CAACO,oBAAoB,EAAE;MACzBL,QAAQ,CAAC,YAAY,CAAC;MACtBF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;;IAEA;IACA,IAAIyF,SAAS,GAAG3B,gBAAgB;IAChC,IAAI,CAAC2B,SAAS,IAAI/B,iBAAiB,EAAE;MACnC+B,SAAS,GAAGD,oBAAoB,CAAC,CAAC;IACpC;;IAEA;IACA,MAAMkH,aAAa,GAAG1G,kBAAkB,CAAC,MAAM,EAAEyG,YAAY,CAAC;;IAE9D;IACA3G,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;;IAEtB;IACA4G,YAAY,CAACT,OAAO,CAACU,KAAK,CAAC,CAAC;;IAE5B;IACA1L,gBAAgB,CAAC;MACfC,QAAQ,EAAE;QACRC,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE;MACb,CAAC;MACDC,GAAG,EAAE;QACHJ,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE;MACb,CAAC;MACDE,WAAW,EAAE;QACXL,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE;MACb,CAAC;MACDG,IAAI,EAAE;QACJN,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE;MACb,CAAC;MACDI,aAAa,EAAE;QACbP,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE;MACb,CAAC;MACDK,OAAO,EAAE;QACPR,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE;MACb;IACF,CAAC,CAAC;;IAEF;IACAmB,oBAAoB,CAACiD,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACPxE,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAyI,UAAU,CAAC,MAAM;MACf,MAAMiD,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,4BAA4B,CAAC;MAC9E,IAAIF,iBAAiB,EAAE;QACrB;QACCA,iBAAiB,CAAiBG,KAAK,CAACC,OAAO,GAAG,OAAO;QACzDJ,iBAAiB,CAAiBG,KAAK,CAACE,SAAS,GAAG,OAAO;;QAE5D;QACAL,iBAAiB,CAACM,cAAc,CAAC;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAQ,CAAC,CAAC;QACxEvH,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC7C,CAAC,MAAM;QACLD,OAAO,CAACwH,IAAI,CAAC,WAAW,CAAC;MAC3B;;MAEA;MACA,MAAMC,WAAW,GAAGT,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;MAC/D,IAAIQ,WAAW,EAAE;QACdA,WAAW,CAAiBP,KAAK,CAACE,SAAS,GAAG,OAAO;QACrDK,WAAW,CAAiBP,KAAK,CAACC,OAAO,GAAG,OAAO;QACpDnH,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACrC,CAAC,MAAM;QACLD,OAAO,CAACwH,IAAI,CAAC,WAAW,CAAC;MAC3B;IACF,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI;MACF;MACAxH,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;;MAE7B;MACA,MAAM;QAAEyH;MAAuB,CAAC,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC;;MAExD;MACAA,sBAAsB,CACpBf,YAAY,EACZgB,aAAa,EACbhD,YAAY,EACZc,WAAW,EACXtB,cAAc,EACdE,sBAAsB,EACtBG,eAAe,EACfC,wBAAwB,EACxBhK,oBAAoB,EACpBI,mBACF,CAAC;MAEDmF,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;IACzB,CAAC,CAAC,OAAO9F,KAAK,EAAE;MACd6F,OAAO,CAAC7F,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChCC,QAAQ,CAAC,YAAYD,KAAK,YAAYyN,KAAK,GAAGzN,KAAK,CAAC6C,OAAO,GAAG,MAAM,EAAE,CAAC;MACvE9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA1F,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACA,IAAIqK,cAAc,CAACuH,OAAO,EAAE;QAC1BvH,cAAc,CAACuH,OAAO,CAACC,KAAK,CAAC,CAAC;QAC9BxH,cAAc,CAACuH,OAAO,GAAG,IAAI;MAC/B;;MAEA;MACAzR,wBAAwB,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAH,SAAS,CAAC,MAAM;IACd,IAAI+H,mBAAmB,IAAIF,UAAU,IAAIA,UAAU,CAAC4E,MAAM,GAAG,CAAC,IAAIrC,QAAQ,CAACwH,OAAO,EAAE;MAClF;MACA,IAAIxH,QAAQ,CAACwH,OAAO,CAACyB,OAAO,CAACC,QAAQ,KAAK,MAAM,EAAE;QAChD;MACF;;MAEA;MACA,IAAIvL,mBAAmB,CAAC4D,IAAI,KAAK,OAAO,EAAE;QACxCH,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;QAC7B;QACArB,QAAQ,CAACwH,OAAO,CAACyB,OAAO,CAACC,QAAQ,GAAG,MAAM;;QAE1C;QACA9H,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB;MACF;;MAEA;MACA,MAAM,CAAC,eAAe,CAAC,CAAC8H,IAAI,CAAEC,WAAW,IAAK;QAC5C,MAAMC,KAAK,GAAGD,WAAW,CAACE,OAAO;;QAEjC;QACA,MAAMC,MAAM,GAAGvJ,QAAQ,CAACwH,OAAO;QAC/B,IAAI,CAAC+B,MAAM,EAAE;;QAEb;QACA,IAAI;UACF,MAAMC,aAAa,GAAGH,KAAK,CAACI,QAAQ,CAACF,MAAM,CAAC;UAC5C,IAAIC,aAAa,EAAE;YACjBA,aAAa,CAACE,OAAO,CAAC,CAAC;UACzB;QACF,CAAC,CAAC,OAAOC,CAAC,EAAE;UACVvI,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC7C;;QAEA;QACA,MAAMuI,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;QACnC,IAAI,CAACD,GAAG,EAAE;QAEV,IAAI;UACF;UACAL,MAAM,CAACN,OAAO,CAACC,QAAQ,GAAG,MAAM;UAEhC,MAAMY,SAAS,GAAGnM,mBAAmB,CAAC4D,IAA0C;UAChF,MAAMqF,MAAM,GAAGmD,kBAAkB,CAACD,SAAS,EAAEnM,mBAAmB,CAACiJ,MAAM,EAAEnJ,UAAU,CAAC;UACpF,IAAI4L,KAAK,CAACO,GAAG,EAAEhD,MAAM,CAAC;;UAEtB;UACAxF,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;QACvB,CAAC,CAAC,OAAO9F,KAAK,EAAE;UACd6F,OAAO,CAAC7F,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/B;UACA6F,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;QACvB;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,OAAO,MAAM;MACX,IAAIrB,QAAQ,CAACwH,OAAO,EAAE;QACpB;QACAxH,QAAQ,CAACwH,OAAO,CAACyB,OAAO,CAACC,QAAQ,GAAG,OAAO;;QAE3C;QACA,MAAM,CAAC,eAAe,CAAC,CAACC,IAAI,CAAEC,WAAW,IAAK;UAC5C,MAAMC,KAAK,GAAGD,WAAW,CAACE,OAAO;UACjC,IAAI;YACF,MAAME,aAAa,GAAGH,KAAK,CAACI,QAAQ,CAACzJ,QAAQ,CAACwH,OAAQ,CAAC;YACvD,IAAIgC,aAAa,EAAE;cACjBA,aAAa,CAACE,OAAO,CAAC,CAAC;YACzB;UACF,CAAC,CAAC,OAAOC,CAAC,EAAE;YACVvI,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEsI,CAAC,CAAC;UAC5C;QACF,CAAC,CAAC,CAACK,KAAK,CAACtC,GAAG,IAAI;UACdtG,OAAO,CAAC7F,KAAK,CAAC,UAAU,EAAEmM,GAAG,CAAC;QAChC,CAAC,CAAC;MACJ;IACF,CAAC;EACH,CAAC,EAAE,CAAC/J,mBAAmB,EAAEF,UAAU,CAAC,CAAC;;EAErC;EACA,MAAMsM,kBAAkB,GAAGA,CACzBxI,IAAwC,EACxCqF,MAAW,EACX5J,IAAW,KACR;IACH;IACA,MAAMiN,MAAM,GAAGjN,IAAI,CAAC+E,GAAG,CAACmI,IAAI,IAAI;MAC9B;MACA,MAAMC,MAAM,GAAGvD,MAAM,CAACwD,KAAK,IAAInE,MAAM,CAACK,IAAI,CAAC4D,IAAI,CAAC,CAAC,CAAC,CAAC;MACnD,OAAOA,IAAI,CAACC,MAAM,CAAC;IACrB,CAAC,CAAC;;IAEF;IACA,MAAME,MAAM,GAAGzD,MAAM,CAAC0D,KAAK,IAAIrE,MAAM,CAACK,IAAI,CAACtJ,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD,MAAMuN,UAAU,GAAGvN,IAAI,CAAC+E,GAAG,CAACmI,IAAI,IAAIA,IAAI,CAACG,MAAM,CAAC,CAAC;;IAEjD;IACA,OAAO;MACL9I,IAAI;MAAE;MACNvE,IAAI,EAAE;QACJiN,MAAM,EAAEA,MAAM;QACdO,QAAQ,EAAE,CAAC;UACTC,KAAK,EAAE7D,MAAM,CAACxE,KAAK,IAAI,MAAM;UAC7BpF,IAAI,EAAEuN,UAAU;UAChBG,eAAe,EAAEnJ,IAAI,KAAK,KAAK,GAC7B,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,GAClE,yBAAyB;UAC3BoJ,WAAW,EAAE,uBAAuB;UACpCC,WAAW,EAAE;QACf,CAAC;MACH,CAAC;MACDC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BC,OAAO,EAAE;UACP5I,KAAK,EAAE;YACLmG,OAAO,EAAE,CAAC,CAAC3B,MAAM,CAACxE,KAAK;YACvB3H,IAAI,EAAEmM,MAAM,CAACxE,KAAK,IAAI;UACxB,CAAC;UACD6I,OAAO,EAAE;YACPC,OAAO,EAAE;UACX,CAAC;UACDC,MAAM,EAAE;YACN5C,OAAO,EAAEhH,IAAI,KAAK;UACpB;QACF;MACF;IACF,CAAC;EACH,CAAC;;EAED;EACA,MAAM6J,mBAAmB,GAAGtV,WAAW,CAAC,CAACoO,MAAc,EAAE1C,OAAe,EAAE6J,OAAiB,KAAK;IAC9F7O,gBAAgB,CAACyE,IAAI,IAAI;MACvB,MAAMqK,cAAc,GAAG;QAAE,GAAGrK;MAAK,CAAC;MAClC,MAAMsK,UAAU,GAAGD,cAAc,CAACpH,MAAM,CAAgC;MAExE,IAAI,CAACqH,UAAU,EAAE;QACfnK,OAAO,CAAC7F,KAAK,CAAC,SAAS2I,MAAM,EAAE,CAAC;QAChC,OAAOjD,IAAI;MACb;;MAEA;MACA,IAAIiD,MAAM,KAAK,UAAU,IAAIA,MAAM,KAAK,aAAa,EAAE;QACrD;QACA;QACA,IAAI1C,OAAO,IAAIA,OAAO,CAACa,MAAM,GAAG,GAAG,IAAIkJ,UAAU,CAAC7O,MAAM,KAAK8E,OAAO,EAAE;UACpEJ,OAAO,CAACC,GAAG,CAAC,UAAU6C,MAAM,OAAOA,MAAM,MAAM1C,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;UAC7EiJ,UAAU,CAAC1O,SAAS,GAAGwO,OAAO,KAAK,IAAI;UACvC,OAAOC,cAAc;QACvB;MACF,CAAC,MAAM;QACL;QACA,IAAI9J,OAAO,IAAI+J,UAAU,CAAC7O,MAAM,CAACqK,QAAQ,CAACvF,OAAO,CAAC,EAAE;UAClDJ,OAAO,CAACC,GAAG,CAAC,WAAW6C,MAAM,MAAM1C,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;UACjEiJ,UAAU,CAAC1O,SAAS,GAAGwO,OAAO,KAAK,IAAI;UACvC,OAAOC,cAAc;QACvB;MACF;;MAEA;MACAC,UAAU,CAAC3O,UAAU,GAAG,IAAI;;MAE5B;MACA2O,UAAU,CAAC1O,SAAS,GAAGwO,OAAO,KAAK,IAAI;;MAEvC;MACA,IAAInH,MAAM,KAAK,UAAU,IAAIA,MAAM,KAAK,aAAa,EAAE;QACrD;QACA;QACA9C,OAAO,CAACC,GAAG,CAAC,GAAG6C,MAAM,+BAA+B,CAAC;MACvD,CAAC,MAAM;QACL;QACA,IAAIqH,UAAU,CAAC7O,MAAM,KAAK,EAAE,IAAI6O,UAAU,CAAC7O,MAAM,CAACqK,QAAQ,CAAC,UAAU,CAAC,EAAE;UACtE;UACAwE,UAAU,CAAC7O,MAAM,GAAG8E,OAAO;QAC7B,CAAC,MAAM;UACL;UACA+J,UAAU,CAAC7O,MAAM,IAAI8E,OAAO;QAC9B;MACF;;MAEA;MACA,IAAI0C,MAAM,KAAK,UAAU,EAAE;QACzB9C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;UAC7B6C,MAAM;UACNsH,gBAAgB,EAAEhK,OAAO,CAACa,MAAM;UAChCoJ,WAAW,EAAEF,UAAU,CAAC7O,MAAM,CAAC2F,MAAM;UACrCqJ,iBAAiB,EAAElK,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;UAC3CqJ,mBAAmB,EAAEJ,UAAU,CAAC7O,MAAM,CAAC4F,SAAS,CAACiJ,UAAU,CAAC7O,MAAM,CAAC2F,MAAM,GAAG,EAAE,CAAC;UAC/EuJ,qBAAqB,EAAEpK,OAAO,CAACuF,QAAQ,CAAC,IAAI,CAAC;UAC7C8E,mBAAmB,EAAErK,OAAO,CAACuF,QAAQ,CAAC,GAAG,CAAC;UAC1C+E,aAAa,EAAEnM,IAAI,CAACkB,SAAS,CAACW,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;QACxD,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI4B,MAAM,KAAK,aAAa,EAAE;QACnC9C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;UAC7B6C,MAAM;UACNsH,gBAAgB,EAAEhK,OAAO,CAACa,MAAM;UAChCoJ,WAAW,EAAEF,UAAU,CAAC7O,MAAM,CAAC2F,MAAM;UACrCqJ,iBAAiB,EAAElK,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;UAC3CqJ,mBAAmB,EAAEJ,UAAU,CAAC7O,MAAM,CAAC4F,SAAS,CAAChC,IAAI,CAACyL,GAAG,CAAC,CAAC,EAAER,UAAU,CAAC7O,MAAM,CAAC2F,MAAM,GAAG,EAAE,CAAC,CAAC;UAC5F2J,WAAW,EAAET,UAAU,CAAC7O,MAAM,CAAC2F,MAAM,GAAG;QAC1C,CAAC,CAAC;MACJ,CAAC,MAAM;QACLjB,OAAO,CAACC,GAAG,CAAC,WAAW6C,MAAM,QAAQ1C,OAAO,CAACa,MAAM,SAASkJ,UAAU,CAAC7O,MAAM,CAAC2F,MAAM,EAAE,CAAC;MACzF;MAEA,OAAOiJ,cAAc;IACvB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,sBAAsB,GAAGnW,WAAW,CAAC,CAACoO,MAAc,EAAE9F,OAA8B,EAAE+F,MAAc,EAAE3C,OAAe,KAAK;IAC9H;IACA,IAAIA,OAAO,IAAI0C,MAAM,KAAK,SAAS,EAAE;MACnC,MAAMgI,IAAoB,GAAG;QAC3BxK,EAAE,EAAExB,mBAAmB,CAACsH,OAAO,EAAE;QACjCpJ,OAAO,EAAEoD,OAAO;QAChBG,SAAS,EAAE,IAAIvB,IAAI,CAAC,CAAC;QACrB+D,MAAM,EAAEA;MACV,CAAC;MACDzI,kBAAkB,CAACuF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEiL,IAAI,CAAC,CAAC;IAC7C;;IAEA;IACA,IAAK9N,OAAO,CAAC+F,MAAM,KAAK,YAAY,IAAI/F,OAAO,CAACoD,OAAO,IAClDpD,OAAO,CAACmD,IAAI,KAAK,kBAAkB,IAAInD,OAAO,CAACoD,OAAQ,IACvDpD,OAAO,CAAC8F,MAAM,KAAK,YAAY,IAAI9F,OAAO,CAACoD,OAAQ,EAAE;MACxDJ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE;QAC3B8C,MAAM,EAAE/F,OAAO,CAAC+F,MAAM;QACtB5C,IAAI,EAAEnD,OAAO,CAACmD,IAAI;QAClB2C,MAAM,EAAE9F,OAAO,CAAC8F,MAAM;QACtB1C,OAAO,EAAEpD,OAAO,CAACoD,OAAO;QACxBG,SAAS,EAAE,IAAIvB,IAAI,CAAC,CAAC,CAAC+L,WAAW,CAAC;MACpC,CAAC,CAAC;MACFjO,eAAe,CAAC;QACdC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,EAAE;QACXC,aAAa,EAAED,OAAO,CAACoD;MACzB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyG,YAAY,GAAGpS,MAAM,CAAC,IAAIkK,GAAG,CAAS,CAAC,CAAC;;EAE9C;EACA,MAAMgJ,aAAa,GAAGjT,WAAW,CAAEsI,OAA8B,IAAK;IACpE;IACA5C,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,IAAI0I,MAAM,GAAG9F,OAAO,CAAC8F,MAAM,IAAI,SAAS;IACxC,MAAMC,MAAM,GAAG/F,OAAO,CAAC+F,MAAM,IAAI,IAAI;IACrC,IAAI3C,OAAO,GAAGpD,OAAO,CAACoD,OAAO,IAAI,EAAE;;IAEnC;IACA,IAAI0C,MAAM,KAAK,UAAU,IAAIA,MAAM,KAAK,aAAa,EAAE;MACrD;MACA,IAAI,CAAC1C,OAAO,EAAE;QACZ;MACF;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACA,OAAO,CAACU,IAAI,CAAC,CAAC,EAAE;QACnB;MACF;IACF;;IAEA;IACA,IAAIL,SAAiB;IACrB,IAAIqC,MAAM,KAAK,UAAU,EAAE;MACzB;MACArC,SAAS,GAAG,GAAGqC,MAAM,IAAIC,MAAM,IAAI/D,IAAI,CAACC,GAAG,CAAC,CAAC,IAAImB,OAAO,CAACa,MAAM,IAAIb,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IAC/F,CAAC,MAAM,IAAI4B,MAAM,KAAK,aAAa,EAAE;MACnC;MACA,MAAMkI,WAAW,GAAG5K,OAAO,CAACa,MAAM,GAAGb,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGd,OAAO,CAACc,SAAS,CAACd,OAAO,CAACa,MAAM,GAAG,EAAE,CAAC;MACtGR,SAAS,GAAG,GAAGqC,MAAM,IAAIC,MAAM,IAAI/D,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI+L,WAAW,EAAE;IAChE,CAAC,MAAM,IAAIlI,MAAM,KAAK,YAAY,IAAI9F,OAAO,CAACmD,IAAI,KAAK,kBAAkB,EAAE;MACzE;MACAM,SAAS,GAAG,GAAGqC,MAAM,IAAIC,MAAM,IAAI/D,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,IAAIiB,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IAC9F,CAAC,MAAM;MACL;MACAT,SAAS,GAAG,GAAGqC,MAAM,IAAIC,MAAM,IAAI3C,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAChE;IAEA,IAAI2F,YAAY,CAACT,OAAO,CAACzC,GAAG,CAAClD,SAAS,CAAC,EAAE;MACvCT,OAAO,CAACC,GAAG,CAAC,WAAW6C,MAAM,MAAM1C,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;MACjE;IACF;IACA2F,YAAY,CAACT,OAAO,CAACrC,GAAG,CAACtD,SAAS,CAAC;;IAEnC;IACA,IAAIoG,YAAY,CAACT,OAAO,CAAC6E,IAAI,GAAG,IAAI,EAAE;MACpC,MAAMC,OAAO,GAAGC,KAAK,CAACC,IAAI,CAACvE,YAAY,CAACT,OAAO,CAAC;MAChDS,YAAY,CAACT,OAAO,CAACU,KAAK,CAAC,CAAC;MAC5BoE,OAAO,CAAC/E,KAAK,CAAC,CAAC,GAAG,CAAC,CAAChB,OAAO,CAACkG,KAAK,IAAIxE,YAAY,CAACT,OAAO,CAACrC,GAAG,CAACsH,KAAK,CAAC,CAAC;IACvE;;IAEA;IACA,IAAIvI,MAAM,KAAK,UAAU,EAAE;MACzB9C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;QAC7B6C,MAAM;QACNC,MAAM;QACNuI,aAAa,EAAElL,OAAO,CAACa,MAAM;QAC7BsK,cAAc,EAAEnL,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;QACzCsK,WAAW,EAAEpL,OAAO,CAACuF,QAAQ,CAAC,IAAI,CAAC;QACnC8F,SAAS,EAAErL,OAAO,CAACuF,QAAQ,CAAC,GAAG,CAAC;QAChC+F,UAAU,EAAEnN,IAAI,CAACkB,SAAS,CAACW,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;MACtD,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI4B,MAAM,KAAK,aAAa,EAAE;MACnC9C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;QAC7B6C,MAAM;QACNC,MAAM;QACNuI,aAAa,EAAElL,OAAO,CAACa,MAAM;QAC7BsK,cAAc,EAAEnL,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;QACzCsK,WAAW,EAAEpL,OAAO,CAACuF,QAAQ,CAAC,IAAI,CAAC;QACnC8F,SAAS,EAAErL,OAAO,CAACuF,QAAQ,CAAC,GAAG;MACjC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL3F,OAAO,CAACC,GAAG,CAAC,YAAY6C,MAAM,MAAMC,MAAM,MAAM3C,OAAO,CAACc,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;IAChF;;IAEA;IACA;IACA,IAAI4B,MAAM,KAAK,aAAa,EAAE;MAC5BkH,mBAAmB,CAAClH,MAAM,EAAE1C,OAAO,EAAEpD,OAAO,CAAC2O,QAAQ,CAAC;IACxD;;IAEA;IACA7H,UAAU,CAAC,MAAM;MACf;MACA,IAAIhB,MAAM,KAAK,UAAU,EAAE;QACzB;QACA1G,iBAAiB,CAACyD,IAAI,IAAI;UACxB,MAAM+L,WAAW,GAAG/L,IAAI,IAAI,EAAE;UAC9B;UACA,OAAO+L,WAAW,GAAGxL,OAAO;QAC9B,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI0C,MAAM,KAAK,aAAa,EAAE;QACnC;QACA5G,oBAAoB,CAAC2D,IAAI,IAAI;UAC3B,MAAM+L,WAAW,GAAG/L,IAAI,IAAI,EAAE;UAC9B;UACA,OAAO+L,WAAW,GAAGxL,OAAO;QAC9B,CAAC,CAAC;;QAEF;QACA1D,mBAAmB,CAAC;UAClBlB,UAAU,EAAE,IAAI;UAChBC,SAAS,EAAEuB,OAAO,CAAC2O,QAAQ,KAAK;QAClC,CAAC,CAAC;MACJ;;MAEA;MACA,IAAI3O,OAAO,CAAC2O,QAAQ,KAAK,IAAI,IAAI7I,MAAM,KAAK,eAAe,EAAE;QAC3D5I,UAAU,CAAC,KAAK,CAAC;QACjB8F,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACtC;;MAEA;MACA4K,sBAAsB,CAAC/H,MAAM,EAAE9F,OAAO,EAAE+F,MAAM,EAAE3C,OAAO,CAAC;IAC1D,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAAC4J,mBAAmB,EAAEa,sBAAsB,CAAC,CAAC;;EAQjD;EACArW,SAAS,CAAC,MAAM,CAEhB,CAAC,EAAE,CAACuH,SAAS,EAAEZ,aAAa,CAACO,GAAG,CAACF,UAAU,EAAEL,aAAa,CAACE,QAAQ,CAACI,SAAS,EAAEN,aAAa,CAACE,QAAQ,CAACG,UAAU,CAAC,CAAC;;EAElH;EACAhH,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAAC2G,aAAa,CAACE,QAAQ,CAACG,UAAU,IAAImB,iBAAiB,CAACtB,QAAQ,EAAE;MACpE;IACF;;IAEA;IACA,MAAMwQ,QAAQ,GAAG,IAAIC,gBAAgB,CAAEC,SAAS,IAAK;MACnD;MACA,IAAI5Q,aAAa,CAACE,QAAQ,CAACI,SAAS,EAAE;QACpCuQ,0BAA0B,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC;;IAEF;IACAlI,UAAU,CAAC,MAAM;MACf;MACA,MAAMiD,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,6BAA6B,CAAC;MAC/E,IAAIF,iBAAiB,EAAE;QACrB;QACA8E,QAAQ,CAACI,OAAO,CAAClF,iBAAiB,EAAE;UAClCmF,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE,IAAI;UACbC,aAAa,EAAE;QACjB,CAAC,CAAC;;QAEF;QACA,IAAIjR,aAAa,CAACE,QAAQ,CAACI,SAAS,EAAE;UACpCuQ,0BAA0B,CAAC,CAAC;QAC9B;MACF;IACF,CAAC,EAAE,GAAG,CAAC;;IAEP;IACA,OAAO,MAAM;MACXH,QAAQ,CAACQ,UAAU,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAAClR,aAAa,CAACE,QAAQ,CAACG,UAAU,EAAEL,aAAa,CAACE,QAAQ,CAACC,MAAM,EAAEH,aAAa,CAACE,QAAQ,CAACI,SAAS,EAAEkB,iBAAiB,CAACtB,QAAQ,CAAC,CAAC;;EAEpI;EACA,MAAMiR,aAAa,GAAI/D,CAAwC,IAAK;IAClE,IAAIA,CAAC,CAACnD,GAAG,KAAK,OAAO,IAAI,CAACnL,OAAO,IAAIF,KAAK,CAAC+G,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACxDyH,CAAC,CAACgE,cAAc,CAAC,CAAC;MAClBhG,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMiG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACzS,KAAK,CAAC+G,IAAI,CAAC,CAAC,IAAI,CAACrG,oBAAoB,EAAE;MAC1C;IACF;IAEA0C,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAMsP,mBAAmB,GAAIC,OAAsB,IAAK;IACtD;IACA1S,QAAQ,CAAC0S,OAAO,CAACC,OAAO,CAACC,QAAQ,CAAC;IAClCzP,wBAAwB,CAAC,KAAK,CAAC;;IAE/B;IACA;EACF,CAAC;EAED,MAAM0P,mBAAmB,GAAGA,CAAA,KAAM;IAChC1P,wBAAwB,CAAC,KAAK,CAAC;EACjC,CAAC;;EAED;EACA,MAAM2P,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACjQ,YAAY,CAACG,OAAO,CAAC8D,IAAI,CAAC,CAAC,EAAE;IAElC,IAAI;MACFd,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEpD,YAAY,CAACG,OAAO,CAAC;;MAE5C;MACA,MAAM+P,eAAe,GAAGlQ,YAAY,CAACG,OAAO;;MAE5C;MACA5B,gBAAgB,CAACyE,IAAI,IAAI;QACvB,MAAMqK,cAAc,GAAG;UAAE,GAAGrK;QAAK,CAAC;QAClC,MAAMmN,cAAc,GAAG9C,cAAc,CAAC7O,QAAQ;;QAE9C;QACA,MAAM4R,SAAS,GAAG,6CAA6C,GAAGF,eAAe,GAAG,oCAAoC;;QAExH;QACA,IAAI,CAACC,cAAc,CAAC1R,MAAM,CAACqK,QAAQ,CAAC,QAAQoH,eAAe,EAAE,CAAC,EAAE;UAC9DC,cAAc,CAAC1R,MAAM,IAAI2R,SAAS;QACpC,CAAC,MAAM;UACLjN,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;QAC7B;QAEA,OAAOiK,cAAc;MACvB,CAAC,CAAC;;MAEF;MACA,MAAM;QAAEgD;MAAiB,CAAC,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC;MAClD,MAAMC,WAAW,GAAGvY,oBAAoB,CAAC,CAAC;MAC1C,MAAM+K,SAAS,GAAGwN,WAAW,CAACC,mBAAmB,CAAC,CAAC;MAEnDpN,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;QAC1BN,SAAS;QACT0N,QAAQ,EAAEN,eAAe;QACzBO,UAAU,EAAE,CAAC,CAAC3N,SAAS;QACvBY,SAAS,EAAE,IAAIvB,IAAI,CAAC,CAAC,CAAC+L,WAAW,CAAC;MACpC,CAAC,CAAC;MAEF,IAAIpL,SAAS,EAAE;QACb,MAAMuN,gBAAgB,CAACvN,SAAS,EAAEoN,eAAe,EAAG5S,KAAK,IAAK;UAC5D6F,OAAO,CAAC7F,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/BC,QAAQ,CAAC,WAAWD,KAAK,CAAC6C,OAAO,EAAE,CAAC;QACtC,CAAC,CAAC;QACFgD,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACzB,CAAC,MAAM;QACLD,OAAO,CAAC7F,KAAK,CAAC,cAAc,CAAC;QAC7BC,QAAQ,CAAC,gBAAgB,CAAC;MAC5B;;MAEA;MACA4F,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/BnD,eAAe,CAAC;QACdC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,EAAE;QACXC,aAAa,EAAE;MACjB,CAAC,CAAC;;MAEF;MACA6G,UAAU,CAAC,MAAM;QACfkI,0BAA0B,CAAC,CAAC;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAO1F,GAAG,EAAE;MACZtG,OAAO,CAAC7F,KAAK,CAAC,WAAW,EAAEmM,GAAG,CAAC;MAC/BlM,QAAQ,CAAC,WAAWkM,GAAG,EAAE,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMiH,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI;MACFvN,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACrB,MAAMuN,EAAE,GAAG5Y,oBAAoB,CAAC,CAAC;MACjC4Y,EAAE,CAACC,WAAW,CAAC,MAAM,CAAC;;MAEtB;MACA3Q,eAAe,CAAC;QACdC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,EAAE;QACXC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOqJ,GAAG,EAAE;MACZtG,OAAO,CAAC7F,KAAK,CAAC,WAAW,EAAEmM,GAAG,CAAC;MAC/BlM,QAAQ,CAAC,WAAWkM,GAAG,EAAE,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMoH,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF1N,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;;MAEhC;MACA7E,gBAAgB,CAACyE,IAAI,IAAI;QACvB,MAAMqK,cAAc,GAAG;UAAE,GAAGrK;QAAK,CAAC;QAClC,MAAMmN,cAAc,GAAG9C,cAAc,CAAC7O,QAAQ;;QAE9C;QACA,IAAI,CAAC2R,cAAc,CAAC1R,MAAM,CAACqK,QAAQ,CAAC,SAAS,CAAC,IAC1C,CAACqH,cAAc,CAAC1R,MAAM,CAACqK,QAAQ,CAAC,8BAA8B,CAAC,EAAE;UACnE,MAAMsH,SAAS,GAAG,iFAAiF;UACnGD,cAAc,CAAC1R,MAAM,IAAI2R,SAAS;QACpC;QAEA,OAAO/C,cAAc;MACvB,CAAC,CAAC;;MAEF;MACA,MAAM;QAAEyD;MAAiB,CAAC,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC;MAClD,MAAMR,WAAW,GAAGvY,oBAAoB,CAAC,CAAC;MAC1C,MAAM+K,SAAS,GAAGwN,WAAW,CAACC,mBAAmB,CAAC,CAAC;MAEnD,IAAIzN,SAAS,EAAE;QACb,MAAMgO,gBAAgB,CAAChO,SAAS,EAAGxF,KAAK,IAAK;UAC3C6F,OAAO,CAAC7F,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;UACjCC,QAAQ,CAAC,aAAaD,KAAK,CAAC6C,OAAO,EAAE,CAAC;QACxC,CAAC,CAAC;MACJ,CAAC,MAAM;QACLgD,OAAO,CAAC7F,KAAK,CAAC,YAAY,CAAC;QAC3BC,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;;MAEA;MACA0C,eAAe,CAAC;QACdC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,EAAE;QACXC,aAAa,EAAE;MACjB,CAAC,CAAC;;MAEF;MACA6G,UAAU,CAAC,MAAM;QACfkI,0BAA0B,CAAC,CAAC;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAO1F,GAAG,EAAE;MACZtG,OAAO,CAAC7F,KAAK,CAAC,WAAW,EAAEmM,GAAG,CAAC;MAC/BlM,QAAQ,CAAC,aAAakM,GAAG,EAAE,CAAC;IAC9B;EACF,CAAC;;EAID;EACA,MAAM0F,0BAA0B,GAAGA,CAAA,KAAM;IACvC;IACA,IAAI,CAAC7Q,aAAa,CAACE,QAAQ,CAACI,SAAS,EAAE;MACrC,OAAO,CAAC;IACV;;IAEA;IACA,MAAMsL,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,6BAA6B,CAAC;IAC/E,IAAIF,iBAAiB,IAAIA,iBAAiB,YAAY6G,WAAW,EAAE;MACjE;MACA;MACA,MAAMC,cAAc,GAAG9G,iBAAiB,CAAC+G,SAAS;MAClD,MAAMC,YAAY,GAAGhH,iBAAiB,CAACgH,YAAY;MACnD,MAAMC,YAAY,GAAGjH,iBAAiB,CAACiH,YAAY;;MAEnD;MACA,IAAID,YAAY,GAAGF,cAAc,GAAGG,YAAY,GAAG,GAAG,EAAE;QACtDhO,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;QAC/B;MACF;;MAEA;MACA8G,iBAAiB,CAAC+G,SAAS,GAAG/G,iBAAiB,CAACgH,YAAY;;MAE5D;MACA,MAAME,eAAe,GAAGjH,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;MACnE,IAAIgH,eAAe,IAAIA,eAAe,YAAYL,WAAW,EAAE;QAC7D;QACAK,eAAe,CAAC/G,KAAK,CAACC,OAAO,GAAG,OAAO;QACvC8G,eAAe,CAAC/G,KAAK,CAACgH,QAAQ,GAAG,MAAM;QACvCD,eAAe,CAAC/G,KAAK,CAACE,SAAS,GAAG,OAAO;;QAEzC;QACAtD,UAAU,CAAC,MAAM;UACf;UACA,IAAI3I,aAAa,CAACE,QAAQ,CAACI,SAAS,EAAE;YACpCwS,eAAe,CAACH,SAAS,GAAGG,eAAe,CAACF,YAAY;UAC1D;QACF,CAAC,EAAE,GAAG,CAAC;MACT;IACF;EACF,CAAC;;EAID;EACA,MAAMI,iBAAiB,GAAGzZ,WAAW,CAAC,CAAC0L,OAAe,EAAEgO,QAAgB,KAAK;IAC3E;IACApO,OAAO,CAACC,GAAG,CAAC,GAAGmO,QAAQ,YAAY,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAMN,oBACEtY,OAAA;IAAK6D,SAAS,EAAE,sBAAsBuE,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,EAAG;IAAAxH,QAAA,gBAElFZ,OAAA;MAAK6D,SAAS,EAAC,gBAAgB;MAAAjD,QAAA,eAC7BZ,OAAA,CAACT,kBAAkB;QACjB2M,SAAS,EAAEtE,aAAc;QACzBE,iBAAiB,EAAEA,iBAAkB;QACrCyQ,eAAe,EAAElM,mBAAoB;QACrCmM,eAAe,EAAEpL,mBAAoB;QACrCqL,SAAS,EAAE7O,oBAAqB;QAChC8O,SAAS,EAAEtQ,gBAAiB;QAC5BuQ,gBAAgB,EAAEnP;MAAoB;QAAA1I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNjB,OAAA;MAAK6D,SAAS,EAAC,kBAAkB;MAAAjD,QAAA,gBAE/BZ,OAAA;QAAK6D,SAAS,EAAC,qBAAqB;QAAAjD,QAAA,GAEjC6D,WAAW,CAAC0G,MAAM,GAAG,CAAC,iBACrBnL,OAAA;UAAKoR,KAAK,EAAE;YACVwH,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,MAAM;YACXC,IAAI,EAAE,MAAM;YACZC,MAAM,EAAE;UACV,CAAE;UAAAnY,QAAA,eACAZ,OAAA,CAACX,kBAAkB;YACjBoF,WAAW,EAAEA,WAAY;YACzBE,oBAAoB,EAAEA,oBAAqB;YAC3CC,uBAAuB,EAAEA,uBAAwB;YACjDC,kBAAkB,EAAEA;UAAmB;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGDjB,OAAA;UAAKoR,KAAK,EAAE;YACV4H,IAAI,EAAE,CAAC;YACPZ,QAAQ,EAAE,MAAM;YAChB9G,SAAS,EAAE,CAAC;YACZ2H,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,MAAM,CAAC;UACrB,CAAE;UAAAtY,QAAA,gBAEAZ,OAAA,CAACZ,YAAY;YAACiF,KAAK,EAAEA;UAAM;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG9BjB,OAAA;YAAKoR,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAE8H,aAAa,EAAE,QAAQ;cAAEC,GAAG,EAAE;YAAO,CAAE;YAAAxY,QAAA,GAEnE,CAACyE,aAAa,CAACE,QAAQ,CAACG,UAAU,IAAIL,aAAa,CAACE,QAAQ,CAACI,SAAS,kBACrE3F,OAAA,CAACR,WAAW;cACV0L,KAAK,EAAC,0BAAM;cACZZ,OAAO,EAAEjE,cAAc,IAAIhB,aAAa,CAACE,QAAQ,CAACC,MAAO;cACzD6T,WAAW,EAAEhU,aAAa,CAACE,QAAQ,CAACI,SAAU;cAC9CD,UAAU,EAAEL,aAAa,CAACE,QAAQ,CAACG,UAAW;cAC9CsH,MAAM,EAAC,UAAU;cACjBsM,aAAa,EAAEjB;YAAkB;cAAAvX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CACF,EAGA,CAACoE,aAAa,CAACO,GAAG,CAACF,UAAU,IAAIL,aAAa,CAACO,GAAG,CAACD,SAAS,kBAC3D3F,OAAA,CAACR,WAAW;cACV0L,KAAK,EAAC,iBAAO;cACbZ,OAAO,EAAErE,SAAS,IAAIZ,aAAa,CAACO,GAAG,CAACJ,MAAO;cAC/C6T,WAAW,EAAEhU,aAAa,CAACO,GAAG,CAACD,SAAU;cACzCD,UAAU,EAAEL,aAAa,CAACO,GAAG,CAACF,UAAW;cACzCsH,MAAM,EAAC,KAAK;cACZsM,aAAa,EAAEjB;YAAkB;cAAAvX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CACF,EAGA,CAAC0F,gBAAgB,CAACjB,UAAU,IAAIiB,gBAAgB,CAAChB,SAAS,kBACzD3F,OAAA,CAACR,WAAW;cACV0L,KAAK,EAAC,0BAAM;cACZZ,OAAO,EAAEnE,iBAAiB,IAAI,EAAG;cACjCkT,WAAW,EAAE1S,gBAAgB,CAAChB,SAAU;cACxCD,UAAU,EAAEiB,gBAAgB,CAACjB,UAAW;cACxCsH,MAAM,EAAC,aAAa;cACpBsM,aAAa,EAAEjB;YAAkB;cAAAvX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CACF,EAGA,CAACoE,aAAa,CAACS,IAAI,CAACJ,UAAU,IAAIL,aAAa,CAACS,IAAI,CAACH,SAAS,kBAC7D3F,OAAA,CAACR,WAAW;cACV0L,KAAK,EAAC,0BAAM;cACZZ,OAAO,EAAE/D,UAAU,GAAGkC,IAAI,CAACkB,SAAS,CAACpD,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,GAAGlB,aAAa,CAACS,IAAI,CAACN,MAAO;cACtF6T,WAAW,EAAEhU,aAAa,CAACS,IAAI,CAACH,SAAU;cAC1CD,UAAU,EAAEL,aAAa,CAACS,IAAI,CAACJ,UAAW;cAC1CsH,MAAM,EAAC,MAAM;cACbsM,aAAa,EAAEjB,iBAAkB;cACjC9R,UAAU,EAAEA,UAAW;cACvBtB,WAAW,EAAEA,WAAY;cACzBE,QAAQ,EAAEA,QAAS;cACnB2K,gBAAgB,EAAEA,gBAAiB;cACnCE,aAAa,EAAEA,aAAc;cAC7BE,kBAAkB,EAAEA,kBAAmB;cACvCzQ,YAAY,EAAEC;YAAa;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACF,EAGA,CAACoE,aAAa,CAACU,aAAa,CAACL,UAAU,IAAIL,aAAa,CAACU,aAAa,CAACJ,SAAS,kBAC/E3F,OAAA,CAACR,WAAW;cACV0L,KAAK,EAAC,gCAAO;cACbZ,OAAO,EAAE7D,mBAAmB,GAAGgC,IAAI,CAACkB,SAAS,CAAClD,mBAAmB,EAAE,IAAI,EAAE,CAAC,CAAC,GAAGpB,aAAa,CAACU,aAAa,CAACP,MAAO;cACjH6T,WAAW,EAAEhU,aAAa,CAACU,aAAa,CAACJ,SAAU;cACnDD,UAAU,EAAEL,aAAa,CAACU,aAAa,CAACL,UAAW;cACnDsH,MAAM,EAAC,eAAe;cACtBsM,aAAa,EAAEjB,iBAAkB;cACjC5R,mBAAmB,EAAEA,mBAAoB;cACzCF,UAAU,EAAEA;YAAW;cAAAzF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNjB,OAAA,CAACb,YAAY;YACX8H,OAAO,EAAEF,YAAY,CAACE,OAAQ;YAC9BC,OAAO,EAAEH,YAAY,CAACG,OAAQ;YAC9BC,aAAa,EAAEJ,YAAY,CAACI,aAAc;YAC1CoS,UAAU,EAAGrS,OAAO,IAAKF,eAAe,CAAC+C,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE7C;YAAQ,CAAC,CAAC,CAAE;YACzEsS,YAAY,EAAExC,oBAAqB;YACnCyC,aAAa,EAAE7B,qBAAsB;YACrC8B,YAAY,EAAEjC;UAAqB;YAAA3W,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjB,OAAA;QAAK6D,SAAS,EAAC,mBAAmB;QAAAjD,QAAA,eAChCZ,OAAA;UAAK6D,SAAS,EAAC,wBAAwB;UAAAjD,QAAA,gBACrCZ,OAAA;YAAK6D,SAAS,EAAC,kBAAkB;YAAAjD,QAAA,gBAE/BZ,OAAA;cAAK6D,SAAS,EAAC,eAAe;cAAAjD,QAAA,eAC5BZ,OAAA,CAACL,OAAO;gBAACuL,KAAK,EAAE1D,sBAAsB,GAAG,SAAS,GAAG,SAAU;gBAAA5G,QAAA,eAC7DZ,OAAA;kBACE6D,SAAS,EAAE,kBAAkB2D,sBAAsB,GAAG,QAAQ,GAAG,EAAE,EAAG;kBACtEmS,OAAO,EAAEA,CAAA,KAAMlS,yBAAyB,CAAC,CAACD,sBAAsB,CAAE;kBAClEoS,QAAQ,EAAEzV,OAAQ;kBAAAvD,QAAA,eAElBZ,OAAA,CAACJ,YAAY;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAGNjB,OAAA;cAAK6D,SAAS,EAAC,eAAe;cAAAjD,QAAA,gBAC5BZ,OAAA;gBACEqK,IAAI,EAAC,MAAM;gBACXwP,KAAK,EAAE5V,KAAM;gBACb6V,QAAQ,EAAGrH,CAAC,IAAKvO,QAAQ,CAACuO,CAAC,CAACsH,MAAM,CAACF,KAAK,CAAE;gBAC1CG,SAAS,EAAExD,aAAc;gBACzByD,WAAW,EAAC,4CAAS;gBACrBpW,SAAS,EAAC,cAAc;gBACxB+V,QAAQ,EAAEzV;cAAQ;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eAGFjB,OAAA;gBAAK6D,SAAS,EAAC,yBAAyB;gBAAAjD,QAAA,eACtCZ,OAAA,CAACL,OAAO;kBAACuL,KAAK,EAAC,kDAAU;kBAAAtK,QAAA,eACvBZ,OAAA;oBAAO6D,SAAS,EAAC,gCAAgC;oBAAAjD,QAAA,eAC/CZ,OAAA;sBACEqK,IAAI,EAAC,UAAU;sBACf6P,OAAO,EAAEnV,mBAAoB;sBAC7B+U,QAAQ,EAAGrH,CAAC,IAAKzN,sBAAsB,CAACyN,CAAC,CAACsH,MAAM,CAACG,OAAO,CAAE;sBAC1DN,QAAQ,EAAEzV,OAAQ;sBAClBN,SAAS,EAAC;oBAAgC;sBAAA/C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNjB,OAAA;cAAK6D,SAAS,EAAC,gBAAgB;cAAAjD,QAAA,GAE5B4G,sBAAsB,iBACrBxH,OAAA,CAACL,OAAO;gBAACuL,KAAK,EAAC,kDAAU;gBAAAtK,QAAA,eACvBZ,OAAA;kBACE2Z,OAAO,EAAEjD,kBAAmB;kBAC5BkD,QAAQ,EAAEzV,OAAO,IAAIF,KAAK,CAAC+G,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAACrG,oBAAqB;kBAClEd,SAAS,EAAC,gBAAgB;kBAAAjD,QAAA,eAE1BZ,OAAA;oBAAKI,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAAAG,QAAA,gBAC/FZ,OAAA;sBAAMa,CAAC,EAAC;oBAAyC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACnDjB,OAAA;sBAAMa,CAAC,EAAC;oBAA2C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACrDjB,OAAA;sBAAMa,CAAC,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACpBjB,OAAA;sBAAQoB,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,GAAG;sBAACK,CAAC,EAAC;oBAAG;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACV,eAGDjB,OAAA,CAACL,OAAO;gBAACuL,KAAK,EAAC,0BAAM;gBAAAtK,QAAA,eACnBZ,OAAA;kBACE2Z,OAAO,EAAElJ,kBAAmB;kBAC5BmJ,QAAQ,EAAEzV,OAAO,IAAIF,KAAK,CAAC+G,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAACrG,oBAAqB;kBAClEd,SAAS,EAAC,aAAa;kBAAAjD,QAAA,EAEtBuD,OAAO,gBACNnE,OAAA;oBAAK6D,SAAS,EAAC,cAAc;oBAACzD,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAAAM,QAAA,gBACtEZ,OAAA;sBAAQ6D,SAAS,EAAC,YAAY;sBAACzC,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACK,CAAC,EAAC,IAAI;sBAAClB,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACF,IAAI,EAAC;oBAAM;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eACjHjB,OAAA;sBAAM6D,SAAS,EAAC,YAAY;sBAACtD,IAAI,EAAC,cAAc;sBAACM,CAAC,EAAC;oBAAiH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzK,CAAC,gBAENjB,OAAA,CAACH,YAAY;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAChB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELoD,KAAK,iBACJrE,OAAA;YAAKoR,KAAK,EAAE;cACV+I,SAAS,EAAE,KAAK;cAChBC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,MAAM;cAChBC,SAAS,EAAE;YACb,CAAE;YAAA1Z,QAAA,EACCyD;UAAK;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjB,OAAA,CAACV,mBAAmB;MAClB2E,KAAK,EAAEA,KAAM;MACbqH,YAAY,EAAE3G,oBAAqB;MACnC+C,aAAa,EAAEA,aAAc;MAC7BT,OAAO,EAAEG,qBAAsB;MAC/BmT,eAAe,EAAE5D,mBAAoB;MACrC6D,OAAO,EAAEzD;IAAoB;MAAAjW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAAC+C,GAAA,CAl6DuBD,QAAQ;AAAA0W,GAAA,GAAR1W,QAAQ;AAAA,IAAA7C,EAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAkB,GAAA,EAAA2W,GAAA;AAAAC,YAAA,CAAAxZ,EAAA;AAAAwZ,YAAA,CAAAlZ,GAAA;AAAAkZ,YAAA,CAAA/Y,GAAA;AAAA+Y,YAAA,CAAAzY,GAAA;AAAAyY,YAAA,CAAAtY,GAAA;AAAAsY,YAAA,CAAApY,GAAA;AAAAoY,YAAA,CAAAlY,GAAA;AAAAkY,YAAA,CAAAhY,GAAA;AAAAgY,YAAA,CAAA9X,GAAA;AAAA8X,YAAA,CAAA5W,GAAA;AAAA4W,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}