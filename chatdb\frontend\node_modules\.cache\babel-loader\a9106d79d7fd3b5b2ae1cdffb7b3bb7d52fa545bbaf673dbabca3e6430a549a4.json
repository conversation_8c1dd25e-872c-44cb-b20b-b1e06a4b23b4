{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\pages\\\\text2sql\\\\components\\\\ConnectionSelector.tsx\";\nimport React from 'react';\nimport '../../../styles/ConnectionSelector.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n/**\n * 数据库连接选择器组件 - 用于页面右上角\n */\nconst ConnectionSelector = ({\n  connections,\n  selectedConnectionId,\n  setSelectedConnectionId,\n  loadingConnections,\n  disabled = false\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text2sql-connection-selector\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text2sql-db-select-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text2sql-db-select-icon\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          children: [/*#__PURE__*/_jsxDEV(\"ellipse\", {\n            cx: \"12\",\n            cy: \"5\",\n            rx: \"9\",\n            ry: \"3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M21 12c0 1.66-4 3-9 3s-9-1.34-9-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: selectedConnectionId || '',\n        onChange: e => setSelectedConnectionId(e.target.value ? Number(e.target.value) : null),\n        className: \"text2sql-db-select\",\n        disabled: disabled || loadingConnections,\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"\",\n          children: \"\\u8BF7\\u9009\\u62E9\\u6570\\u636E\\u5E93\\u8FDE\\u63A5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), connections.map(conn => /*#__PURE__*/_jsxDEV(\"option\", {\n          value: conn.id,\n          children: [conn.name, \" (\", conn.type, \" - \", conn.database, \")\"]\n        }, conn.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text2sql-db-select-arrow\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          width: \"12\",\n          height: \"12\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          children: /*#__PURE__*/_jsxDEV(\"polyline\", {\n            points: \"6 9 12 15 18 9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), loadingConnections && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-500 absolute -bottom-5 left-0\",\n        children: \"\\u52A0\\u8F7D\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 32\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_c = ConnectionSelector;\nexport default ConnectionSelector;\nvar _c;\n$RefreshReg$(_c, \"ConnectionSelector\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ConnectionSelector", "connections", "selectedConnectionId", "setSelectedConnectionId", "loadingConnections", "disabled", "className", "children", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "cx", "cy", "rx", "ry", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "value", "onChange", "e", "target", "Number", "map", "conn", "id", "name", "type", "database", "points", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/pages/text2sql/components/ConnectionSelector.tsx"], "sourcesContent": ["import React from 'react';\nimport '../../../styles/ConnectionSelector.css';\n\ninterface Connection {\n  id: number;\n  name: string;\n  type: string;\n  host: string;\n  port: number;\n  username: string;\n  database: string;\n  created_at: string;\n  updated_at: string;\n}\n\ninterface ConnectionSelectorProps {\n  connections: Connection[];\n  selectedConnectionId: number | null;\n  setSelectedConnectionId: (id: number | null) => void;\n  loadingConnections: boolean;\n  disabled?: boolean;\n}\n\n/**\n * 数据库连接选择器组件 - 用于页面右上角\n */\nconst ConnectionSelector: React.FC<ConnectionSelectorProps> = ({\n  connections,\n  selectedConnectionId,\n  setSelectedConnectionId,\n  loadingConnections,\n  disabled = false\n}) => {\n  return (\n    <div className=\"text2sql-connection-selector\">\n      <div className=\"text2sql-db-select-wrapper\">\n        <div className=\"text2sql-db-select-icon\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n            <ellipse cx=\"12\" cy=\"5\" rx=\"9\" ry=\"3\"></ellipse>\n            <path d=\"M21 12c0 1.66-4 3-9 3s-9-1.34-9-3\"></path>\n            <path d=\"M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5\"></path>\n          </svg>\n        </div>\n        <select\n          value={selectedConnectionId || ''}\n          onChange={(e) => setSelectedConnectionId(e.target.value ? Number(e.target.value) : null)}\n          className=\"text2sql-db-select\"\n          disabled={disabled || loadingConnections}\n        >\n          <option value=\"\">请选择数据库连接</option>\n          {connections.map(conn => (\n            <option key={conn.id} value={conn.id}>{conn.name} ({conn.type} - {conn.database})</option>\n          ))}\n        </select>\n        <div className=\"text2sql-db-select-arrow\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n            <polyline points=\"6 9 12 15 18 9\"></polyline>\n          </svg>\n        </div>\n        {loadingConnections && <div className=\"text-xs text-gray-500 absolute -bottom-5 left-0\">加载中...</div>}\n      </div>\n\n\n    </div>\n  );\n};\n\nexport default ConnectionSelector;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsBhD;AACA;AACA;AACA,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,WAAW;EACXC,oBAAoB;EACpBC,uBAAuB;EACvBC,kBAAkB;EAClBC,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,oBACEN,OAAA;IAAKO,SAAS,EAAC,8BAA8B;IAAAC,QAAA,eAC3CR,OAAA;MAAKO,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzCR,OAAA;QAAKO,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtCR,OAAA;UAAKS,KAAK,EAAC,4BAA4B;UAACC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAACC,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAAAT,QAAA,gBAC/KR,OAAA;YAASkB,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAChDzB,OAAA;YAAM0B,CAAC,EAAC;UAAmC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnDzB,OAAA;YAAM0B,CAAC,EAAC;UAAqC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNzB,OAAA;QACE2B,KAAK,EAAExB,oBAAoB,IAAI,EAAG;QAClCyB,QAAQ,EAAGC,CAAC,IAAKzB,uBAAuB,CAACyB,CAAC,CAACC,MAAM,CAACH,KAAK,GAAGI,MAAM,CAACF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,GAAG,IAAI,CAAE;QACzFpB,SAAS,EAAC,oBAAoB;QAC9BD,QAAQ,EAAEA,QAAQ,IAAID,kBAAmB;QAAAG,QAAA,gBAEzCR,OAAA;UAAQ2B,KAAK,EAAC,EAAE;UAAAnB,QAAA,EAAC;QAAQ;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACjCvB,WAAW,CAAC8B,GAAG,CAACC,IAAI,iBACnBjC,OAAA;UAAsB2B,KAAK,EAAEM,IAAI,CAACC,EAAG;UAAA1B,QAAA,GAAEyB,IAAI,CAACE,IAAI,EAAC,IAAE,EAACF,IAAI,CAACG,IAAI,EAAC,KAAG,EAACH,IAAI,CAACI,QAAQ,EAAC,GAAC;QAAA,GAApEJ,IAAI,CAACC,EAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAqE,CAC1F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACTzB,OAAA;QAAKO,SAAS,EAAC,0BAA0B;QAAAC,QAAA,eACvCR,OAAA;UAAKS,KAAK,EAAC,4BAA4B;UAACC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAACC,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAAAT,QAAA,eAC/KR,OAAA;YAAUsC,MAAM,EAAC;UAAgB;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACLpB,kBAAkB,iBAAIL,OAAA;QAAKO,SAAS,EAAC,iDAAiD;QAAAC,QAAA,EAAC;MAAM;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV,CAAC;AAACc,EAAA,GAvCItC,kBAAqD;AAyC3D,eAAeA,kBAAkB;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}