{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\components\\\\EnhancedButton.tsx\";\nimport React from 'react';\nimport { Button, Tooltip } from 'antd';\nimport { LoadingOutlined } from '@ant-design/icons';\nimport '../styles/EnhancedButton.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EnhancedButton = ({\n  variant = 'primary',\n  size = 'medium',\n  tooltip,\n  loading = false,\n  loadingText,\n  ripple = true,\n  elevated = false,\n  fullWidth = false,\n  children,\n  className = '',\n  onClick,\n  ...props\n}) => {\n  const handleClick = e => {\n    if (ripple && !loading && !props.disabled) {\n      createRipple(e);\n    }\n    if (onClick) {\n      onClick(e);\n    }\n  };\n  const createRipple = event => {\n    const button = event.currentTarget;\n    const rect = button.getBoundingClientRect();\n    const size = Math.max(rect.width, rect.height);\n    const x = event.clientX - rect.left - size / 2;\n    const y = event.clientY - rect.top - size / 2;\n    const ripple = document.createElement('span');\n    ripple.className = 'button-ripple';\n    ripple.style.width = ripple.style.height = size + 'px';\n    ripple.style.left = x + 'px';\n    ripple.style.top = y + 'px';\n    button.appendChild(ripple);\n    setTimeout(() => {\n      ripple.remove();\n    }, 600);\n  };\n  const getButtonClass = () => {\n    const classes = ['enhanced-button'];\n    classes.push(`enhanced-button--${variant}`);\n    classes.push(`enhanced-button--${size}`);\n    if (elevated) classes.push('enhanced-button--elevated');\n    if (fullWidth) classes.push('enhanced-button--full-width');\n    if (loading) classes.push('enhanced-button--loading');\n    if (ripple) classes.push('enhanced-button--ripple');\n    return [...classes, className].join(' ');\n  };\n  const buttonContent = /*#__PURE__*/_jsxDEV(Button, {\n    ...props,\n    className: getButtonClass(),\n    onClick: handleClick,\n    disabled: props.disabled || loading,\n    children: [loading && /*#__PURE__*/_jsxDEV(LoadingOutlined, {\n      className: \"enhanced-button__loading-icon\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"enhanced-button__content\",\n      children: loading && loadingText ? loadingText : children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n  if (tooltip) {\n    return /*#__PURE__*/_jsxDEV(Tooltip, {\n      title: tooltip,\n      placement: \"top\",\n      children: buttonContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this);\n  }\n  return buttonContent;\n};\n_c = EnhancedButton;\nexport default EnhancedButton;\nvar _c;\n$RefreshReg$(_c, \"EnhancedButton\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "LoadingOutlined", "jsxDEV", "_jsxDEV", "Enhanced<PERSON><PERSON><PERSON>", "variant", "size", "tooltip", "loading", "loadingText", "ripple", "elevated", "fullWidth", "children", "className", "onClick", "props", "handleClick", "e", "disabled", "createRipple", "event", "button", "currentTarget", "rect", "getBoundingClientRect", "Math", "max", "width", "height", "x", "clientX", "left", "y", "clientY", "top", "document", "createElement", "style", "append<PERSON><PERSON><PERSON>", "setTimeout", "remove", "getButtonClass", "classes", "push", "join", "buttonContent", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "placement", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/components/EnhancedButton.tsx"], "sourcesContent": ["import React from 'react';\nimport { Button, ButtonProps, Tooltip } from 'antd';\nimport { LoadingOutlined } from '@ant-design/icons';\nimport '../styles/EnhancedButton.css';\n\ninterface EnhancedButtonProps extends ButtonProps {\n  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost';\n  size?: 'small' | 'medium' | 'large';\n  tooltip?: string;\n  loading?: boolean;\n  loadingText?: string;\n  ripple?: boolean;\n  elevated?: boolean;\n  fullWidth?: boolean;\n}\n\nconst EnhancedButton: React.FC<EnhancedButtonProps> = ({\n  variant = 'primary',\n  size = 'medium',\n  tooltip,\n  loading = false,\n  loadingText,\n  ripple = true,\n  elevated = false,\n  fullWidth = false,\n  children,\n  className = '',\n  onClick,\n  ...props\n}) => {\n  const handleClick = (e: React.MouseEvent<HTMLElement>) => {\n    if (ripple && !loading && !props.disabled) {\n      createRipple(e);\n    }\n    if (onClick) {\n      onClick(e);\n    }\n  };\n\n  const createRipple = (event: React.MouseEvent<HTMLElement>) => {\n    const button = event.currentTarget;\n    const rect = button.getBoundingClientRect();\n    const size = Math.max(rect.width, rect.height);\n    const x = event.clientX - rect.left - size / 2;\n    const y = event.clientY - rect.top - size / 2;\n\n    const ripple = document.createElement('span');\n    ripple.className = 'button-ripple';\n    ripple.style.width = ripple.style.height = size + 'px';\n    ripple.style.left = x + 'px';\n    ripple.style.top = y + 'px';\n\n    button.appendChild(ripple);\n\n    setTimeout(() => {\n      ripple.remove();\n    }, 600);\n  };\n\n  const getButtonClass = () => {\n    const classes = ['enhanced-button'];\n    \n    classes.push(`enhanced-button--${variant}`);\n    classes.push(`enhanced-button--${size}`);\n    \n    if (elevated) classes.push('enhanced-button--elevated');\n    if (fullWidth) classes.push('enhanced-button--full-width');\n    if (loading) classes.push('enhanced-button--loading');\n    if (ripple) classes.push('enhanced-button--ripple');\n    \n    return [...classes, className].join(' ');\n  };\n\n  const buttonContent = (\n    <Button\n      {...props}\n      className={getButtonClass()}\n      onClick={handleClick}\n      disabled={props.disabled || loading}\n    >\n      {loading && <LoadingOutlined className=\"enhanced-button__loading-icon\" />}\n      <span className=\"enhanced-button__content\">\n        {loading && loadingText ? loadingText : children}\n      </span>\n    </Button>\n  );\n\n  if (tooltip) {\n    return (\n      <Tooltip title={tooltip} placement=\"top\">\n        {buttonContent}\n      </Tooltip>\n    );\n  }\n\n  return buttonContent;\n};\n\nexport default EnhancedButton;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAeC,OAAO,QAAQ,MAAM;AACnD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAatC,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,QAAQ;EACfC,OAAO;EACPC,OAAO,GAAG,KAAK;EACfC,WAAW;EACXC,MAAM,GAAG,IAAI;EACbC,QAAQ,GAAG,KAAK;EAChBC,SAAS,GAAG,KAAK;EACjBC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,OAAO;EACP,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAIC,CAAgC,IAAK;IACxD,IAAIR,MAAM,IAAI,CAACF,OAAO,IAAI,CAACQ,KAAK,CAACG,QAAQ,EAAE;MACzCC,YAAY,CAACF,CAAC,CAAC;IACjB;IACA,IAAIH,OAAO,EAAE;MACXA,OAAO,CAACG,CAAC,CAAC;IACZ;EACF,CAAC;EAED,MAAME,YAAY,GAAIC,KAAoC,IAAK;IAC7D,MAAMC,MAAM,GAAGD,KAAK,CAACE,aAAa;IAClC,MAAMC,IAAI,GAAGF,MAAM,CAACG,qBAAqB,CAAC,CAAC;IAC3C,MAAMnB,IAAI,GAAGoB,IAAI,CAACC,GAAG,CAACH,IAAI,CAACI,KAAK,EAAEJ,IAAI,CAACK,MAAM,CAAC;IAC9C,MAAMC,CAAC,GAAGT,KAAK,CAACU,OAAO,GAAGP,IAAI,CAACQ,IAAI,GAAG1B,IAAI,GAAG,CAAC;IAC9C,MAAM2B,CAAC,GAAGZ,KAAK,CAACa,OAAO,GAAGV,IAAI,CAACW,GAAG,GAAG7B,IAAI,GAAG,CAAC;IAE7C,MAAMI,MAAM,GAAG0B,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC7C3B,MAAM,CAACI,SAAS,GAAG,eAAe;IAClCJ,MAAM,CAAC4B,KAAK,CAACV,KAAK,GAAGlB,MAAM,CAAC4B,KAAK,CAACT,MAAM,GAAGvB,IAAI,GAAG,IAAI;IACtDI,MAAM,CAAC4B,KAAK,CAACN,IAAI,GAAGF,CAAC,GAAG,IAAI;IAC5BpB,MAAM,CAAC4B,KAAK,CAACH,GAAG,GAAGF,CAAC,GAAG,IAAI;IAE3BX,MAAM,CAACiB,WAAW,CAAC7B,MAAM,CAAC;IAE1B8B,UAAU,CAAC,MAAM;MACf9B,MAAM,CAAC+B,MAAM,CAAC,CAAC;IACjB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,OAAO,GAAG,CAAC,iBAAiB,CAAC;IAEnCA,OAAO,CAACC,IAAI,CAAC,oBAAoBvC,OAAO,EAAE,CAAC;IAC3CsC,OAAO,CAACC,IAAI,CAAC,oBAAoBtC,IAAI,EAAE,CAAC;IAExC,IAAIK,QAAQ,EAAEgC,OAAO,CAACC,IAAI,CAAC,2BAA2B,CAAC;IACvD,IAAIhC,SAAS,EAAE+B,OAAO,CAACC,IAAI,CAAC,6BAA6B,CAAC;IAC1D,IAAIpC,OAAO,EAAEmC,OAAO,CAACC,IAAI,CAAC,0BAA0B,CAAC;IACrD,IAAIlC,MAAM,EAAEiC,OAAO,CAACC,IAAI,CAAC,yBAAyB,CAAC;IAEnD,OAAO,CAAC,GAAGD,OAAO,EAAE7B,SAAS,CAAC,CAAC+B,IAAI,CAAC,GAAG,CAAC;EAC1C,CAAC;EAED,MAAMC,aAAa,gBACjB3C,OAAA,CAACJ,MAAM;IAAA,GACDiB,KAAK;IACTF,SAAS,EAAE4B,cAAc,CAAC,CAAE;IAC5B3B,OAAO,EAAEE,WAAY;IACrBE,QAAQ,EAAEH,KAAK,CAACG,QAAQ,IAAIX,OAAQ;IAAAK,QAAA,GAEnCL,OAAO,iBAAIL,OAAA,CAACF,eAAe;MAACa,SAAS,EAAC;IAA+B;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzE/C,OAAA;MAAMW,SAAS,EAAC,0BAA0B;MAAAD,QAAA,EACvCL,OAAO,IAAIC,WAAW,GAAGA,WAAW,GAAGI;IAAQ;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CACT;EAED,IAAI3C,OAAO,EAAE;IACX,oBACEJ,OAAA,CAACH,OAAO;MAACmD,KAAK,EAAE5C,OAAQ;MAAC6C,SAAS,EAAC,KAAK;MAAAvC,QAAA,EACrCiC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEd;EAEA,OAAOJ,aAAa;AACtB,CAAC;AAACO,EAAA,GAhFIjD,cAA6C;AAkFnD,eAAeA,cAAc;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}