{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { Component } from 'react';\nimport { Result, Button, Typography, Collapse, Alert } from 'antd';\nimport { BugOutlined, ReloadOutlined, HomeOutlined, ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';\nimport '../styles/ErrorBoundary.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  Panel\n} = Collapse;\nclass ErrorBoundary extends Component {\n  constructor(props) {\n    super(props);\n    this.reportError = (error, errorInfo) => {\n      // 这里可以集成错误监控服务，如 Sentry\n      console.error('Error Boundary caught an error:', {\n        error: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n        errorId: this.state.errorId,\n        timestamp: new Date().toISOString(),\n        userAgent: navigator.userAgent,\n        url: window.location.href\n      });\n    };\n    this.handleReload = () => {\n      window.location.reload();\n    };\n    this.handleGoHome = () => {\n      window.location.href = '/';\n    };\n    this.handleRetry = () => {\n      this.setState({\n        hasError: false,\n        error: null,\n        errorInfo: null,\n        errorId: ''\n      });\n    };\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null,\n      errorId: ''\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true,\n      error,\n      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    this.setState({\n      error,\n      errorInfo\n    });\n\n    // 调用外部错误处理函数\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n\n    // 发送错误报告到监控服务\n    this.reportError(error, errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      const {\n        fallback,\n        showDetails = true,\n        level = 'component'\n      } = this.props;\n      const {\n        error,\n        errorInfo,\n        errorId\n      } = this.state;\n\n      // 如果提供了自定义fallback，使用它\n      if (fallback) {\n        return fallback;\n      }\n\n      // 页面级错误\n      if (level === 'page') {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-boundary error-boundary--page\",\n          children: /*#__PURE__*/_jsxDEV(Result, {\n            status: \"500\",\n            title: \"\\u9875\\u9762\\u51FA\\u73B0\\u9519\\u8BEF\",\n            subTitle: \"\\u62B1\\u6B49\\uFF0C\\u9875\\u9762\\u9047\\u5230\\u4E86\\u4E00\\u4E9B\\u95EE\\u9898\\u3002\\u8BF7\\u5C1D\\u8BD5\\u5237\\u65B0\\u9875\\u9762\\u6216\\u8054\\u7CFB\\u6280\\u672F\\u652F\\u6301\\u3002\",\n            icon: /*#__PURE__*/_jsxDEV(BugOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 21\n            }, this),\n            extra: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 46\n              }, this),\n              onClick: this.handleReload,\n              children: \"\\u5237\\u65B0\\u9875\\u9762\"\n            }, \"reload\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(HomeOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 31\n              }, this),\n              onClick: this.handleGoHome,\n              children: \"\\u8FD4\\u56DE\\u9996\\u9875\"\n            }, \"home\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this)],\n            children: showDetails && error && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error-details\",\n              children: /*#__PURE__*/_jsxDEV(Alert, {\n                message: \"\\u9519\\u8BEF\\u8BE6\\u60C5\",\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: \"\\u9519\\u8BEFID:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 129,\n                      columnNumber: 27\n                    }, this), \" \", errorId]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: \"\\u9519\\u8BEF\\u4FE1\\u606F:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 132,\n                      columnNumber: 27\n                    }, this), \" \", error.message]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n                    ghost: true,\n                    children: /*#__PURE__*/_jsxDEV(Panel, {\n                      header: /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [/*#__PURE__*/_jsxDEV(InfoCircleOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 138,\n                          columnNumber: 33\n                        }, this), \" \\u6280\\u672F\\u8BE6\\u60C5\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 137,\n                        columnNumber: 31\n                      }, this),\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"error-stack\",\n                        children: /*#__PURE__*/_jsxDEV(Text, {\n                          code: true,\n                          children: error.stack\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 144,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 143,\n                        columnNumber: 29\n                      }, this), errorInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"error-component-stack\",\n                        children: [/*#__PURE__*/_jsxDEV(Text, {\n                          strong: true,\n                          children: \"\\u7EC4\\u4EF6\\u5806\\u6808:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 148,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(Text, {\n                          code: true,\n                          children: errorInfo.componentStack\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 149,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 147,\n                        columnNumber: 31\n                      }, this)]\n                    }, \"1\", true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 135,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 23\n                }, this),\n                type: \"error\",\n                showIcon: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this);\n      }\n\n      // 组件级错误\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-boundary error-boundary--component\",\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u7EC4\\u4EF6\\u52A0\\u8F7D\\u5931\\u8D25\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: \"\\u8BE5\\u7EC4\\u4EF6\\u9047\\u5230\\u4E86\\u9519\\u8BEF\\uFF0C\\u8BF7\\u5C1D\\u8BD5\\u91CD\\u65B0\\u52A0\\u8F7D\\u3002\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), showDetails && /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: [\"\\u9519\\u8BEFID: \", errorId]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this),\n          type: \"error\",\n          showIcon: true,\n          icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 19\n          }, this),\n          action: /*#__PURE__*/_jsxDEV(Button, {\n            size: \"small\",\n            onClick: this.handleRetry,\n            children: \"\\u91CD\\u8BD5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), showDetails && error && /*#__PURE__*/_jsxDEV(Collapse, {\n          ghost: true,\n          className: \"error-details-collapse\",\n          children: /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u67E5\\u770B\\u8BE6\\u7EC6\\u4FE1\\u606F\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error-details\",\n              children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u9519\\u8BEF\\u4FE1\\u606F:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this), \" \", error.message]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"error-stack\",\n                children: /*#__PURE__*/_jsxDEV(Text, {\n                  code: true,\n                  children: error.stack\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this)\n          }, \"1\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\n\n// 函数式错误边界Hook\nexport const useErrorHandler = () => {\n  _s();\n  const [error, setError] = React.useState(null);\n  const resetError = () => setError(null);\n  const handleError = React.useCallback(error => {\n    setError(error);\n  }, []);\n  React.useEffect(() => {\n    if (error) {\n      throw error;\n    }\n  }, [error]);\n  return {\n    handleError,\n    resetError\n  };\n};\n\n// 异步错误处理组件\n_s(useErrorHandler, \"Awaz/8rfBoM3TxhiBu60/CByzwk=\");\nexport const AsyncErrorBoundary = ({\n  children,\n  onError\n}) => {\n  _s2();\n  const {\n    handleError\n  } = useErrorHandler();\n  React.useEffect(() => {\n    const handleUnhandledRejection = event => {\n      const error = new Error(event.reason);\n      handleError(error);\n      if (onError) onError(error);\n    };\n    window.addEventListener('unhandledrejection', handleUnhandledRejection);\n    return () => {\n      window.removeEventListener('unhandledrejection', handleUnhandledRejection);\n    };\n  }, [handleError, onError]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s2(AsyncErrorBoundary, \"zgOlNFRXaesnrxis8mnnDvW6xVs=\", false, function () {\n  return [useErrorHandler];\n});\n_c = AsyncErrorBoundary;\nexport default ErrorBoundary;\nvar _c;\n$RefreshReg$(_c, \"AsyncErrorBoundary\");", "map": {"version": 3, "names": ["React", "Component", "Result", "<PERSON><PERSON>", "Typography", "Collapse", "<PERSON><PERSON>", "BugOutlined", "ReloadOutlined", "HomeOutlined", "ExclamationCircleOutlined", "InfoCircleOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Text", "Paragraph", "Panel", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "props", "reportError", "error", "errorInfo", "console", "message", "stack", "componentStack", "errorId", "state", "timestamp", "Date", "toISOString", "userAgent", "navigator", "url", "window", "location", "href", "handleReload", "reload", "handleGoHome", "handleRetry", "setState", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "now", "Math", "random", "toString", "substr", "componentDidCatch", "onError", "render", "fallback", "showDetails", "level", "className", "children", "status", "title", "subTitle", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "extra", "type", "onClick", "description", "strong", "ghost", "header", "code", "showIcon", "action", "size", "useErrorHandler", "_s", "setError", "useState", "resetError", "handleError", "useCallback", "useEffect", "AsyncErrorBoundary", "_s2", "handleUnhandledRejection", "event", "Error", "reason", "addEventListener", "removeEventListener", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/components/ErrorBoundary.tsx"], "sourcesContent": ["import React, { Component, ErrorInfo, ReactNode } from 'react';\nimport { Result, Button, Typography, Collapse, Alert } from 'antd';\nimport { \n  BugOutlined, \n  ReloadOutlined, \n  HomeOutlined,\n  ExclamationCircleOutlined,\n  InfoCircleOutlined\n} from '@ant-design/icons';\nimport '../styles/ErrorBoundary.css';\n\nconst { Text, Paragraph } = Typography;\nconst { Panel } = Collapse;\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\n  showDetails?: boolean;\n  level?: 'page' | 'component';\n}\n\ninterface State {\n  hasError: boolean;\n  error: Error | null;\n  errorInfo: ErrorInfo | null;\n  errorId: string;\n}\n\nclass ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null,\n      errorId: ''\n    };\n  }\n\n  static getDerivedStateFromError(error: Error): Partial<State> {\n    return {\n      hasError: true,\n      error,\n      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    this.setState({\n      error,\n      errorInfo\n    });\n\n    // 调用外部错误处理函数\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n\n    // 发送错误报告到监控服务\n    this.reportError(error, errorInfo);\n  }\n\n  reportError = (error: Error, errorInfo: ErrorInfo) => {\n    // 这里可以集成错误监控服务，如 Sentry\n    console.error('Error Boundary caught an error:', {\n      error: error.message,\n      stack: error.stack,\n      componentStack: errorInfo.componentStack,\n      errorId: this.state.errorId,\n      timestamp: new Date().toISOString(),\n      userAgent: navigator.userAgent,\n      url: window.location.href\n    });\n  };\n\n  handleReload = () => {\n    window.location.reload();\n  };\n\n  handleGoHome = () => {\n    window.location.href = '/';\n  };\n\n  handleRetry = () => {\n    this.setState({\n      hasError: false,\n      error: null,\n      errorInfo: null,\n      errorId: ''\n    });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      const { fallback, showDetails = true, level = 'component' } = this.props;\n      const { error, errorInfo, errorId } = this.state;\n\n      // 如果提供了自定义fallback，使用它\n      if (fallback) {\n        return fallback;\n      }\n\n      // 页面级错误\n      if (level === 'page') {\n        return (\n          <div className=\"error-boundary error-boundary--page\">\n            <Result\n              status=\"500\"\n              title=\"页面出现错误\"\n              subTitle=\"抱歉，页面遇到了一些问题。请尝试刷新页面或联系技术支持。\"\n              icon={<BugOutlined />}\n              extra={[\n                <Button type=\"primary\" icon={<ReloadOutlined />} onClick={this.handleReload} key=\"reload\">\n                  刷新页面\n                </Button>,\n                <Button icon={<HomeOutlined />} onClick={this.handleGoHome} key=\"home\">\n                  返回首页\n                </Button>\n              ]}\n            >\n              {showDetails && error && (\n                <div className=\"error-details\">\n                  <Alert\n                    message=\"错误详情\"\n                    description={\n                      <div>\n                        <Paragraph>\n                          <Text strong>错误ID:</Text> {errorId}\n                        </Paragraph>\n                        <Paragraph>\n                          <Text strong>错误信息:</Text> {error.message}\n                        </Paragraph>\n                        <Collapse ghost>\n                          <Panel \n                            header={\n                              <span>\n                                <InfoCircleOutlined /> 技术详情\n                              </span>\n                            } \n                            key=\"1\"\n                          >\n                            <div className=\"error-stack\">\n                              <Text code>{error.stack}</Text>\n                            </div>\n                            {errorInfo && (\n                              <div className=\"error-component-stack\">\n                                <Text strong>组件堆栈:</Text>\n                                <Text code>{errorInfo.componentStack}</Text>\n                              </div>\n                            )}\n                          </Panel>\n                        </Collapse>\n                      </div>\n                    }\n                    type=\"error\"\n                    showIcon\n                  />\n                </div>\n              )}\n            </Result>\n          </div>\n        );\n      }\n\n      // 组件级错误\n      return (\n        <div className=\"error-boundary error-boundary--component\">\n          <Alert\n            message=\"组件加载失败\"\n            description={\n              <div>\n                <Paragraph>\n                  该组件遇到了错误，请尝试重新加载。\n                </Paragraph>\n                {showDetails && (\n                  <Paragraph>\n                    <Text type=\"secondary\">错误ID: {errorId}</Text>\n                  </Paragraph>\n                )}\n              </div>\n            }\n            type=\"error\"\n            showIcon\n            icon={<ExclamationCircleOutlined />}\n            action={\n              <Button size=\"small\" onClick={this.handleRetry}>\n                重试\n              </Button>\n            }\n          />\n          \n          {showDetails && error && (\n            <Collapse ghost className=\"error-details-collapse\">\n              <Panel header=\"查看详细信息\" key=\"1\">\n                <div className=\"error-details\">\n                  <Paragraph>\n                    <Text strong>错误信息:</Text> {error.message}\n                  </Paragraph>\n                  <div className=\"error-stack\">\n                    <Text code>{error.stack}</Text>\n                  </div>\n                </div>\n              </Panel>\n            </Collapse>\n          )}\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n// 函数式错误边界Hook\nexport const useErrorHandler = () => {\n  const [error, setError] = React.useState<Error | null>(null);\n\n  const resetError = () => setError(null);\n\n  const handleError = React.useCallback((error: Error) => {\n    setError(error);\n  }, []);\n\n  React.useEffect(() => {\n    if (error) {\n      throw error;\n    }\n  }, [error]);\n\n  return { handleError, resetError };\n};\n\n// 异步错误处理组件\nexport const AsyncErrorBoundary: React.FC<{\n  children: ReactNode;\n  onError?: (error: Error) => void;\n}> = ({ children, onError }) => {\n  const { handleError } = useErrorHandler();\n\n  React.useEffect(() => {\n    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {\n      const error = new Error(event.reason);\n      handleError(error);\n      if (onError) onError(error);\n    };\n\n    window.addEventListener('unhandledrejection', handleUnhandledRejection);\n    return () => {\n      window.removeEventListener('unhandledrejection', handleUnhandledRejection);\n    };\n  }, [handleError, onError]);\n\n  return <>{children}</>;\n};\n\nexport default ErrorBoundary;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAA8B,OAAO;AAC9D,SAASC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,MAAM;AAClE,SACEC,WAAW,EACXC,cAAc,EACdC,YAAY,EACZC,yBAAyB,EACzBC,kBAAkB,QACb,mBAAmB;AAC1B,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErC,MAAM;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGb,UAAU;AACtC,MAAM;EAAEc;AAAM,CAAC,GAAGb,QAAQ;AAiB1B,MAAMc,aAAa,SAASlB,SAAS,CAAe;EAClDmB,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IAAC,KAgCfC,WAAW,GAAG,CAACC,KAAY,EAAEC,SAAoB,KAAK;MACpD;MACAC,OAAO,CAACF,KAAK,CAAC,iCAAiC,EAAE;QAC/CA,KAAK,EAAEA,KAAK,CAACG,OAAO;QACpBC,KAAK,EAAEJ,KAAK,CAACI,KAAK;QAClBC,cAAc,EAAEJ,SAAS,CAACI,cAAc;QACxCC,OAAO,EAAE,IAAI,CAACC,KAAK,CAACD,OAAO;QAC3BE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAEC,SAAS,CAACD,SAAS;QAC9BE,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC;MACvB,CAAC,CAAC;IACJ,CAAC;IAAA,KAEDC,YAAY,GAAG,MAAM;MACnBH,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC;IAC1B,CAAC;IAAA,KAEDC,YAAY,GAAG,MAAM;MACnBL,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;IAC5B,CAAC;IAAA,KAEDI,WAAW,GAAG,MAAM;MAClB,IAAI,CAACC,QAAQ,CAAC;QACZC,QAAQ,EAAE,KAAK;QACftB,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE,IAAI;QACfK,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC;IA3DC,IAAI,CAACC,KAAK,GAAG;MACXe,QAAQ,EAAE,KAAK;MACftB,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAI;MACfK,OAAO,EAAE;IACX,CAAC;EACH;EAEA,OAAOiB,wBAAwBA,CAACvB,KAAY,EAAkB;IAC5D,OAAO;MACLsB,QAAQ,EAAE,IAAI;MACdtB,KAAK;MACLM,OAAO,EAAE,SAASG,IAAI,CAACe,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IACzE,CAAC;EACH;EAEAC,iBAAiBA,CAAC7B,KAAY,EAAEC,SAAoB,EAAE;IACpD,IAAI,CAACoB,QAAQ,CAAC;MACZrB,KAAK;MACLC;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,IAAI,CAACH,KAAK,CAACgC,OAAO,EAAE;MACtB,IAAI,CAAChC,KAAK,CAACgC,OAAO,CAAC9B,KAAK,EAAEC,SAAS,CAAC;IACtC;;IAEA;IACA,IAAI,CAACF,WAAW,CAACC,KAAK,EAAEC,SAAS,CAAC;EACpC;EAgCA8B,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACxB,KAAK,CAACe,QAAQ,EAAE;MACvB,MAAM;QAAEU,QAAQ;QAAEC,WAAW,GAAG,IAAI;QAAEC,KAAK,GAAG;MAAY,CAAC,GAAG,IAAI,CAACpC,KAAK;MACxE,MAAM;QAAEE,KAAK;QAAEC,SAAS;QAAEK;MAAQ,CAAC,GAAG,IAAI,CAACC,KAAK;;MAEhD;MACA,IAAIyB,QAAQ,EAAE;QACZ,OAAOA,QAAQ;MACjB;;MAEA;MACA,IAAIE,KAAK,KAAK,MAAM,EAAE;QACpB,oBACE5C,OAAA;UAAK6C,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClD9C,OAAA,CAACX,MAAM;YACL0D,MAAM,EAAC,KAAK;YACZC,KAAK,EAAC,sCAAQ;YACdC,QAAQ,EAAC,0KAA8B;YACvCC,IAAI,eAAElD,OAAA,CAACN,WAAW;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtBC,KAAK,EAAE,cACLvD,OAAA,CAACV,MAAM;cAACkE,IAAI,EAAC,SAAS;cAACN,IAAI,eAAElD,OAAA,CAACL,cAAc;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACG,OAAO,EAAE,IAAI,CAAC9B,YAAa;cAAAmB,QAAA,EAAc;YAE1F,GAFiF,QAAQ;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjF,CAAC,eACTtD,OAAA,CAACV,MAAM;cAAC4D,IAAI,eAAElD,OAAA,CAACJ,YAAY;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACG,OAAO,EAAE,IAAI,CAAC5B,YAAa;cAAAiB,QAAA,EAAY;YAEvE,GAFgE,MAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE9D,CAAC,CACT;YAAAR,QAAA,EAEDH,WAAW,IAAIjC,KAAK,iBACnBV,OAAA;cAAK6C,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B9C,OAAA,CAACP,KAAK;gBACJoB,OAAO,EAAC,0BAAM;gBACd6C,WAAW,eACT1D,OAAA;kBAAA8C,QAAA,gBACE9C,OAAA,CAACI,SAAS;oBAAA0C,QAAA,gBACR9C,OAAA,CAACG,IAAI;sBAACwD,MAAM;sBAAAb,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACtC,OAAO;kBAAA;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACZtD,OAAA,CAACI,SAAS;oBAAA0C,QAAA,gBACR9C,OAAA,CAACG,IAAI;sBAACwD,MAAM;sBAAAb,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC5C,KAAK,CAACG,OAAO;kBAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACZtD,OAAA,CAACR,QAAQ;oBAACoE,KAAK;oBAAAd,QAAA,eACb9C,OAAA,CAACK,KAAK;sBACJwD,MAAM,eACJ7D,OAAA;wBAAA8C,QAAA,gBACE9C,OAAA,CAACF,kBAAkB;0BAAAqD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,6BACxB;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACP;sBAAAR,QAAA,gBAGD9C,OAAA;wBAAK6C,SAAS,EAAC,aAAa;wBAAAC,QAAA,eAC1B9C,OAAA,CAACG,IAAI;0BAAC2D,IAAI;0BAAAhB,QAAA,EAAEpC,KAAK,CAACI;wBAAK;0BAAAqC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC,EACL3C,SAAS,iBACRX,OAAA;wBAAK6C,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,gBACpC9C,OAAA,CAACG,IAAI;0BAACwD,MAAM;0BAAAb,QAAA,EAAC;wBAAK;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACzBtD,OAAA,CAACG,IAAI;0BAAC2D,IAAI;0BAAAhB,QAAA,EAAEnC,SAAS,CAACI;wBAAc;0BAAAoC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzC,CACN;oBAAA,GAVG,GAAG;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAWF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CACN;gBACDE,IAAI,EAAC,OAAO;gBACZO,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAEV;;MAEA;MACA,oBACEtD,OAAA;QAAK6C,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvD9C,OAAA,CAACP,KAAK;UACJoB,OAAO,EAAC,sCAAQ;UAChB6C,WAAW,eACT1D,OAAA;YAAA8C,QAAA,gBACE9C,OAAA,CAACI,SAAS;cAAA0C,QAAA,EAAC;YAEX;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EACXX,WAAW,iBACV3C,OAAA,CAACI,SAAS;cAAA0C,QAAA,eACR9C,OAAA,CAACG,IAAI;gBAACqD,IAAI,EAAC,WAAW;gBAAAV,QAAA,GAAC,kBAAM,EAAC9B,OAAO;cAAA;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CACZ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;UACDE,IAAI,EAAC,OAAO;UACZO,QAAQ;UACRb,IAAI,eAAElD,OAAA,CAACH,yBAAyB;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpCU,MAAM,eACJhE,OAAA,CAACV,MAAM;YAAC2E,IAAI,EAAC,OAAO;YAACR,OAAO,EAAE,IAAI,CAAC3B,WAAY;YAAAgB,QAAA,EAAC;UAEhD;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAEDX,WAAW,IAAIjC,KAAK,iBACnBV,OAAA,CAACR,QAAQ;UAACoE,KAAK;UAACf,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eAChD9C,OAAA,CAACK,KAAK;YAACwD,MAAM,EAAC,sCAAQ;YAAAf,QAAA,eACpB9C,OAAA;cAAK6C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B9C,OAAA,CAACI,SAAS;gBAAA0C,QAAA,gBACR9C,OAAA,CAACG,IAAI;kBAACwD,MAAM;kBAAAb,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC5C,KAAK,CAACG,OAAO;cAAA;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACZtD,OAAA;gBAAK6C,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1B9C,OAAA,CAACG,IAAI;kBAAC2D,IAAI;kBAAAhB,QAAA,EAAEpC,KAAK,CAACI;gBAAK;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GARmB,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAEV;IAEA,OAAO,IAAI,CAAC9C,KAAK,CAACsC,QAAQ;EAC5B;AACF;;AAEA;AACA,OAAO,MAAMoB,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACzD,KAAK,EAAE0D,QAAQ,CAAC,GAAGjF,KAAK,CAACkF,QAAQ,CAAe,IAAI,CAAC;EAE5D,MAAMC,UAAU,GAAGA,CAAA,KAAMF,QAAQ,CAAC,IAAI,CAAC;EAEvC,MAAMG,WAAW,GAAGpF,KAAK,CAACqF,WAAW,CAAE9D,KAAY,IAAK;IACtD0D,QAAQ,CAAC1D,KAAK,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAENvB,KAAK,CAACsF,SAAS,CAAC,MAAM;IACpB,IAAI/D,KAAK,EAAE;MACT,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAEX,OAAO;IAAE6D,WAAW;IAAED;EAAW,CAAC;AACpC,CAAC;;AAED;AAAAH,EAAA,CAlBaD,eAAe;AAmB5B,OAAO,MAAMQ,kBAGX,GAAGA,CAAC;EAAE5B,QAAQ;EAAEN;AAAQ,CAAC,KAAK;EAAAmC,GAAA;EAC9B,MAAM;IAAEJ;EAAY,CAAC,GAAGL,eAAe,CAAC,CAAC;EAEzC/E,KAAK,CAACsF,SAAS,CAAC,MAAM;IACpB,MAAMG,wBAAwB,GAAIC,KAA4B,IAAK;MACjE,MAAMnE,KAAK,GAAG,IAAIoE,KAAK,CAACD,KAAK,CAACE,MAAM,CAAC;MACrCR,WAAW,CAAC7D,KAAK,CAAC;MAClB,IAAI8B,OAAO,EAAEA,OAAO,CAAC9B,KAAK,CAAC;IAC7B,CAAC;IAEDc,MAAM,CAACwD,gBAAgB,CAAC,oBAAoB,EAAEJ,wBAAwB,CAAC;IACvE,OAAO,MAAM;MACXpD,MAAM,CAACyD,mBAAmB,CAAC,oBAAoB,EAAEL,wBAAwB,CAAC;IAC5E,CAAC;EACH,CAAC,EAAE,CAACL,WAAW,EAAE/B,OAAO,CAAC,CAAC;EAE1B,oBAAOxC,OAAA,CAAAE,SAAA;IAAA4C,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAAC6B,GAAA,CApBWD,kBAGX;EAAA,QACwBR,eAAe;AAAA;AAAAgB,EAAA,GAJ5BR,kBAGX;AAmBF,eAAepE,aAAa;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}