{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\components\\\\ResponsiveLayout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Layout, Button, Drawer } from 'antd';\nimport { MenuOutlined, CloseOutlined } from '@ant-design/icons';\nimport '../styles/ResponsiveLayout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Content,\n  Sider\n} = Layout;\nconst ResponsiveLayout = ({\n  children,\n  sidebar,\n  header,\n  showSidebar = true,\n  sidebarWidth = 260,\n  collapsible = true\n}) => {\n  _s();\n  const [isMobile, setIsMobile] = useState(false);\n  const [sidebarVisible, setSidebarVisible] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n  const toggleSidebar = () => {\n    if (isMobile) {\n      setSidebarVisible(!sidebarVisible);\n    } else {\n      setCollapsed(!collapsed);\n    }\n  };\n  const closeMobileSidebar = () => {\n    setSidebarVisible(false);\n  };\n  if (isMobile) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      className: \"responsive-layout mobile\",\n      children: [header && /*#__PURE__*/_jsxDEV(Header, {\n        className: \"responsive-header\",\n        children: [showSidebar && /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(MenuOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 23\n          }, this),\n          onClick: toggleSidebar,\n          className: \"mobile-menu-button\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 15\n        }, this), header]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        className: \"responsive-content\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), showSidebar && sidebar && /*#__PURE__*/_jsxDEV(Drawer, {\n        title: \"\\u83DC\\u5355\",\n        placement: \"left\",\n        onClose: closeMobileSidebar,\n        open: sidebarVisible,\n        className: \"mobile-sidebar-drawer\",\n        width: 280,\n        closeIcon: /*#__PURE__*/_jsxDEV(CloseOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 24\n        }, this),\n        children: sidebar\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    className: \"responsive-layout desktop\",\n    children: [showSidebar && sidebar && /*#__PURE__*/_jsxDEV(Sider, {\n      width: sidebarWidth,\n      collapsed: collapsed,\n      collapsible: collapsible,\n      onCollapse: setCollapsed,\n      className: \"responsive-sidebar\",\n      theme: \"light\",\n      children: sidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {\n      className: \"responsive-main\",\n      children: [header && /*#__PURE__*/_jsxDEV(Header, {\n        className: \"responsive-header\",\n        children: header\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        className: \"responsive-content\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s(ResponsiveLayout, \"w8ixnGcDcg3jQJugfT/3RdM7rDk=\");\n_c = ResponsiveLayout;\nexport default ResponsiveLayout;\nvar _c;\n$RefreshReg$(_c, \"ResponsiveLayout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "<PERSON><PERSON>", "Drawer", "MenuOutlined", "CloseOutlined", "jsxDEV", "_jsxDEV", "Header", "Content", "<PERSON><PERSON>", "ResponsiveLayout", "children", "sidebar", "header", "showSidebar", "sidebarWidth", "collapsible", "_s", "isMobile", "setIsMobile", "sidebarVisible", "setSidebarVisible", "collapsed", "setCollapsed", "checkMobile", "window", "innerWidth", "addEventListener", "removeEventListener", "toggleSidebar", "closeMobileSidebar", "className", "type", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "placement", "onClose", "open", "width", "closeIcon", "onCollapse", "theme", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/components/ResponsiveLayout.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Layout, But<PERSON>, Drawer } from 'antd';\nimport { MenuOutlined, CloseOutlined } from '@ant-design/icons';\nimport '../styles/ResponsiveLayout.css';\n\nconst { Header, Content, Sider } = Layout;\n\ninterface ResponsiveLayoutProps {\n  children: React.ReactNode;\n  sidebar?: React.ReactNode;\n  header?: React.ReactNode;\n  showSidebar?: boolean;\n  sidebarWidth?: number;\n  collapsible?: boolean;\n}\n\nconst ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({\n  children,\n  sidebar,\n  header,\n  showSidebar = true,\n  sidebarWidth = 260,\n  collapsible = true\n}) => {\n  const [isMobile, setIsMobile] = useState(false);\n  const [sidebarVisible, setSidebarVisible] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  const toggleSidebar = () => {\n    if (isMobile) {\n      setSidebarVisible(!sidebarVisible);\n    } else {\n      setCollapsed(!collapsed);\n    }\n  };\n\n  const closeMobileSidebar = () => {\n    setSidebarVisible(false);\n  };\n\n  if (isMobile) {\n    return (\n      <Layout className=\"responsive-layout mobile\">\n        {header && (\n          <Header className=\"responsive-header\">\n            {showSidebar && (\n              <Button\n                type=\"text\"\n                icon={<MenuOutlined />}\n                onClick={toggleSidebar}\n                className=\"mobile-menu-button\"\n              />\n            )}\n            {header}\n          </Header>\n        )}\n        \n        <Content className=\"responsive-content\">\n          {children}\n        </Content>\n\n        {showSidebar && sidebar && (\n          <Drawer\n            title=\"菜单\"\n            placement=\"left\"\n            onClose={closeMobileSidebar}\n            open={sidebarVisible}\n            className=\"mobile-sidebar-drawer\"\n            width={280}\n            closeIcon={<CloseOutlined />}\n          >\n            {sidebar}\n          </Drawer>\n        )}\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout className=\"responsive-layout desktop\">\n      {showSidebar && sidebar && (\n        <Sider\n          width={sidebarWidth}\n          collapsed={collapsed}\n          collapsible={collapsible}\n          onCollapse={setCollapsed}\n          className=\"responsive-sidebar\"\n          theme=\"light\"\n        >\n          {sidebar}\n        </Sider>\n      )}\n      \n      <Layout className=\"responsive-main\">\n        {header && (\n          <Header className=\"responsive-header\">\n            {header}\n          </Header>\n        )}\n        \n        <Content className=\"responsive-content\">\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default ResponsiveLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,MAAM,EAAEC,MAAM,QAAQ,MAAM;AAC7C,SAASC,YAAY,EAAEC,aAAa,QAAQ,mBAAmB;AAC/D,OAAO,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAM;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAM,CAAC,GAAGT,MAAM;AAWzC,MAAMU,gBAAiD,GAAGA,CAAC;EACzDC,QAAQ;EACRC,OAAO;EACPC,MAAM;EACNC,WAAW,GAAG,IAAI;EAClBC,YAAY,GAAG,GAAG;EAClBC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACsB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACd,MAAMyB,WAAW,GAAGA,CAAA,KAAM;MACxBL,WAAW,CAACM,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;IAEDF,WAAW,CAAC,CAAC;IACbC,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,WAAW,CAAC;IAC9C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,WAAW,CAAC;EAChE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIX,QAAQ,EAAE;MACZG,iBAAiB,CAAC,CAACD,cAAc,CAAC;IACpC,CAAC,MAAM;MACLG,YAAY,CAAC,CAACD,SAAS,CAAC;IAC1B;EACF,CAAC;EAED,MAAMQ,kBAAkB,GAAGA,CAAA,KAAM;IAC/BT,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,IAAIH,QAAQ,EAAE;IACZ,oBACEZ,OAAA,CAACN,MAAM;MAAC+B,SAAS,EAAC,0BAA0B;MAAApB,QAAA,GACzCE,MAAM,iBACLP,OAAA,CAACC,MAAM;QAACwB,SAAS,EAAC,mBAAmB;QAAApB,QAAA,GAClCG,WAAW,iBACVR,OAAA,CAACL,MAAM;UACL+B,IAAI,EAAC,MAAM;UACXC,IAAI,eAAE3B,OAAA,CAACH,YAAY;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBC,OAAO,EAAET,aAAc;UACvBE,SAAS,EAAC;QAAoB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CACF,EACAxB,MAAM;MAAA;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACT,eAED/B,OAAA,CAACE,OAAO;QAACuB,SAAS,EAAC,oBAAoB;QAAApB,QAAA,EACpCA;MAAQ;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAETvB,WAAW,IAAIF,OAAO,iBACrBN,OAAA,CAACJ,MAAM;QACLqC,KAAK,EAAC,cAAI;QACVC,SAAS,EAAC,MAAM;QAChBC,OAAO,EAAEX,kBAAmB;QAC5BY,IAAI,EAAEtB,cAAe;QACrBW,SAAS,EAAC,uBAAuB;QACjCY,KAAK,EAAE,GAAI;QACXC,SAAS,eAAEtC,OAAA,CAACF,aAAa;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAA1B,QAAA,EAE5BC;MAAO;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAEb;EAEA,oBACE/B,OAAA,CAACN,MAAM;IAAC+B,SAAS,EAAC,2BAA2B;IAAApB,QAAA,GAC1CG,WAAW,IAAIF,OAAO,iBACrBN,OAAA,CAACG,KAAK;MACJkC,KAAK,EAAE5B,YAAa;MACpBO,SAAS,EAAEA,SAAU;MACrBN,WAAW,EAAEA,WAAY;MACzB6B,UAAU,EAAEtB,YAAa;MACzBQ,SAAS,EAAC,oBAAoB;MAC9Be,KAAK,EAAC,OAAO;MAAAnC,QAAA,EAEZC;IAAO;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAED/B,OAAA,CAACN,MAAM;MAAC+B,SAAS,EAAC,iBAAiB;MAAApB,QAAA,GAChCE,MAAM,iBACLP,OAAA,CAACC,MAAM;QAACwB,SAAS,EAAC,mBAAmB;QAAApB,QAAA,EAClCE;MAAM;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACT,eAED/B,OAAA,CAACE,OAAO;QAACuB,SAAS,EAAC,oBAAoB;QAAApB,QAAA,EACpCA;MAAQ;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACpB,EAAA,CApGIP,gBAAiD;AAAAqC,EAAA,GAAjDrC,gBAAiD;AAsGvD,eAAeA,gBAAgB;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}