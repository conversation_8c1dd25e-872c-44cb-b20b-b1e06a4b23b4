{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\components\\\\SafeComponentWrapper.tsx\",\n  _s = $RefreshSig$();\n/**\n * 安全组件包装器 - 提供自动回退和错误处理\n */\n\nimport React, { Component } from 'react';\nimport { featureFlags } from '../utils/featureFlags';\nimport { errorMonitoring } from '../utils/errorMonitoring';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n/**\n * 安全组件包装器类\n * 自动处理错误并回退到原始组件\n */\nclass SafeComponentWrapper extends Component {\n  constructor(props) {\n    super(props);\n    this.maxRetries = 3;\n    this.recoveryTimer = null;\n    this.scheduleRecovery = () => {\n      const delay = this.props.recoveryDelay || 3000;\n      this.setState({\n        isRecovering: true\n      });\n      this.recoveryTimer = setTimeout(() => {\n        this.setState(prevState => ({\n          hasError: false,\n          error: null,\n          errorInfo: null,\n          retryCount: prevState.retryCount + 1,\n          isRecovering: false\n        }));\n      }, delay);\n    };\n    this.handleManualRetry = () => {\n      this.setState({\n        hasError: false,\n        error: null,\n        errorInfo: null,\n        retryCount: 0,\n        isRecovering: false\n      });\n    };\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null,\n      retryCount: 0,\n      isRecovering: false\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    const {\n      feature,\n      componentName,\n      onError\n    } = this.props;\n    this.setState({\n      error,\n      errorInfo\n    });\n\n    // 报告错误到监控系统\n    errorMonitoring.reportError({\n      component: componentName,\n      feature,\n      error: {\n        message: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack\n      }\n    });\n\n    // 调用外部错误处理器\n    if (onError) {\n      onError(error, errorInfo);\n    }\n\n    // 尝试自动恢复\n    if (this.props.enableAutoRecovery && this.state.retryCount < this.maxRetries) {\n      this.scheduleRecovery();\n    }\n    console.error(`SafeComponentWrapper caught error in ${componentName}:`, error);\n  }\n  componentWillUnmount() {\n    if (this.recoveryTimer) {\n      clearTimeout(this.recoveryTimer);\n    }\n  }\n  render() {\n    const {\n      children,\n      fallback,\n      feature,\n      componentName\n    } = this.props;\n    const {\n      hasError,\n      isRecovering,\n      retryCount\n    } = this.state;\n\n    // 检查功能是否启用\n    if (!featureFlags.isEnabled(feature)) {\n      return fallback;\n    }\n\n    // 如果有错误，显示回退内容\n    if (hasError) {\n      if (isRecovering) {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"safe-component-recovering\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '16px',\n              background: '#fff7e6',\n              border: '1px solid #ffd591',\n              borderRadius: '6px',\n              textAlign: 'center',\n              color: '#d46b08'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"\\uD83D\\uDD04 \\u7EC4\\u4EF6\\u6062\\u590D\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '12px',\n                marginTop: '4px'\n              },\n              children: [\"\\u5C1D\\u8BD5\\u6B21\\u6570: \", retryCount + 1, \"/\", this.maxRetries]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"safe-component-error\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px',\n            background: '#fff2f0',\n            border: '1px solid #ffccc7',\n            borderRadius: '6px',\n            color: '#a8071a'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '8px'\n            },\n            children: [\"\\u26A0\\uFE0F \", componentName, \" \\u7EC4\\u4EF6\\u51FA\\u73B0\\u9519\\u8BEF\\uFF0C\\u5DF2\\u81EA\\u52A8\\u56DE\\u9000\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), retryCount < this.maxRetries && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: this.handleManualRetry,\n            style: {\n              background: '#1890ff',\n              color: 'white',\n              border: 'none',\n              padding: '4px 8px',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\u91CD\\u8BD5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px'\n          },\n          children: fallback\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this);\n    }\n    return children;\n  }\n}\n\n/**\n * 高阶组件：为组件添加安全包装\n */\nexport function withSafeWrapper(WrappedComponent, FallbackComponent, feature, componentName) {\n  const SafeWrappedComponent = props => {\n    return /*#__PURE__*/_jsxDEV(SafeComponentWrapper, {\n      feature: feature,\n      componentName: componentName,\n      fallback: /*#__PURE__*/_jsxDEV(FallbackComponent, {\n        ...props\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 19\n      }, this),\n      enableAutoRecovery: true,\n      recoveryDelay: 3000,\n      children: /*#__PURE__*/_jsxDEV(WrappedComponent, {\n        ...props\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this);\n  };\n  SafeWrappedComponent.displayName = `SafeWrapper(${componentName})`;\n  return SafeWrappedComponent;\n}\n\n/**\n * Hook：安全地使用新组件\n */\nexport function useSafeComponent(NewComponent, FallbackComponent, feature) {\n  _s();\n  return React.useMemo(() => {\n    if (featureFlags.isEnabled(feature)) {\n      return NewComponent;\n    }\n    return FallbackComponent;\n  }, [NewComponent, FallbackComponent, feature]);\n}\n\n/**\n * 条件渲染组件：根据功能开关选择组件\n */\n_s(useSafeComponent, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nexport function ConditionalComponent({\n  feature,\n  newComponent: NewComponent,\n  fallbackComponent: FallbackComponent,\n  componentProps,\n  enableSafeWrapper = true,\n  componentName = 'ConditionalComponent'\n}) {\n  const isEnabled = featureFlags.isEnabled(feature);\n  if (!isEnabled) {\n    return /*#__PURE__*/_jsxDEV(FallbackComponent, {\n      ...componentProps\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 12\n    }, this);\n  }\n  if (enableSafeWrapper) {\n    return /*#__PURE__*/_jsxDEV(SafeComponentWrapper, {\n      feature: feature,\n      componentName: componentName,\n      fallback: /*#__PURE__*/_jsxDEV(FallbackComponent, {\n        ...componentProps\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 19\n      }, this),\n      enableAutoRecovery: true,\n      children: /*#__PURE__*/_jsxDEV(NewComponent, {\n        ...componentProps\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(NewComponent, {\n    ...componentProps\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 270,\n    columnNumber: 10\n  }, this);\n}\n\n/**\n * 功能开关组件：简单的条件渲染\n */\n_c = ConditionalComponent;\nexport function FeatureToggle({\n  feature,\n  children,\n  fallback = null\n}) {\n  const isEnabled = featureFlags.isEnabled(feature);\n  return isEnabled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false) : /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: fallback\n  }, void 0, false);\n}\n\n/**\n * 渐进式增强组件：逐步启用功能\n */\n_c2 = FeatureToggle;\nexport function ProgressiveEnhancement({\n  features,\n  fallback\n}) {\n  // 找到第一个启用的功能\n  for (const {\n    feature,\n    component\n  } of features) {\n    if (featureFlags.isEnabled(feature)) {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: component\n      }, void 0, false);\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: fallback\n  }, void 0, false);\n}\n_c3 = ProgressiveEnhancement;\nexport default SafeComponentWrapper;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ConditionalComponent\");\n$RefreshReg$(_c2, \"FeatureToggle\");\n$RefreshReg$(_c3, \"ProgressiveEnhancement\");", "map": {"version": 3, "names": ["React", "Component", "featureFlags", "errorMonitoring", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SafeComponentWrapper", "constructor", "props", "maxRetries", "recoveryTimer", "scheduleRecovery", "delay", "recoveryDelay", "setState", "isRecovering", "setTimeout", "prevState", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "retryCount", "handleManualRetry", "state", "getDerivedStateFromError", "componentDidCatch", "feature", "componentName", "onError", "reportError", "component", "message", "stack", "componentStack", "enableAutoRecovery", "console", "componentWillUnmount", "clearTimeout", "render", "children", "fallback", "isEnabled", "className", "style", "padding", "background", "border", "borderRadius", "textAlign", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "marginTop", "marginBottom", "onClick", "cursor", "withSafeWrapper", "WrappedComponent", "FallbackComponent", "SafeWrappedComponent", "displayName", "useSafeComponent", "NewComponent", "_s", "useMemo", "ConditionalComponent", "newComponent", "fallbackComponent", "componentProps", "enableSafeWrapper", "_c", "FeatureToggle", "_c2", "ProgressiveEnhancement", "features", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/components/SafeComponentWrapper.tsx"], "sourcesContent": ["/**\n * 安全组件包装器 - 提供自动回退和错误处理\n */\n\nimport React, { Component, ReactNode, ErrorInfo } from 'react';\nimport { featureFlags, FeatureFlags } from '../utils/featureFlags';\nimport { errorMonitoring } from '../utils/errorMonitoring';\n\ninterface SafeComponentWrapperProps {\n  children: ReactNode;\n  fallback: ReactNode;\n  feature: keyof FeatureFlags;\n  componentName: string;\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\n  enableAutoRecovery?: boolean;\n  recoveryDelay?: number;\n}\n\ninterface SafeComponentWrapperState {\n  hasError: boolean;\n  error: Error | null;\n  errorInfo: ErrorInfo | null;\n  retryCount: number;\n  isRecovering: boolean;\n}\n\n/**\n * 安全组件包装器类\n * 自动处理错误并回退到原始组件\n */\nclass SafeComponentWrapper extends Component<SafeComponentWrapperProps, SafeComponentWrapperState> {\n  private maxRetries = 3;\n  private recoveryTimer: NodeJS.Timeout | null = null;\n\n  constructor(props: SafeComponentWrapperProps) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null,\n      retryCount: 0,\n      isRecovering: false,\n    };\n  }\n\n  static getDerivedStateFromError(error: Error): Partial<SafeComponentWrapperState> {\n    return {\n      hasError: true,\n      error,\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    const { feature, componentName, onError } = this.props;\n\n    this.setState({\n      error,\n      errorInfo,\n    });\n\n    // 报告错误到监控系统\n    errorMonitoring.reportError({\n      component: componentName,\n      feature,\n      error: {\n        message: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n      },\n    });\n\n    // 调用外部错误处理器\n    if (onError) {\n      onError(error, errorInfo);\n    }\n\n    // 尝试自动恢复\n    if (this.props.enableAutoRecovery && this.state.retryCount < this.maxRetries) {\n      this.scheduleRecovery();\n    }\n\n    console.error(`SafeComponentWrapper caught error in ${componentName}:`, error);\n  }\n\n  private scheduleRecovery = () => {\n    const delay = this.props.recoveryDelay || 3000;\n    \n    this.setState({ isRecovering: true });\n\n    this.recoveryTimer = setTimeout(() => {\n      this.setState(prevState => ({\n        hasError: false,\n        error: null,\n        errorInfo: null,\n        retryCount: prevState.retryCount + 1,\n        isRecovering: false,\n      }));\n    }, delay);\n  };\n\n  private handleManualRetry = () => {\n    this.setState({\n      hasError: false,\n      error: null,\n      errorInfo: null,\n      retryCount: 0,\n      isRecovering: false,\n    });\n  };\n\n  componentWillUnmount() {\n    if (this.recoveryTimer) {\n      clearTimeout(this.recoveryTimer);\n    }\n  }\n\n  render() {\n    const { children, fallback, feature, componentName } = this.props;\n    const { hasError, isRecovering, retryCount } = this.state;\n\n    // 检查功能是否启用\n    if (!featureFlags.isEnabled(feature)) {\n      return fallback;\n    }\n\n    // 如果有错误，显示回退内容\n    if (hasError) {\n      if (isRecovering) {\n        return (\n          <div className=\"safe-component-recovering\">\n            <div style={{\n              padding: '16px',\n              background: '#fff7e6',\n              border: '1px solid #ffd591',\n              borderRadius: '6px',\n              textAlign: 'center',\n              color: '#d46b08'\n            }}>\n              <div>🔄 组件恢复中...</div>\n              <div style={{ fontSize: '12px', marginTop: '4px' }}>\n                尝试次数: {retryCount + 1}/{this.maxRetries}\n              </div>\n            </div>\n          </div>\n        );\n      }\n\n      return (\n        <div className=\"safe-component-error\">\n          <div style={{\n            padding: '16px',\n            background: '#fff2f0',\n            border: '1px solid #ffccc7',\n            borderRadius: '6px',\n            color: '#a8071a'\n          }}>\n            <div style={{ marginBottom: '8px' }}>\n              ⚠️ {componentName} 组件出现错误，已自动回退\n            </div>\n            {retryCount < this.maxRetries && (\n              <button\n                onClick={this.handleManualRetry}\n                style={{\n                  background: '#1890ff',\n                  color: 'white',\n                  border: 'none',\n                  padding: '4px 8px',\n                  borderRadius: '4px',\n                  cursor: 'pointer',\n                  fontSize: '12px'\n                }}\n              >\n                重试\n              </button>\n            )}\n          </div>\n          <div style={{ marginTop: '8px' }}>\n            {fallback}\n          </div>\n        </div>\n      );\n    }\n\n    return children;\n  }\n}\n\n/**\n * 高阶组件：为组件添加安全包装\n */\nexport function withSafeWrapper<P extends object>(\n  WrappedComponent: React.ComponentType<P>,\n  FallbackComponent: React.ComponentType<P>,\n  feature: keyof FeatureFlags,\n  componentName: string\n) {\n  const SafeWrappedComponent = (props: P) => {\n    return (\n      <SafeComponentWrapper\n        feature={feature}\n        componentName={componentName}\n        fallback={<FallbackComponent {...props} />}\n        enableAutoRecovery={true}\n        recoveryDelay={3000}\n      >\n        <WrappedComponent {...props} />\n      </SafeComponentWrapper>\n    );\n  };\n\n  SafeWrappedComponent.displayName = `SafeWrapper(${componentName})`;\n  return SafeWrappedComponent;\n}\n\n/**\n * Hook：安全地使用新组件\n */\nexport function useSafeComponent<P extends object>(\n  NewComponent: React.ComponentType<P>,\n  FallbackComponent: React.ComponentType<P>,\n  feature: keyof FeatureFlags\n): React.ComponentType<P> {\n  return React.useMemo(() => {\n    if (featureFlags.isEnabled(feature)) {\n      return NewComponent;\n    }\n    return FallbackComponent;\n  }, [NewComponent, FallbackComponent, feature]);\n}\n\n/**\n * 条件渲染组件：根据功能开关选择组件\n */\ninterface ConditionalComponentProps<P extends object> {\n  feature: keyof FeatureFlags;\n  newComponent: React.ComponentType<P>;\n  fallbackComponent: React.ComponentType<P>;\n  componentProps: P;\n  enableSafeWrapper?: boolean;\n  componentName?: string;\n}\n\nexport function ConditionalComponent<P extends object>({\n  feature,\n  newComponent: NewComponent,\n  fallbackComponent: FallbackComponent,\n  componentProps,\n  enableSafeWrapper = true,\n  componentName = 'ConditionalComponent'\n}: ConditionalComponentProps<P>) {\n  const isEnabled = featureFlags.isEnabled(feature);\n\n  if (!isEnabled) {\n    return <FallbackComponent {...componentProps} />;\n  }\n\n  if (enableSafeWrapper) {\n    return (\n      <SafeComponentWrapper\n        feature={feature}\n        componentName={componentName}\n        fallback={<FallbackComponent {...componentProps} />}\n        enableAutoRecovery={true}\n      >\n        <NewComponent {...componentProps} />\n      </SafeComponentWrapper>\n    );\n  }\n\n  return <NewComponent {...componentProps} />;\n}\n\n/**\n * 功能开关组件：简单的条件渲染\n */\ninterface FeatureToggleProps {\n  feature: keyof FeatureFlags;\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\nexport function FeatureToggle({ feature, children, fallback = null }: FeatureToggleProps) {\n  const isEnabled = featureFlags.isEnabled(feature);\n  return isEnabled ? <>{children}</> : <>{fallback}</>;\n}\n\n/**\n * 渐进式增强组件：逐步启用功能\n */\ninterface ProgressiveEnhancementProps {\n  features: Array<{\n    feature: keyof FeatureFlags;\n    component: ReactNode;\n  }>;\n  fallback: ReactNode;\n}\n\nexport function ProgressiveEnhancement({ features, fallback }: ProgressiveEnhancementProps) {\n  // 找到第一个启用的功能\n  for (const { feature, component } of features) {\n    if (featureFlags.isEnabled(feature)) {\n      return <>{component}</>;\n    }\n  }\n  \n  return <>{fallback}</>;\n}\n\nexport default SafeComponentWrapper;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,SAAS,QAA8B,OAAO;AAC9D,SAASC,YAAY,QAAsB,uBAAuB;AAClE,SAASC,eAAe,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAoB3D;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,SAASP,SAAS,CAAuD;EAIjGQ,WAAWA,CAACC,KAAgC,EAAE;IAC5C,KAAK,CAACA,KAAK,CAAC;IAAC,KAJPC,UAAU,GAAG,CAAC;IAAA,KACdC,aAAa,GAA0B,IAAI;IAAA,KAoD3CC,gBAAgB,GAAG,MAAM;MAC/B,MAAMC,KAAK,GAAG,IAAI,CAACJ,KAAK,CAACK,aAAa,IAAI,IAAI;MAE9C,IAAI,CAACC,QAAQ,CAAC;QAAEC,YAAY,EAAE;MAAK,CAAC,CAAC;MAErC,IAAI,CAACL,aAAa,GAAGM,UAAU,CAAC,MAAM;QACpC,IAAI,CAACF,QAAQ,CAACG,SAAS,KAAK;UAC1BC,QAAQ,EAAE,KAAK;UACfC,KAAK,EAAE,IAAI;UACXC,SAAS,EAAE,IAAI;UACfC,UAAU,EAAEJ,SAAS,CAACI,UAAU,GAAG,CAAC;UACpCN,YAAY,EAAE;QAChB,CAAC,CAAC,CAAC;MACL,CAAC,EAAEH,KAAK,CAAC;IACX,CAAC;IAAA,KAEOU,iBAAiB,GAAG,MAAM;MAChC,IAAI,CAACR,QAAQ,CAAC;QACZI,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,CAAC;QACbN,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC;IAxEC,IAAI,CAACQ,KAAK,GAAG;MACXL,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,CAAC;MACbN,YAAY,EAAE;IAChB,CAAC;EACH;EAEA,OAAOS,wBAAwBA,CAACL,KAAY,EAAsC;IAChF,OAAO;MACLD,QAAQ,EAAE,IAAI;MACdC;IACF,CAAC;EACH;EAEAM,iBAAiBA,CAACN,KAAY,EAAEC,SAAoB,EAAE;IACpD,MAAM;MAAEM,OAAO;MAAEC,aAAa;MAAEC;IAAQ,CAAC,GAAG,IAAI,CAACpB,KAAK;IAEtD,IAAI,CAACM,QAAQ,CAAC;MACZK,KAAK;MACLC;IACF,CAAC,CAAC;;IAEF;IACAnB,eAAe,CAAC4B,WAAW,CAAC;MAC1BC,SAAS,EAAEH,aAAa;MACxBD,OAAO;MACPP,KAAK,EAAE;QACLY,OAAO,EAAEZ,KAAK,CAACY,OAAO;QACtBC,KAAK,EAAEb,KAAK,CAACa,KAAK;QAClBC,cAAc,EAAEb,SAAS,CAACa;MAC5B;IACF,CAAC,CAAC;;IAEF;IACA,IAAIL,OAAO,EAAE;MACXA,OAAO,CAACT,KAAK,EAAEC,SAAS,CAAC;IAC3B;;IAEA;IACA,IAAI,IAAI,CAACZ,KAAK,CAAC0B,kBAAkB,IAAI,IAAI,CAACX,KAAK,CAACF,UAAU,GAAG,IAAI,CAACZ,UAAU,EAAE;MAC5E,IAAI,CAACE,gBAAgB,CAAC,CAAC;IACzB;IAEAwB,OAAO,CAAChB,KAAK,CAAC,wCAAwCQ,aAAa,GAAG,EAAER,KAAK,CAAC;EAChF;EA4BAiB,oBAAoBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAAC1B,aAAa,EAAE;MACtB2B,YAAY,CAAC,IAAI,CAAC3B,aAAa,CAAC;IAClC;EACF;EAEA4B,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC,QAAQ;MAAEC,QAAQ;MAAEd,OAAO;MAAEC;IAAc,CAAC,GAAG,IAAI,CAACnB,KAAK;IACjE,MAAM;MAAEU,QAAQ;MAAEH,YAAY;MAAEM;IAAW,CAAC,GAAG,IAAI,CAACE,KAAK;;IAEzD;IACA,IAAI,CAACvB,YAAY,CAACyC,SAAS,CAACf,OAAO,CAAC,EAAE;MACpC,OAAOc,QAAQ;IACjB;;IAEA;IACA,IAAItB,QAAQ,EAAE;MACZ,IAAIH,YAAY,EAAE;QAChB,oBACEZ,OAAA;UAAKuC,SAAS,EAAC,2BAA2B;UAAAH,QAAA,eACxCpC,OAAA;YAAKwC,KAAK,EAAE;cACVC,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,SAAS;cACrBC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBC,SAAS,EAAE,QAAQ;cACnBC,KAAK,EAAE;YACT,CAAE;YAAAV,QAAA,gBACApC,OAAA;cAAAoC,QAAA,EAAK;YAAW;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtBlD,OAAA;cAAKwC,KAAK,EAAE;gBAAEW,QAAQ,EAAE,MAAM;gBAAEC,SAAS,EAAE;cAAM,CAAE;cAAAhB,QAAA,GAAC,4BAC5C,EAAClB,UAAU,GAAG,CAAC,EAAC,GAAC,EAAC,IAAI,CAACZ,UAAU;YAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV;MAEA,oBACElD,OAAA;QAAKuC,SAAS,EAAC,sBAAsB;QAAAH,QAAA,gBACnCpC,OAAA;UAAKwC,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,SAAS;YACrBC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBE,KAAK,EAAE;UACT,CAAE;UAAAV,QAAA,gBACApC,OAAA;YAAKwC,KAAK,EAAE;cAAEa,YAAY,EAAE;YAAM,CAAE;YAAAjB,QAAA,GAAC,eAChC,EAACZ,aAAa,EAAC,2EACpB;UAAA;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACLhC,UAAU,GAAG,IAAI,CAACZ,UAAU,iBAC3BN,OAAA;YACEsD,OAAO,EAAE,IAAI,CAACnC,iBAAkB;YAChCqB,KAAK,EAAE;cACLE,UAAU,EAAE,SAAS;cACrBI,KAAK,EAAE,OAAO;cACdH,MAAM,EAAE,MAAM;cACdF,OAAO,EAAE,SAAS;cAClBG,YAAY,EAAE,KAAK;cACnBW,MAAM,EAAE,SAAS;cACjBJ,QAAQ,EAAE;YACZ,CAAE;YAAAf,QAAA,EACH;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNlD,OAAA;UAAKwC,KAAK,EAAE;YAAEY,SAAS,EAAE;UAAM,CAAE;UAAAhB,QAAA,EAC9BC;QAAQ;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,OAAOd,QAAQ;EACjB;AACF;;AAEA;AACA;AACA;AACA,OAAO,SAASoB,eAAeA,CAC7BC,gBAAwC,EACxCC,iBAAyC,EACzCnC,OAA2B,EAC3BC,aAAqB,EACrB;EACA,MAAMmC,oBAAoB,GAAItD,KAAQ,IAAK;IACzC,oBACEL,OAAA,CAACG,oBAAoB;MACnBoB,OAAO,EAAEA,OAAQ;MACjBC,aAAa,EAAEA,aAAc;MAC7Ba,QAAQ,eAAErC,OAAA,CAAC0D,iBAAiB;QAAA,GAAKrD;MAAK;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAE;MAC3CnB,kBAAkB,EAAE,IAAK;MACzBrB,aAAa,EAAE,IAAK;MAAA0B,QAAA,eAEpBpC,OAAA,CAACyD,gBAAgB;QAAA,GAAKpD;MAAK;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAE3B,CAAC;EAEDS,oBAAoB,CAACC,WAAW,GAAG,eAAepC,aAAa,GAAG;EAClE,OAAOmC,oBAAoB;AAC7B;;AAEA;AACA;AACA;AACA,OAAO,SAASE,gBAAgBA,CAC9BC,YAAoC,EACpCJ,iBAAyC,EACzCnC,OAA2B,EACH;EAAAwC,EAAA;EACxB,OAAOpE,KAAK,CAACqE,OAAO,CAAC,MAAM;IACzB,IAAInE,YAAY,CAACyC,SAAS,CAACf,OAAO,CAAC,EAAE;MACnC,OAAOuC,YAAY;IACrB;IACA,OAAOJ,iBAAiB;EAC1B,CAAC,EAAE,CAACI,YAAY,EAAEJ,iBAAiB,EAAEnC,OAAO,CAAC,CAAC;AAChD;;AAEA;AACA;AACA;AAFAwC,EAAA,CAbgBF,gBAAgB;AAyBhC,OAAO,SAASI,oBAAoBA,CAAmB;EACrD1C,OAAO;EACP2C,YAAY,EAAEJ,YAAY;EAC1BK,iBAAiB,EAAET,iBAAiB;EACpCU,cAAc;EACdC,iBAAiB,GAAG,IAAI;EACxB7C,aAAa,GAAG;AACY,CAAC,EAAE;EAC/B,MAAMc,SAAS,GAAGzC,YAAY,CAACyC,SAAS,CAACf,OAAO,CAAC;EAEjD,IAAI,CAACe,SAAS,EAAE;IACd,oBAAOtC,OAAA,CAAC0D,iBAAiB;MAAA,GAAKU;IAAc;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAClD;EAEA,IAAImB,iBAAiB,EAAE;IACrB,oBACErE,OAAA,CAACG,oBAAoB;MACnBoB,OAAO,EAAEA,OAAQ;MACjBC,aAAa,EAAEA,aAAc;MAC7Ba,QAAQ,eAAErC,OAAA,CAAC0D,iBAAiB;QAAA,GAAKU;MAAc;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAE;MACpDnB,kBAAkB,EAAE,IAAK;MAAAK,QAAA,eAEzBpC,OAAA,CAAC8D,YAAY;QAAA,GAAKM;MAAc;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAE3B;EAEA,oBAAOlD,OAAA,CAAC8D,YAAY;IAAA,GAAKM;EAAc;IAAArB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;AAC7C;;AAEA;AACA;AACA;AAFAoB,EAAA,GA9BgBL,oBAAoB;AAuCpC,OAAO,SAASM,aAAaA,CAAC;EAAEhD,OAAO;EAAEa,QAAQ;EAAEC,QAAQ,GAAG;AAAyB,CAAC,EAAE;EACxF,MAAMC,SAAS,GAAGzC,YAAY,CAACyC,SAAS,CAACf,OAAO,CAAC;EACjD,OAAOe,SAAS,gBAAGtC,OAAA,CAAAE,SAAA;IAAAkC,QAAA,EAAGA;EAAQ,gBAAG,CAAC,gBAAGpC,OAAA,CAAAE,SAAA;IAAAkC,QAAA,EAAGC;EAAQ,gBAAG,CAAC;AACtD;;AAEA;AACA;AACA;AAFAmC,GAAA,GALgBD,aAAa;AAgB7B,OAAO,SAASE,sBAAsBA,CAAC;EAAEC,QAAQ;EAAErC;AAAsC,CAAC,EAAE;EAC1F;EACA,KAAK,MAAM;IAAEd,OAAO;IAAEI;EAAU,CAAC,IAAI+C,QAAQ,EAAE;IAC7C,IAAI7E,YAAY,CAACyC,SAAS,CAACf,OAAO,CAAC,EAAE;MACnC,oBAAOvB,OAAA,CAAAE,SAAA;QAAAkC,QAAA,EAAGT;MAAS,gBAAG,CAAC;IACzB;EACF;EAEA,oBAAO3B,OAAA,CAAAE,SAAA;IAAAkC,QAAA,EAAGC;EAAQ,gBAAG,CAAC;AACxB;AAACsC,GAAA,GATeF,sBAAsB;AAWtC,eAAetE,oBAAoB;AAAC,IAAAmE,EAAA,EAAAE,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}