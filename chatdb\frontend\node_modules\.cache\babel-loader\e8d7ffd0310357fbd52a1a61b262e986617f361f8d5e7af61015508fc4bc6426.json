{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\pages\\\\UXTestPage.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * UX优化测试页面 - 用于测试和演示新的UX组件\n */\n\nimport React, { useState } from 'react';\nimport { Card, Space, Divider, message, Typography, Row, Col } from 'antd';\nimport { SendOutlined, SettingOutlined, TestTubeOutlined, BugOutlined } from '@ant-design/icons';\n\n// 导入安全组件\nimport SafeEnhancedButton from '../components/SafeEnhancedButton';\nimport SafeEnhancedInput from '../components/SafeEnhancedInput';\nimport SafeResponsiveLayout from '../components/SafeResponsiveLayout';\nimport SafeErrorBoundary from '../components/SafeErrorBoundary';\nimport FeatureFlagPanel from '../components/FeatureFlagPanel';\n\n// 导入功能开关\nimport { useFeatureFlags, featureFlags } from '../utils/featureFlags';\nimport { errorMonitoring } from '../utils/errorMonitoring';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\n\n// 测试错误的组件\nconst ErrorTestComponent = () => {\n  _s();\n  const [shouldError, setShouldError] = useState(false);\n  if (shouldError) {\n    throw new Error('这是一个测试错误，用于验证错误边界功能');\n  }\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: \"\\u9519\\u8BEF\\u8FB9\\u754C\\u6D4B\\u8BD5\",\n    size: \"small\",\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"\\u70B9\\u51FB\\u4E0B\\u9762\\u7684\\u6309\\u94AE\\u6765\\u6D4B\\u8BD5\\u9519\\u8BEF\\u8FB9\\u754C\\u529F\\u80FD\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n      variant: \"danger\",\n      onClick: () => setShouldError(true),\n      icon: /*#__PURE__*/_jsxDEV(BugOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 15\n      }, this),\n      children: \"\\u89E6\\u53D1\\u9519\\u8BEF\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_s(ErrorTestComponent, \"JOLBUt+4LQHZVWO7L9+Ct9G7oAc=\");\n_c = ErrorTestComponent;\nconst UXTestPage = () => {\n  _s2();\n  const [showFeaturePanel, setShowFeaturePanel] = useState(false);\n  const [inputValue, setInputValue] = useState('');\n  const [loading, setLoading] = useState(false);\n  const flags = useFeatureFlags();\n  const handleSubmit = async () => {\n    if (!inputValue.trim()) {\n      message.warning('请输入一些内容');\n      return;\n    }\n    setLoading(true);\n    try {\n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      message.success('操作成功！');\n    } catch (error) {\n      message.error('操作失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleTestError = () => {\n    // 手动报告一个测试错误\n    errorMonitoring.reportError({\n      component: 'UXTestPage',\n      feature: 'useEnhancedButton',\n      error: {\n        message: '这是一个手动测试错误',\n        stack: 'Test stack trace'\n      }\n    });\n    message.info('测试错误已报告');\n  };\n\n  // 侧边栏内容\n  const sidebarContent = /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '16px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 4,\n      children: \"\\u529F\\u80FD\\u72B6\\u6001\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '16px'\n      },\n      children: Object.entries(flags).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          padding: '4px 0',\n          fontSize: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: key\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: value ? '#52c41a' : '#8c8c8c',\n            fontWeight: 'bold'\n          },\n          children: value ? '✓' : '✗'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this)]\n      }, key, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Title, {\n      level: 4,\n      children: \"\\u5FEB\\u901F\\u64CD\\u4F5C\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      style: {\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n        variant: \"primary\",\n        size: \"small\",\n        fullWidth: true,\n        onClick: () => featureFlags.enableAllUXFeatures(),\n        children: \"\\u542F\\u7528\\u6240\\u6709\\u529F\\u80FD\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n        variant: \"secondary\",\n        size: \"small\",\n        fullWidth: true,\n        onClick: () => featureFlags.disableAllUXFeatures(),\n        children: \"\\u7981\\u7528\\u6240\\u6709\\u529F\\u80FD\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n        variant: \"warning\",\n        size: \"small\",\n        fullWidth: true,\n        onClick: () => featureFlags.resetFlags(),\n        children: \"\\u91CD\\u7F6E\\u8BBE\\u7F6E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Title, {\n      level: 4,\n      children: \"\\u6D4B\\u8BD5\\u5DE5\\u5177\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      style: {\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n        variant: \"ghost\",\n        size: \"small\",\n        fullWidth: true,\n        onClick: handleTestError,\n        icon: /*#__PURE__*/_jsxDEV(TestTubeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 17\n        }, this),\n        children: \"\\u6D4B\\u8BD5\\u9519\\u8BEF\\u62A5\\u544A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n        variant: \"ghost\",\n        size: \"small\",\n        fullWidth: true,\n        onClick: () => errorMonitoring.resetAllComponentHealth(),\n        icon: /*#__PURE__*/_jsxDEV(BugOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 17\n        }, this),\n        children: \"\\u91CD\\u7F6E\\u5065\\u5EB7\\u72B6\\u6001\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n\n  // 头部内容\n  const headerContent = /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      width: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '12px',\n        color: 'white'\n      },\n      children: [/*#__PURE__*/_jsxDEV(TestTubeOutlined, {\n        style: {\n          fontSize: '20px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          fontSize: '18px',\n          fontWeight: '600'\n        },\n        children: \"UX\\u4F18\\u5316\\u6D4B\\u8BD5\\u9875\\u9762\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n      variant: \"ghost\",\n      size: \"small\",\n      icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 15\n      }, this),\n      onClick: () => setShowFeaturePanel(true),\n      style: {\n        color: 'white',\n        borderColor: 'rgba(255,255,255,0.3)'\n      },\n      children: \"\\u529F\\u80FD\\u5F00\\u5173\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(SafeErrorBoundary, {\n    level: \"page\",\n    showDetails: true,\n    children: /*#__PURE__*/_jsxDEV(SafeResponsiveLayout, {\n      sidebar: sidebarContent,\n      header: headerContent,\n      showSidebar: true,\n      collapsible: true,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          children: \"UX\\u7EC4\\u4EF6\\u6D4B\\u8BD5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          children: \"\\u8FD9\\u4E2A\\u9875\\u9762\\u7528\\u4E8E\\u6D4B\\u8BD5\\u548C\\u6F14\\u793A\\u65B0\\u7684UX\\u4F18\\u5316\\u7EC4\\u4EF6\\u3002\\u6240\\u6709\\u7EC4\\u4EF6\\u90FD\\u5177\\u6709\\u81EA\\u52A8\\u56DE\\u9000\\u673A\\u5236\\uFF0C \\u5F53\\u51FA\\u73B0\\u9519\\u8BEF\\u65F6\\u4F1A\\u81EA\\u52A8\\u5207\\u6362\\u5230\\u539F\\u59CB\\u7EC4\\u4EF6\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u589E\\u5F3A\\u6309\\u94AE\\u6D4B\\u8BD5\",\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(Space, {\n                wrap: true,\n                children: [/*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n                  variant: \"primary\",\n                  children: \"\\u4E3B\\u8981\\u6309\\u94AE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n                  variant: \"secondary\",\n                  children: \"\\u6B21\\u8981\\u6309\\u94AE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n                  variant: \"success\",\n                  children: \"\\u6210\\u529F\\u6309\\u94AE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n                  variant: \"warning\",\n                  children: \"\\u8B66\\u544A\\u6309\\u94AE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n                  variant: \"danger\",\n                  children: \"\\u5371\\u9669\\u6309\\u94AE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n                  variant: \"ghost\",\n                  children: \"\\u5E7D\\u7075\\u6309\\u94AE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Space, {\n                wrap: true,\n                children: [/*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n                  size: \"small\",\n                  children: \"\\u5C0F\\u6309\\u94AE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n                  size: \"medium\",\n                  children: \"\\u4E2D\\u6309\\u94AE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n                  size: \"large\",\n                  children: \"\\u5927\\u6309\\u94AE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedButton, {\n                variant: \"primary\",\n                loading: loading,\n                loadingText: \"\\u5904\\u7406\\u4E2D...\",\n                onClick: handleSubmit,\n                icon: /*#__PURE__*/_jsxDEV(SendOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 25\n                }, this),\n                elevated: true,\n                tooltip: \"\\u70B9\\u51FB\\u63D0\\u4EA4\\u8868\\u5355\",\n                children: \"\\u63D0\\u4EA4\\u8868\\u5355\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u589E\\u5F3A\\u8F93\\u5165\\u6846\\u6D4B\\u8BD5\",\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(SafeEnhancedInput, {\n                  label: \"\\u57FA\\u7840\\u8F93\\u5165\",\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5185\\u5BB9\",\n                  value: inputValue,\n                  onChange: e => setInputValue(e.target.value),\n                  helperText: \"\\u8FD9\\u662F\\u5E2E\\u52A9\\u6587\\u672C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedInput, {\n                  label: \"\\u5FC5\\u586B\\u8F93\\u5165\",\n                  placeholder: \"\\u5FC5\\u586B\\u5B57\\u6BB5\",\n                  required: true,\n                  variant: \"filled\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedInput, {\n                  label: \"\\u5BC6\\u7801\\u8F93\\u5165\",\n                  type: \"password\",\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\",\n                  showPasswordToggle: true,\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedInput, {\n                  label: \"\\u6587\\u672C\\u533A\\u57DF\",\n                  placeholder: \"\\u591A\\u884C\\u6587\\u672C\\u8F93\\u5165\",\n                  autoResize: true,\n                  maxLength: 200,\n                  showCount: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedInput, {\n                  label: \"\\u9519\\u8BEF\\u72B6\\u6001\",\n                  placeholder: \"\\u9519\\u8BEF\\u8F93\\u5165\",\n                  error: \"\\u8FD9\\u662F\\u9519\\u8BEF\\u4FE1\\u606F\",\n                  variant: \"standard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(SafeEnhancedInput, {\n                  label: \"\\u6210\\u529F\\u72B6\\u6001\",\n                  placeholder: \"\\u6210\\u529F\\u8F93\\u5165\",\n                  success: true,\n                  clearable: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(SafeErrorBoundary, {\n              level: \"component\",\n              showDetails: true,\n              children: /*#__PURE__*/_jsxDEV(ErrorTestComponent, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u5F53\\u524D\\u529F\\u80FD\\u72B6\\u6001\",\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                gutter: [16, 8],\n                children: Object.entries(flags).map(([key, value]) => /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 12,\n                  sm: 8,\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '8px',\n                      background: value ? '#f6ffed' : '#fff2f0',\n                      border: `1px solid ${value ? '#b7eb8f' : '#ffccc7'}`,\n                      borderRadius: '4px',\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '12px',\n                        fontWeight: 'bold',\n                        color: value ? '#52c41a' : '#ff4d4f'\n                      },\n                      children: value ? '✓' : '✗'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '10px',\n                        marginTop: '2px'\n                      },\n                      children: key.replace('use', '').replace('enable', '')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 23\n                  }, this)\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FeatureFlagPanel, {\n        visible: showFeaturePanel,\n        onClose: () => setShowFeaturePanel(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 200,\n    columnNumber: 5\n  }, this);\n};\n_s2(UXTestPage, \"yqRiTdYrHhkoTgAGm1zUggULidA=\", false, function () {\n  return [useFeatureFlags];\n});\n_c2 = UXTestPage;\nexport default UXTestPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"ErrorTestComponent\");\n$RefreshReg$(_c2, \"UXTestPage\");", "map": {"version": 3, "names": ["React", "useState", "Card", "Space", "Divider", "message", "Typography", "Row", "Col", "SendOutlined", "SettingOutlined", "TestTubeOutlined", "BugOutlined", "SafeEnhancedButton", "SafeEnhancedInput", "SafeResponsiveLayout", "SafeErrorBoundary", "FeatureFlagPanel", "useFeatureFlags", "featureFlags", "errorMonitoring", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "ErrorTestComponent", "_s", "shouldError", "setShouldError", "Error", "title", "size", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "icon", "_c", "UXTestPage", "_s2", "showFeaturePanel", "setShowFeaturePanel", "inputValue", "setInputValue", "loading", "setLoading", "flags", "handleSubmit", "trim", "warning", "Promise", "resolve", "setTimeout", "success", "error", "handleTestError", "reportError", "component", "feature", "stack", "info", "sidebarContent", "style", "padding", "level", "marginBottom", "Object", "entries", "map", "key", "value", "display", "justifyContent", "alignItems", "fontSize", "color", "fontWeight", "direction", "width", "fullWidth", "enableAllUXFeatures", "disableAllUXFeatures", "resetFlags", "resetAllComponentHealth", "headerContent", "gap", "borderColor", "showDetails", "sidebar", "header", "showSidebar", "collapsible", "gutter", "xs", "md", "wrap", "loadingText", "elevated", "tooltip", "label", "placeholder", "onChange", "e", "target", "helperText", "required", "type", "showPasswordToggle", "autoResize", "max<PERSON><PERSON><PERSON>", "showCount", "clearable", "sm", "background", "border", "borderRadius", "textAlign", "marginTop", "replace", "visible", "onClose", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/pages/UXTestPage.tsx"], "sourcesContent": ["/**\n * UX优化测试页面 - 用于测试和演示新的UX组件\n */\n\nimport React, { useState } from 'react';\nimport { Card, Space, Divider, message, Typography, Row, Col } from 'antd';\nimport { \n  SendOutlined, \n  DatabaseOutlined, \n  SettingOutlined,\n  TestTubeOutlined,\n  BugOutlined\n} from '@ant-design/icons';\n\n// 导入安全组件\nimport SafeEnhancedButton from '../components/SafeEnhancedButton';\nimport SafeEnhancedInput from '../components/SafeEnhancedInput';\nimport SafeResponsiveLayout from '../components/SafeResponsiveLayout';\nimport SafeErrorBoundary from '../components/SafeErrorBoundary';\nimport FeatureFlagPanel from '../components/FeatureFlagPanel';\n\n// 导入功能开关\nimport { useFeatureFlags, featureFlags } from '../utils/featureFlags';\nimport { errorMonitoring } from '../utils/errorMonitoring';\n\nconst { Title, Text, Paragraph } = Typography;\n\n// 测试错误的组件\nconst ErrorTestComponent: React.FC = () => {\n  const [shouldError, setShouldError] = useState(false);\n\n  if (shouldError) {\n    throw new Error('这是一个测试错误，用于验证错误边界功能');\n  }\n\n  return (\n    <Card title=\"错误边界测试\" size=\"small\">\n      <p>点击下面的按钮来测试错误边界功能：</p>\n      <SafeEnhancedButton\n        variant=\"danger\"\n        onClick={() => setShouldError(true)}\n        icon={<BugOutlined />}\n      >\n        触发错误\n      </SafeEnhancedButton>\n    </Card>\n  );\n};\n\nconst UXTestPage: React.FC = () => {\n  const [showFeaturePanel, setShowFeaturePanel] = useState(false);\n  const [inputValue, setInputValue] = useState('');\n  const [loading, setLoading] = useState(false);\n  const flags = useFeatureFlags();\n\n  const handleSubmit = async () => {\n    if (!inputValue.trim()) {\n      message.warning('请输入一些内容');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // 模拟API调用\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      message.success('操作成功！');\n    } catch (error) {\n      message.error('操作失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleTestError = () => {\n    // 手动报告一个测试错误\n    errorMonitoring.reportError({\n      component: 'UXTestPage',\n      feature: 'useEnhancedButton',\n      error: {\n        message: '这是一个手动测试错误',\n        stack: 'Test stack trace'\n      }\n    });\n    message.info('测试错误已报告');\n  };\n\n  // 侧边栏内容\n  const sidebarContent = (\n    <div style={{ padding: '16px' }}>\n      <Title level={4}>功能状态</Title>\n      <div style={{ marginBottom: '16px' }}>\n        {Object.entries(flags).map(([key, value]) => (\n          <div key={key} style={{ \n            display: 'flex', \n            justifyContent: 'space-between',\n            alignItems: 'center',\n            padding: '4px 0',\n            fontSize: '12px'\n          }}>\n            <Text>{key}</Text>\n            <span style={{ \n              color: value ? '#52c41a' : '#8c8c8c',\n              fontWeight: 'bold'\n            }}>\n              {value ? '✓' : '✗'}\n            </span>\n          </div>\n        ))}\n      </div>\n\n      <Divider />\n\n      <Title level={4}>快速操作</Title>\n      <Space direction=\"vertical\" style={{ width: '100%' }}>\n        <SafeEnhancedButton\n          variant=\"primary\"\n          size=\"small\"\n          fullWidth\n          onClick={() => featureFlags.enableAllUXFeatures()}\n        >\n          启用所有功能\n        </SafeEnhancedButton>\n        <SafeEnhancedButton\n          variant=\"secondary\"\n          size=\"small\"\n          fullWidth\n          onClick={() => featureFlags.disableAllUXFeatures()}\n        >\n          禁用所有功能\n        </SafeEnhancedButton>\n        <SafeEnhancedButton\n          variant=\"warning\"\n          size=\"small\"\n          fullWidth\n          onClick={() => featureFlags.resetFlags()}\n        >\n          重置设置\n        </SafeEnhancedButton>\n      </Space>\n\n      <Divider />\n\n      <Title level={4}>测试工具</Title>\n      <Space direction=\"vertical\" style={{ width: '100%' }}>\n        <SafeEnhancedButton\n          variant=\"ghost\"\n          size=\"small\"\n          fullWidth\n          onClick={handleTestError}\n          icon={<TestTubeOutlined />}\n        >\n          测试错误报告\n        </SafeEnhancedButton>\n        <SafeEnhancedButton\n          variant=\"ghost\"\n          size=\"small\"\n          fullWidth\n          onClick={() => errorMonitoring.resetAllComponentHealth()}\n          icon={<BugOutlined />}\n        >\n          重置健康状态\n        </SafeEnhancedButton>\n      </Space>\n    </div>\n  );\n\n  // 头部内容\n  const headerContent = (\n    <div style={{ \n      display: 'flex', \n      alignItems: 'center', \n      justifyContent: 'space-between',\n      width: '100%'\n    }}>\n      <div style={{ \n        display: 'flex', \n        alignItems: 'center', \n        gap: '12px',\n        color: 'white'\n      }}>\n        <TestTubeOutlined style={{ fontSize: '20px' }} />\n        <span style={{ fontSize: '18px', fontWeight: '600' }}>\n          UX优化测试页面\n        </span>\n      </div>\n      \n      <SafeEnhancedButton\n        variant=\"ghost\"\n        size=\"small\"\n        icon={<SettingOutlined />}\n        onClick={() => setShowFeaturePanel(true)}\n        style={{ color: 'white', borderColor: 'rgba(255,255,255,0.3)' }}\n      >\n        功能开关\n      </SafeEnhancedButton>\n    </div>\n  );\n\n  return (\n    <SafeErrorBoundary level=\"page\" showDetails={true}>\n      <SafeResponsiveLayout\n        sidebar={sidebarContent}\n        header={headerContent}\n        showSidebar={true}\n        collapsible={true}\n      >\n        <div style={{ padding: '24px' }}>\n          <Title level={2}>UX组件测试</Title>\n          <Paragraph>\n            这个页面用于测试和演示新的UX优化组件。所有组件都具有自动回退机制，\n            当出现错误时会自动切换到原始组件。\n          </Paragraph>\n\n          <Row gutter={[16, 16]}>\n            {/* 按钮测试 */}\n            <Col xs={24} md={12}>\n              <Card title=\"增强按钮测试\" size=\"small\">\n                <Space wrap>\n                  <SafeEnhancedButton variant=\"primary\">\n                    主要按钮\n                  </SafeEnhancedButton>\n                  <SafeEnhancedButton variant=\"secondary\">\n                    次要按钮\n                  </SafeEnhancedButton>\n                  <SafeEnhancedButton variant=\"success\">\n                    成功按钮\n                  </SafeEnhancedButton>\n                  <SafeEnhancedButton variant=\"warning\">\n                    警告按钮\n                  </SafeEnhancedButton>\n                  <SafeEnhancedButton variant=\"danger\">\n                    危险按钮\n                  </SafeEnhancedButton>\n                  <SafeEnhancedButton variant=\"ghost\">\n                    幽灵按钮\n                  </SafeEnhancedButton>\n                </Space>\n                \n                <Divider />\n                \n                <Space wrap>\n                  <SafeEnhancedButton size=\"small\">小按钮</SafeEnhancedButton>\n                  <SafeEnhancedButton size=\"medium\">中按钮</SafeEnhancedButton>\n                  <SafeEnhancedButton size=\"large\">大按钮</SafeEnhancedButton>\n                </Space>\n\n                <Divider />\n\n                <SafeEnhancedButton\n                  variant=\"primary\"\n                  loading={loading}\n                  loadingText=\"处理中...\"\n                  onClick={handleSubmit}\n                  icon={<SendOutlined />}\n                  elevated\n                  tooltip=\"点击提交表单\"\n                >\n                  提交表单\n                </SafeEnhancedButton>\n              </Card>\n            </Col>\n\n            {/* 输入框测试 */}\n            <Col xs={24} md={12}>\n              <Card title=\"增强输入框测试\" size=\"small\">\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <SafeEnhancedInput\n                    label=\"基础输入\"\n                    placeholder=\"请输入内容\"\n                    value={inputValue}\n                    onChange={(e) => setInputValue(e.target.value)}\n                    helperText=\"这是帮助文本\"\n                  />\n\n                  <SafeEnhancedInput\n                    label=\"必填输入\"\n                    placeholder=\"必填字段\"\n                    required\n                    variant=\"filled\"\n                  />\n\n                  <SafeEnhancedInput\n                    label=\"密码输入\"\n                    type=\"password\"\n                    placeholder=\"请输入密码\"\n                    showPasswordToggle\n                    variant=\"outlined\"\n                  />\n\n                  <SafeEnhancedInput\n                    label=\"文本区域\"\n                    placeholder=\"多行文本输入\"\n                    autoResize\n                    maxLength={200}\n                    showCount\n                  />\n\n                  <SafeEnhancedInput\n                    label=\"错误状态\"\n                    placeholder=\"错误输入\"\n                    error=\"这是错误信息\"\n                    variant=\"standard\"\n                  />\n\n                  <SafeEnhancedInput\n                    label=\"成功状态\"\n                    placeholder=\"成功输入\"\n                    success\n                    clearable\n                  />\n                </Space>\n              </Card>\n            </Col>\n\n            {/* 错误边界测试 */}\n            <Col xs={24}>\n              <SafeErrorBoundary level=\"component\" showDetails={true}>\n                <ErrorTestComponent />\n              </SafeErrorBoundary>\n            </Col>\n\n            {/* 功能状态显示 */}\n            <Col xs={24}>\n              <Card title=\"当前功能状态\" size=\"small\">\n                <Row gutter={[16, 8]}>\n                  {Object.entries(flags).map(([key, value]) => (\n                    <Col key={key} xs={12} sm={8} md={6}>\n                      <div style={{\n                        padding: '8px',\n                        background: value ? '#f6ffed' : '#fff2f0',\n                        border: `1px solid ${value ? '#b7eb8f' : '#ffccc7'}`,\n                        borderRadius: '4px',\n                        textAlign: 'center'\n                      }}>\n                        <div style={{ \n                          fontSize: '12px', \n                          fontWeight: 'bold',\n                          color: value ? '#52c41a' : '#ff4d4f'\n                        }}>\n                          {value ? '✓' : '✗'}\n                        </div>\n                        <div style={{ fontSize: '10px', marginTop: '2px' }}>\n                          {key.replace('use', '').replace('enable', '')}\n                        </div>\n                      </div>\n                    </Col>\n                  ))}\n                </Row>\n              </Card>\n            </Col>\n          </Row>\n        </div>\n\n        <FeatureFlagPanel\n          visible={showFeaturePanel}\n          onClose={() => setShowFeaturePanel(false)}\n        />\n      </SafeResponsiveLayout>\n    </SafeErrorBoundary>\n  );\n};\n\nexport default UXTestPage;\n"], "mappings": ";;;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEC,GAAG,EAAEC,GAAG,QAAQ,MAAM;AAC1E,SACEC,YAAY,EAEZC,eAAe,EACfC,gBAAgB,EAChBC,WAAW,QACN,mBAAmB;;AAE1B;AACA,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,oBAAoB,MAAM,oCAAoC;AACrE,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,gBAAgB,MAAM,gCAAgC;;AAE7D;AACA,SAASC,eAAe,EAAEC,YAAY,QAAQ,uBAAuB;AACrE,SAASC,eAAe,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGnB,UAAU;;AAE7C;AACA,MAAMoB,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAErD,IAAI2B,WAAW,EAAE;IACf,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;EACxC;EAEA,oBACER,OAAA,CAACpB,IAAI;IAAC6B,KAAK,EAAC,sCAAQ;IAACC,IAAI,EAAC,OAAO;IAAAC,QAAA,gBAC/BX,OAAA;MAAAW,QAAA,EAAG;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACxBf,OAAA,CAACT,kBAAkB;MACjByB,OAAO,EAAC,QAAQ;MAChBC,OAAO,EAAEA,CAAA,KAAMV,cAAc,CAAC,IAAI,CAAE;MACpCW,IAAI,eAAElB,OAAA,CAACV,WAAW;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAAAJ,QAAA,EACvB;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAoB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CAAC;AAEX,CAAC;AAACV,EAAA,CAnBID,kBAA4B;AAAAe,EAAA,GAA5Bf,kBAA4B;AAqBlC,MAAMgB,UAAoB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACjC,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMiD,KAAK,GAAGhC,eAAe,CAAC,CAAC;EAE/B,MAAMiC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACL,UAAU,CAACM,IAAI,CAAC,CAAC,EAAE;MACtB/C,OAAO,CAACgD,OAAO,CAAC,SAAS,CAAC;MAC1B;IACF;IAEAJ,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAM,IAAIK,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACvDlD,OAAO,CAACoD,OAAO,CAAC,OAAO,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACAvC,eAAe,CAACwC,WAAW,CAAC;MAC1BC,SAAS,EAAE,YAAY;MACvBC,OAAO,EAAE,mBAAmB;MAC5BJ,KAAK,EAAE;QACLrD,OAAO,EAAE,YAAY;QACrB0D,KAAK,EAAE;MACT;IACF,CAAC,CAAC;IACF1D,OAAO,CAAC2D,IAAI,CAAC,SAAS,CAAC;EACzB,CAAC;;EAED;EACA,MAAMC,cAAc,gBAClB3C,OAAA;IAAK4C,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAlC,QAAA,gBAC9BX,OAAA,CAACC,KAAK;MAAC6C,KAAK,EAAE,CAAE;MAAAnC,QAAA,EAAC;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAC7Bf,OAAA;MAAK4C,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAO,CAAE;MAAApC,QAAA,EAClCqC,MAAM,CAACC,OAAO,CAACrB,KAAK,CAAC,CAACsB,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,kBACtCpD,OAAA;QAAe4C,KAAK,EAAE;UACpBS,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBV,OAAO,EAAE,OAAO;UAChBW,QAAQ,EAAE;QACZ,CAAE;QAAA7C,QAAA,gBACAX,OAAA,CAACE,IAAI;UAAAS,QAAA,EAAEwC;QAAG;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClBf,OAAA;UAAM4C,KAAK,EAAE;YACXa,KAAK,EAAEL,KAAK,GAAG,SAAS,GAAG,SAAS;YACpCM,UAAU,EAAE;UACd,CAAE;UAAA/C,QAAA,EACCyC,KAAK,GAAG,GAAG,GAAG;QAAG;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA,GAbCoC,GAAG;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcR,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENf,OAAA,CAAClB,OAAO;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEXf,OAAA,CAACC,KAAK;MAAC6C,KAAK,EAAE,CAAE;MAAAnC,QAAA,EAAC;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAC7Bf,OAAA,CAACnB,KAAK;MAAC8E,SAAS,EAAC,UAAU;MAACf,KAAK,EAAE;QAAEgB,KAAK,EAAE;MAAO,CAAE;MAAAjD,QAAA,gBACnDX,OAAA,CAACT,kBAAkB;QACjByB,OAAO,EAAC,SAAS;QACjBN,IAAI,EAAC,OAAO;QACZmD,SAAS;QACT5C,OAAO,EAAEA,CAAA,KAAMpB,YAAY,CAACiE,mBAAmB,CAAC,CAAE;QAAAnD,QAAA,EACnD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAAC,eACrBf,OAAA,CAACT,kBAAkB;QACjByB,OAAO,EAAC,WAAW;QACnBN,IAAI,EAAC,OAAO;QACZmD,SAAS;QACT5C,OAAO,EAAEA,CAAA,KAAMpB,YAAY,CAACkE,oBAAoB,CAAC,CAAE;QAAApD,QAAA,EACpD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAAC,eACrBf,OAAA,CAACT,kBAAkB;QACjByB,OAAO,EAAC,SAAS;QACjBN,IAAI,EAAC,OAAO;QACZmD,SAAS;QACT5C,OAAO,EAAEA,CAAA,KAAMpB,YAAY,CAACmE,UAAU,CAAC,CAAE;QAAArD,QAAA,EAC1C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAERf,OAAA,CAAClB,OAAO;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEXf,OAAA,CAACC,KAAK;MAAC6C,KAAK,EAAE,CAAE;MAAAnC,QAAA,EAAC;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAC7Bf,OAAA,CAACnB,KAAK;MAAC8E,SAAS,EAAC,UAAU;MAACf,KAAK,EAAE;QAAEgB,KAAK,EAAE;MAAO,CAAE;MAAAjD,QAAA,gBACnDX,OAAA,CAACT,kBAAkB;QACjByB,OAAO,EAAC,OAAO;QACfN,IAAI,EAAC,OAAO;QACZmD,SAAS;QACT5C,OAAO,EAAEoB,eAAgB;QACzBnB,IAAI,eAAElB,OAAA,CAACX,gBAAgB;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAC5B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAAC,eACrBf,OAAA,CAACT,kBAAkB;QACjByB,OAAO,EAAC,OAAO;QACfN,IAAI,EAAC,OAAO;QACZmD,SAAS;QACT5C,OAAO,EAAEA,CAAA,KAAMnB,eAAe,CAACmE,uBAAuB,CAAC,CAAE;QACzD/C,IAAI,eAAElB,OAAA,CAACV,WAAW;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EACvB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;;EAED;EACA,MAAMmD,aAAa,gBACjBlE,OAAA;IAAK4C,KAAK,EAAE;MACVS,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBD,cAAc,EAAE,eAAe;MAC/BM,KAAK,EAAE;IACT,CAAE;IAAAjD,QAAA,gBACAX,OAAA;MAAK4C,KAAK,EAAE;QACVS,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBY,GAAG,EAAE,MAAM;QACXV,KAAK,EAAE;MACT,CAAE;MAAA9C,QAAA,gBACAX,OAAA,CAACX,gBAAgB;QAACuD,KAAK,EAAE;UAAEY,QAAQ,EAAE;QAAO;MAAE;QAAA5C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjDf,OAAA;QAAM4C,KAAK,EAAE;UAAEY,QAAQ,EAAE,MAAM;UAAEE,UAAU,EAAE;QAAM,CAAE;QAAA/C,QAAA,EAAC;MAEtD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENf,OAAA,CAACT,kBAAkB;MACjByB,OAAO,EAAC,OAAO;MACfN,IAAI,EAAC,OAAO;MACZQ,IAAI,eAAElB,OAAA,CAACZ,eAAe;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC1BE,OAAO,EAAEA,CAAA,KAAMM,mBAAmB,CAAC,IAAI,CAAE;MACzCqB,KAAK,EAAE;QAAEa,KAAK,EAAE,OAAO;QAAEW,WAAW,EAAE;MAAwB,CAAE;MAAAzD,QAAA,EACjE;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAoB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CACN;EAED,oBACEf,OAAA,CAACN,iBAAiB;IAACoD,KAAK,EAAC,MAAM;IAACuB,WAAW,EAAE,IAAK;IAAA1D,QAAA,eAChDX,OAAA,CAACP,oBAAoB;MACnB6E,OAAO,EAAE3B,cAAe;MACxB4B,MAAM,EAAEL,aAAc;MACtBM,WAAW,EAAE,IAAK;MAClBC,WAAW,EAAE,IAAK;MAAA9D,QAAA,gBAElBX,OAAA;QAAK4C,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAlC,QAAA,gBAC9BX,OAAA,CAACC,KAAK;UAAC6C,KAAK,EAAE,CAAE;UAAAnC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/Bf,OAAA,CAACG,SAAS;UAAAQ,QAAA,EAAC;QAGX;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAEZf,OAAA,CAACf,GAAG;UAACyF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAA/D,QAAA,gBAEpBX,OAAA,CAACd,GAAG;YAACyF,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAjE,QAAA,eAClBX,OAAA,CAACpB,IAAI;cAAC6B,KAAK,EAAC,sCAAQ;cAACC,IAAI,EAAC,OAAO;cAAAC,QAAA,gBAC/BX,OAAA,CAACnB,KAAK;gBAACgG,IAAI;gBAAAlE,QAAA,gBACTX,OAAA,CAACT,kBAAkB;kBAACyB,OAAO,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAEtC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eACrBf,OAAA,CAACT,kBAAkB;kBAACyB,OAAO,EAAC,WAAW;kBAAAL,QAAA,EAAC;gBAExC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eACrBf,OAAA,CAACT,kBAAkB;kBAACyB,OAAO,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAEtC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eACrBf,OAAA,CAACT,kBAAkB;kBAACyB,OAAO,EAAC,SAAS;kBAAAL,QAAA,EAAC;gBAEtC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eACrBf,OAAA,CAACT,kBAAkB;kBAACyB,OAAO,EAAC,QAAQ;kBAAAL,QAAA,EAAC;gBAErC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eACrBf,OAAA,CAACT,kBAAkB;kBAACyB,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAEpC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eAERf,OAAA,CAAClB,OAAO;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEXf,OAAA,CAACnB,KAAK;gBAACgG,IAAI;gBAAAlE,QAAA,gBACTX,OAAA,CAACT,kBAAkB;kBAACmB,IAAI,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eACzDf,OAAA,CAACT,kBAAkB;kBAACmB,IAAI,EAAC,QAAQ;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eAC1Df,OAAA,CAACT,kBAAkB;kBAACmB,IAAI,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eAERf,OAAA,CAAClB,OAAO;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEXf,OAAA,CAACT,kBAAkB;gBACjByB,OAAO,EAAC,SAAS;gBACjBU,OAAO,EAAEA,OAAQ;gBACjBoD,WAAW,EAAC,uBAAQ;gBACpB7D,OAAO,EAAEY,YAAa;gBACtBX,IAAI,eAAElB,OAAA,CAACb,YAAY;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBgE,QAAQ;gBACRC,OAAO,EAAC,sCAAQ;gBAAArE,QAAA,EACjB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAoB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNf,OAAA,CAACd,GAAG;YAACyF,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAjE,QAAA,eAClBX,OAAA,CAACpB,IAAI;cAAC6B,KAAK,EAAC,4CAAS;cAACC,IAAI,EAAC,OAAO;cAAAC,QAAA,eAChCX,OAAA,CAACnB,KAAK;gBAAC8E,SAAS,EAAC,UAAU;gBAACf,KAAK,EAAE;kBAAEgB,KAAK,EAAE;gBAAO,CAAE;gBAAAjD,QAAA,gBACnDX,OAAA,CAACR,iBAAiB;kBAChByF,KAAK,EAAC,0BAAM;kBACZC,WAAW,EAAC,gCAAO;kBACnB9B,KAAK,EAAE5B,UAAW;kBAClB2D,QAAQ,EAAGC,CAAC,IAAK3D,aAAa,CAAC2D,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE;kBAC/CkC,UAAU,EAAC;gBAAQ;kBAAA1E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eAEFf,OAAA,CAACR,iBAAiB;kBAChByF,KAAK,EAAC,0BAAM;kBACZC,WAAW,EAAC,0BAAM;kBAClBK,QAAQ;kBACRvE,OAAO,EAAC;gBAAQ;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eAEFf,OAAA,CAACR,iBAAiB;kBAChByF,KAAK,EAAC,0BAAM;kBACZO,IAAI,EAAC,UAAU;kBACfN,WAAW,EAAC,gCAAO;kBACnBO,kBAAkB;kBAClBzE,OAAO,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eAEFf,OAAA,CAACR,iBAAiB;kBAChByF,KAAK,EAAC,0BAAM;kBACZC,WAAW,EAAC,sCAAQ;kBACpBQ,UAAU;kBACVC,SAAS,EAAE,GAAI;kBACfC,SAAS;gBAAA;kBAAAhF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAEFf,OAAA,CAACR,iBAAiB;kBAChByF,KAAK,EAAC,0BAAM;kBACZC,WAAW,EAAC,0BAAM;kBAClB9C,KAAK,EAAC,sCAAQ;kBACdpB,OAAO,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eAEFf,OAAA,CAACR,iBAAiB;kBAChByF,KAAK,EAAC,0BAAM;kBACZC,WAAW,EAAC,0BAAM;kBAClB/C,OAAO;kBACP0D,SAAS;gBAAA;kBAAAjF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNf,OAAA,CAACd,GAAG;YAACyF,EAAE,EAAE,EAAG;YAAAhE,QAAA,eACVX,OAAA,CAACN,iBAAiB;cAACoD,KAAK,EAAC,WAAW;cAACuB,WAAW,EAAE,IAAK;cAAA1D,QAAA,eACrDX,OAAA,CAACI,kBAAkB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eAGNf,OAAA,CAACd,GAAG;YAACyF,EAAE,EAAE,EAAG;YAAAhE,QAAA,eACVX,OAAA,CAACpB,IAAI;cAAC6B,KAAK,EAAC,sCAAQ;cAACC,IAAI,EAAC,OAAO;cAAAC,QAAA,eAC/BX,OAAA,CAACf,GAAG;gBAACyF,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAE;gBAAA/D,QAAA,EAClBqC,MAAM,CAACC,OAAO,CAACrB,KAAK,CAAC,CAACsB,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,kBACtCpD,OAAA,CAACd,GAAG;kBAAWyF,EAAE,EAAE,EAAG;kBAACmB,EAAE,EAAE,CAAE;kBAAClB,EAAE,EAAE,CAAE;kBAAAjE,QAAA,eAClCX,OAAA;oBAAK4C,KAAK,EAAE;sBACVC,OAAO,EAAE,KAAK;sBACdkD,UAAU,EAAE3C,KAAK,GAAG,SAAS,GAAG,SAAS;sBACzC4C,MAAM,EAAE,aAAa5C,KAAK,GAAG,SAAS,GAAG,SAAS,EAAE;sBACpD6C,YAAY,EAAE,KAAK;sBACnBC,SAAS,EAAE;oBACb,CAAE;oBAAAvF,QAAA,gBACAX,OAAA;sBAAK4C,KAAK,EAAE;wBACVY,QAAQ,EAAE,MAAM;wBAChBE,UAAU,EAAE,MAAM;wBAClBD,KAAK,EAAEL,KAAK,GAAG,SAAS,GAAG;sBAC7B,CAAE;sBAAAzC,QAAA,EACCyC,KAAK,GAAG,GAAG,GAAG;oBAAG;sBAAAxC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACNf,OAAA;sBAAK4C,KAAK,EAAE;wBAAEY,QAAQ,EAAE,MAAM;wBAAE2C,SAAS,EAAE;sBAAM,CAAE;sBAAAxF,QAAA,EAChDwC,GAAG,CAACiD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAE,EAAE;oBAAC;sBAAAxF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAlBEoC,GAAG;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmBR,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENf,OAAA,CAACL,gBAAgB;QACf0G,OAAO,EAAE/E,gBAAiB;QAC1BgF,OAAO,EAAEA,CAAA,KAAM/E,mBAAmB,CAAC,KAAK;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACkB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAExB,CAAC;AAACM,GAAA,CAvTID,UAAoB;EAAA,QAIVxB,eAAe;AAAA;AAAA2G,GAAA,GAJzBnF,UAAoB;AAyT1B,eAAeA,UAAU;AAAC,IAAAD,EAAA,EAAAoF,GAAA;AAAAC,YAAA,CAAArF,EAAA;AAAAqF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}