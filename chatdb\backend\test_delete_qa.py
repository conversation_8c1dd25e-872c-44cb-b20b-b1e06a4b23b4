#!/usr/bin/env python3
"""
测试问答对删除功能
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.hybrid_retrieval_service import HybridRetrievalEngine

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_delete_qa():
    """测试删除问答对功能"""
    
    logger.info("🧪 开始测试问答对删除功能...")
    
    try:
        # 创建混合检索引擎
        engine = HybridRetrievalEngine()
        
        # 初始化引擎
        logger.info("📡 初始化混合检索引擎...")
        await engine.initialize()
        
        if not engine._initialized:
            logger.error("❌ 混合检索引擎初始化失败")
            return False
        
        logger.info("✅ 混合检索引擎初始化成功")
        
        # 首先查看现有的问答对
        logger.info("📋 查看现有问答对...")
        from neo4j import GraphDatabase
        from app.core.config import settings
        
        driver = GraphDatabase.driver(
            settings.NEO4J_URI, 
            auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
        )
        
        with driver.session() as session:
            result = session.run("""
                MATCH (qa:QAPair)
                RETURN qa.id as id, qa.question as question
                ORDER BY qa.created_at DESC
                LIMIT 5
            """)
            
            qa_pairs = list(result)
            logger.info(f"   找到 {len(qa_pairs)} 个问答对:")
            
            for i, record in enumerate(qa_pairs):
                logger.info(f"   {i+1}. ID: {record['id']}")
                logger.info(f"      问题: {record['question'][:50]}...")
        
        driver.close()
        
        if not qa_pairs:
            logger.warning("⚠️ 没有找到问答对，无法测试删除功能")
            return True
        
        # 选择第一个问答对进行删除测试
        test_qa_id = qa_pairs[0]['id']
        test_question = qa_pairs[0]['question']
        
        logger.info(f"🎯 准备删除测试问答对:")
        logger.info(f"   ID: {test_qa_id}")
        logger.info(f"   问题: {test_question[:50]}...")
        
        # 执行删除
        logger.info("🗑️ 执行删除操作...")
        success = await engine.delete_qa_pair(test_qa_id)
        
        if success:
            logger.info("✅ 删除操作成功")
            
            # 验证删除结果
            logger.info("🔍 验证删除结果...")
            
            # 检查Neo4j
            driver = GraphDatabase.driver(
                settings.NEO4J_URI, 
                auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
            )
            
            with driver.session() as session:
                result = session.run("""
                    MATCH (qa:QAPair {id: $qa_id})
                    RETURN count(qa) as count
                """, qa_id=test_qa_id)
                
                record = result.single()
                neo4j_count = record["count"] if record else 0
                
                if neo4j_count == 0:
                    logger.info("   ✅ Neo4j中已成功删除")
                else:
                    logger.error("   ❌ Neo4j中仍存在记录")
            
            driver.close()
            
            # 检查Milvus
            try:
                from pymilvus import Collection
                collection = Collection("qa_pairs")
                collection.load()
                
                results = collection.query(
                    expr=f'id == "{test_qa_id}"',
                    output_fields=["id"],
                    limit=1
                )
                
                if len(results) == 0:
                    logger.info("   ✅ Milvus中已成功删除")
                else:
                    logger.error("   ❌ Milvus中仍存在记录")
                    
            except Exception as e:
                logger.error(f"   ❌ 检查Milvus失败: {str(e)}")
            
            logger.info("🎉 删除功能测试完成!")
            return True
            
        else:
            logger.error("❌ 删除操作失败")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return False

async def main():
    logger.info("🚀 开始测试问答对删除功能...")
    
    success = await test_delete_qa()
    
    if success:
        logger.info("✅ 删除功能测试通过")
    else:
        logger.error("❌ 删除功能测试失败")

if __name__ == "__main__":
    asyncio.run(main())
