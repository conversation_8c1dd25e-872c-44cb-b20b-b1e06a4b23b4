{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\components\\\\SafeResponsiveLayout.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * 安全的响应式布局组件 - 带有自动回退机制\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Layout, Drawer, Button } from 'antd';\nimport { MenuOutlined } from '@ant-design/icons';\nimport { withSafeWrapper } from './SafeComponentWrapper';\nimport ResponsiveLayout from './ResponsiveLayout';\nimport { useFeatureFlag } from '../utils/featureFlags';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Content,\n  Sider\n} = Layout;\n// 原始布局组件（回退组件）\nconst FallbackLayout = ({\n  children,\n  sidebar,\n  header,\n  showSidebar = true,\n  sidebarWidth = 260,\n  collapsible = true\n}) => {\n  _s();\n  const [isMobile, setIsMobile] = useState(false);\n  const [sidebarVisible, setSidebarVisible] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  useEffect(() => {\n    const checkMobile = () => {\n      const mobile = window.innerWidth <= 768;\n      setIsMobile(mobile);\n      if (!mobile) {\n        setSidebarVisible(false);\n      }\n    };\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n  const toggleSidebar = () => {\n    if (isMobile) {\n      setSidebarVisible(!sidebarVisible);\n    } else {\n      setCollapsed(!collapsed);\n    }\n  };\n\n  // 移动端布局\n  if (isMobile) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      style: {\n        minHeight: '100vh'\n      },\n      children: [header && /*#__PURE__*/_jsxDEV(Header, {\n        style: {\n          padding: '0 16px',\n          display: 'flex',\n          alignItems: 'center',\n          background: '#001529'\n        },\n        children: [showSidebar && sidebar && /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(MenuOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 23\n          }, this),\n          onClick: toggleSidebar,\n          style: {\n            color: 'white',\n            marginRight: '16px',\n            padding: '4px 8px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1\n          },\n          children: header\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          padding: '16px',\n          flex: 1,\n          overflow: 'auto'\n        },\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), showSidebar && sidebar && /*#__PURE__*/_jsxDEV(Drawer, {\n        title: \"\\u83DC\\u5355\",\n        placement: \"left\",\n        onClose: () => setSidebarVisible(false),\n        open: sidebarVisible,\n        width: 280,\n        bodyStyle: {\n          padding: 0\n        },\n        children: sidebar\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 桌面端布局\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    style: {\n      minHeight: '100vh'\n    },\n    children: [showSidebar && sidebar && /*#__PURE__*/_jsxDEV(Sider, {\n      width: sidebarWidth,\n      collapsed: collapsed,\n      collapsible: collapsible,\n      onCollapse: setCollapsed,\n      theme: \"light\",\n      style: {\n        background: '#f9f9f9',\n        borderRight: '1px solid #f0f0f0'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: '100%',\n          overflow: 'auto',\n          padding: '16px 0'\n        },\n        children: sidebar\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {\n      children: [header && /*#__PURE__*/_jsxDEV(Header, {\n        style: {\n          padding: '0 24px',\n          background: '#001529',\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: header\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          padding: '24px',\n          overflow: 'auto',\n          background: '#ffffff'\n        },\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n};\n\n// 响应式布局组件适配器\n_s(FallbackLayout, \"w8ixnGcDcg3jQJugfT/3RdM7rDk=\");\n_c = FallbackLayout;\nconst ResponsiveLayoutAdapter = props => {\n  try {\n    return /*#__PURE__*/_jsxDEV(ResponsiveLayout, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 12\n    }, this);\n  } catch (error) {\n    console.warn('ResponsiveLayout failed, falling back to standard layout:', error);\n    return /*#__PURE__*/_jsxDEV(FallbackLayout, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 12\n    }, this);\n  }\n};\n\n// 使用安全包装器包装响应式布局\n_c2 = ResponsiveLayoutAdapter;\nconst SafeResponsiveLayoutWithWrapper = withSafeWrapper(ResponsiveLayoutAdapter, FallbackLayout, 'useResponsiveLayout', 'ResponsiveLayout');\n\n// 主要导出组件 - 根据功能开关选择组件\n_c3 = SafeResponsiveLayoutWithWrapper;\nconst SafeResponsiveLayout = props => {\n  _s2();\n  const isResponsiveEnabled = useFeatureFlag('useResponsiveLayout');\n  if (!isResponsiveEnabled) {\n    return /*#__PURE__*/_jsxDEV(FallbackLayout, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(SafeResponsiveLayoutWithWrapper, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 186,\n    columnNumber: 10\n  }, this);\n};\n\n// 添加CSS样式以支持回退布局\n_s2(SafeResponsiveLayout, \"styXkWVKVBbe2R/KkXxntcGqi4k=\", false, function () {\n  return [useFeatureFlag];\n});\n_c4 = SafeResponsiveLayout;\nconst layoutStyles = `\n/* 回退布局样式 */\n.ant-layout {\n  transition: all 0.3s ease;\n}\n\n.ant-layout-sider {\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);\n}\n\n.ant-layout-sider-collapsed {\n  box-shadow: 1px 0 4px rgba(0, 0, 0, 0.05);\n}\n\n.ant-layout-header {\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  z-index: 10;\n}\n\n.ant-layout-content {\n  min-height: 0;\n}\n\n/* 移动端优化 */\n@media (max-width: 768px) {\n  .ant-layout-header {\n    padding: 0 12px !important;\n  }\n  \n  .ant-layout-content {\n    padding: 12px !important;\n  }\n  \n  .ant-drawer-body {\n    padding: 0;\n  }\n}\n\n/* 平板端优化 */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .ant-layout-sider {\n    width: 240px !important;\n    min-width: 240px !important;\n    max-width: 240px !important;\n  }\n}\n\n/* 确保内容区域正确滚动 */\n.ant-layout-content {\n  overflow: auto;\n  flex: 1;\n}\n\n/* 侧边栏内容滚动 */\n.ant-layout-sider .ant-layout-sider-children {\n  overflow: auto;\n  height: 100%;\n}\n\n/* 抽屉样式优化 */\n.ant-drawer-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.ant-drawer-title {\n  color: white;\n}\n\n.ant-drawer-close {\n  color: white;\n}\n\n.ant-drawer-close:hover {\n  background: rgba(255, 255, 255, 0.1);\n}\n`;\n\n// 动态注入样式\nif (typeof document !== 'undefined') {\n  const styleId = 'safe-responsive-layout-styles';\n  if (!document.getElementById(styleId)) {\n    const style = document.createElement('style');\n    style.id = styleId;\n    style.textContent = layoutStyles;\n    document.head.appendChild(style);\n  }\n}\nexport default SafeResponsiveLayout;\n\n// 导出类型\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"FallbackLayout\");\n$RefreshReg$(_c2, \"ResponsiveLayoutAdapter\");\n$RefreshReg$(_c3, \"SafeResponsiveLayoutWithWrapper\");\n$RefreshReg$(_c4, \"SafeResponsiveLayout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "Drawer", "<PERSON><PERSON>", "MenuOutlined", "withSafeWrapper", "ResponsiveLayout", "useFeatureFlag", "jsxDEV", "_jsxDEV", "Header", "Content", "<PERSON><PERSON>", "FallbackLayout", "children", "sidebar", "header", "showSidebar", "sidebarWidth", "collapsible", "_s", "isMobile", "setIsMobile", "sidebarVisible", "setSidebarVisible", "collapsed", "setCollapsed", "checkMobile", "mobile", "window", "innerWidth", "addEventListener", "removeEventListener", "toggleSidebar", "style", "minHeight", "padding", "display", "alignItems", "background", "type", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "color", "marginRight", "flex", "overflow", "title", "placement", "onClose", "open", "width", "bodyStyle", "onCollapse", "theme", "borderRight", "height", "_c", "ResponsiveLayoutAdapter", "props", "error", "console", "warn", "_c2", "SafeResponsiveLayoutWithWrapper", "_c3", "SafeResponsiveLayout", "_s2", "isResponsiveEnabled", "_c4", "layoutStyles", "document", "styleId", "getElementById", "createElement", "id", "textContent", "head", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/components/SafeResponsiveLayout.tsx"], "sourcesContent": ["/**\n * 安全的响应式布局组件 - 带有自动回退机制\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Layout, Drawer, Button } from 'antd';\nimport { MenuOutlined } from '@ant-design/icons';\nimport { withSafeWrapper } from './SafeComponentWrapper';\nimport ResponsiveLayout from './ResponsiveLayout';\nimport { useFeatureFlag } from '../utils/featureFlags';\n\nconst { Header, Content, Sider } = Layout;\n\ninterface SafeResponsiveLayoutProps {\n  children: React.ReactNode;\n  sidebar?: React.ReactNode;\n  header?: React.ReactNode;\n  showSidebar?: boolean;\n  sidebarWidth?: number;\n  collapsible?: boolean;\n}\n\n// 原始布局组件（回退组件）\nconst FallbackLayout: React.FC<SafeResponsiveLayoutProps> = ({\n  children,\n  sidebar,\n  header,\n  showSidebar = true,\n  sidebarWidth = 260,\n  collapsible = true\n}) => {\n  const [isMobile, setIsMobile] = useState(false);\n  const [sidebarVisible, setSidebarVisible] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n\n  useEffect(() => {\n    const checkMobile = () => {\n      const mobile = window.innerWidth <= 768;\n      setIsMobile(mobile);\n      if (!mobile) {\n        setSidebarVisible(false);\n      }\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  const toggleSidebar = () => {\n    if (isMobile) {\n      setSidebarVisible(!sidebarVisible);\n    } else {\n      setCollapsed(!collapsed);\n    }\n  };\n\n  // 移动端布局\n  if (isMobile) {\n    return (\n      <Layout style={{ minHeight: '100vh' }}>\n        {header && (\n          <Header style={{ \n            padding: '0 16px', \n            display: 'flex', \n            alignItems: 'center',\n            background: '#001529'\n          }}>\n            {showSidebar && sidebar && (\n              <Button\n                type=\"text\"\n                icon={<MenuOutlined />}\n                onClick={toggleSidebar}\n                style={{ \n                  color: 'white', \n                  marginRight: '16px',\n                  padding: '4px 8px'\n                }}\n              />\n            )}\n            <div style={{ flex: 1 }}>\n              {header}\n            </div>\n          </Header>\n        )}\n        \n        <Content style={{ \n          padding: '16px', \n          flex: 1,\n          overflow: 'auto'\n        }}>\n          {children}\n        </Content>\n\n        {showSidebar && sidebar && (\n          <Drawer\n            title=\"菜单\"\n            placement=\"left\"\n            onClose={() => setSidebarVisible(false)}\n            open={sidebarVisible}\n            width={280}\n            bodyStyle={{ padding: 0 }}\n          >\n            {sidebar}\n          </Drawer>\n        )}\n      </Layout>\n    );\n  }\n\n  // 桌面端布局\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      {showSidebar && sidebar && (\n        <Sider\n          width={sidebarWidth}\n          collapsed={collapsed}\n          collapsible={collapsible}\n          onCollapse={setCollapsed}\n          theme=\"light\"\n          style={{\n            background: '#f9f9f9',\n            borderRight: '1px solid #f0f0f0'\n          }}\n        >\n          <div style={{ \n            height: '100%', \n            overflow: 'auto',\n            padding: '16px 0'\n          }}>\n            {sidebar}\n          </div>\n        </Sider>\n      )}\n      \n      <Layout>\n        {header && (\n          <Header style={{ \n            padding: '0 24px',\n            background: '#001529',\n            display: 'flex',\n            alignItems: 'center'\n          }}>\n            {header}\n          </Header>\n        )}\n        \n        <Content style={{ \n          padding: '24px',\n          overflow: 'auto',\n          background: '#ffffff'\n        }}>\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\n// 响应式布局组件适配器\nconst ResponsiveLayoutAdapter: React.FC<SafeResponsiveLayoutProps> = (props) => {\n  try {\n    return <ResponsiveLayout {...props} />;\n  } catch (error) {\n    console.warn('ResponsiveLayout failed, falling back to standard layout:', error);\n    return <FallbackLayout {...props} />;\n  }\n};\n\n// 使用安全包装器包装响应式布局\nconst SafeResponsiveLayoutWithWrapper = withSafeWrapper(\n  ResponsiveLayoutAdapter,\n  FallbackLayout,\n  'useResponsiveLayout',\n  'ResponsiveLayout'\n);\n\n// 主要导出组件 - 根据功能开关选择组件\nconst SafeResponsiveLayout: React.FC<SafeResponsiveLayoutProps> = (props) => {\n  const isResponsiveEnabled = useFeatureFlag('useResponsiveLayout');\n  \n  if (!isResponsiveEnabled) {\n    return <FallbackLayout {...props} />;\n  }\n\n  return <SafeResponsiveLayoutWithWrapper {...props} />;\n};\n\n// 添加CSS样式以支持回退布局\nconst layoutStyles = `\n/* 回退布局样式 */\n.ant-layout {\n  transition: all 0.3s ease;\n}\n\n.ant-layout-sider {\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);\n}\n\n.ant-layout-sider-collapsed {\n  box-shadow: 1px 0 4px rgba(0, 0, 0, 0.05);\n}\n\n.ant-layout-header {\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  z-index: 10;\n}\n\n.ant-layout-content {\n  min-height: 0;\n}\n\n/* 移动端优化 */\n@media (max-width: 768px) {\n  .ant-layout-header {\n    padding: 0 12px !important;\n  }\n  \n  .ant-layout-content {\n    padding: 12px !important;\n  }\n  \n  .ant-drawer-body {\n    padding: 0;\n  }\n}\n\n/* 平板端优化 */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .ant-layout-sider {\n    width: 240px !important;\n    min-width: 240px !important;\n    max-width: 240px !important;\n  }\n}\n\n/* 确保内容区域正确滚动 */\n.ant-layout-content {\n  overflow: auto;\n  flex: 1;\n}\n\n/* 侧边栏内容滚动 */\n.ant-layout-sider .ant-layout-sider-children {\n  overflow: auto;\n  height: 100%;\n}\n\n/* 抽屉样式优化 */\n.ant-drawer-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.ant-drawer-title {\n  color: white;\n}\n\n.ant-drawer-close {\n  color: white;\n}\n\n.ant-drawer-close:hover {\n  background: rgba(255, 255, 255, 0.1);\n}\n`;\n\n// 动态注入样式\nif (typeof document !== 'undefined') {\n  const styleId = 'safe-responsive-layout-styles';\n  if (!document.getElementById(styleId)) {\n    const style = document.createElement('style');\n    style.id = styleId;\n    style.textContent = layoutStyles;\n    document.head.appendChild(style);\n  }\n}\n\nexport default SafeResponsiveLayout;\n\n// 导出类型\nexport type { SafeResponsiveLayoutProps };\n"], "mappings": ";;;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,MAAM,EAAEC,MAAM,QAAQ,MAAM;AAC7C,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,cAAc,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAM;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAM,CAAC,GAAGX,MAAM;AAWzC;AACA,MAAMY,cAAmD,GAAGA,CAAC;EAC3DC,QAAQ;EACRC,OAAO;EACPC,MAAM;EACNC,WAAW,GAAG,IAAI;EAClBC,YAAY,GAAG,GAAG;EAClBC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACd,MAAM2B,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAMC,MAAM,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG;MACvCR,WAAW,CAACM,MAAM,CAAC;MACnB,IAAI,CAACA,MAAM,EAAE;QACXJ,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC;IAEDG,WAAW,CAAC,CAAC;IACbE,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEJ,WAAW,CAAC;IAC9C,OAAO,MAAME,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEL,WAAW,CAAC;EAChE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIZ,QAAQ,EAAE;MACZG,iBAAiB,CAAC,CAACD,cAAc,CAAC;IACpC,CAAC,MAAM;MACLG,YAAY,CAAC,CAACD,SAAS,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,IAAIJ,QAAQ,EAAE;IACZ,oBACEZ,OAAA,CAACR,MAAM;MAACiC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAQ,CAAE;MAAArB,QAAA,GACnCE,MAAM,iBACLP,OAAA,CAACC,MAAM;QAACwB,KAAK,EAAE;UACbE,OAAO,EAAE,QAAQ;UACjBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAzB,QAAA,GACCG,WAAW,IAAIF,OAAO,iBACrBN,OAAA,CAACN,MAAM;UACLqC,IAAI,EAAC,MAAM;UACXC,IAAI,eAAEhC,OAAA,CAACL,YAAY;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBC,OAAO,EAAEb,aAAc;UACvBC,KAAK,EAAE;YACLa,KAAK,EAAE,OAAO;YACdC,WAAW,EAAE,MAAM;YACnBZ,OAAO,EAAE;UACX;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,eACDpC,OAAA;UAAKyB,KAAK,EAAE;YAAEe,IAAI,EAAE;UAAE,CAAE;UAAAnC,QAAA,EACrBE;QAAM;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACT,eAEDpC,OAAA,CAACE,OAAO;QAACuB,KAAK,EAAE;UACdE,OAAO,EAAE,MAAM;UACfa,IAAI,EAAE,CAAC;UACPC,QAAQ,EAAE;QACZ,CAAE;QAAApC,QAAA,EACCA;MAAQ;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAET5B,WAAW,IAAIF,OAAO,iBACrBN,OAAA,CAACP,MAAM;QACLiD,KAAK,EAAC,cAAI;QACVC,SAAS,EAAC,MAAM;QAChBC,OAAO,EAAEA,CAAA,KAAM7B,iBAAiB,CAAC,KAAK,CAAE;QACxC8B,IAAI,EAAE/B,cAAe;QACrBgC,KAAK,EAAE,GAAI;QACXC,SAAS,EAAE;UAAEpB,OAAO,EAAE;QAAE,CAAE;QAAAtB,QAAA,EAEzBC;MAAO;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAEb;;EAEA;EACA,oBACEpC,OAAA,CAACR,MAAM;IAACiC,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAArB,QAAA,GACnCG,WAAW,IAAIF,OAAO,iBACrBN,OAAA,CAACG,KAAK;MACJ2C,KAAK,EAAErC,YAAa;MACpBO,SAAS,EAAEA,SAAU;MACrBN,WAAW,EAAEA,WAAY;MACzBsC,UAAU,EAAE/B,YAAa;MACzBgC,KAAK,EAAC,OAAO;MACbxB,KAAK,EAAE;QACLK,UAAU,EAAE,SAAS;QACrBoB,WAAW,EAAE;MACf,CAAE;MAAA7C,QAAA,eAEFL,OAAA;QAAKyB,KAAK,EAAE;UACV0B,MAAM,EAAE,MAAM;UACdV,QAAQ,EAAE,MAAM;UAChBd,OAAO,EAAE;QACX,CAAE;QAAAtB,QAAA,EACCC;MAAO;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAEDpC,OAAA,CAACR,MAAM;MAAAa,QAAA,GACJE,MAAM,iBACLP,OAAA,CAACC,MAAM;QAACwB,KAAK,EAAE;UACbE,OAAO,EAAE,QAAQ;UACjBG,UAAU,EAAE,SAAS;UACrBF,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAE;QAAAxB,QAAA,EACCE;MAAM;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACT,eAEDpC,OAAA,CAACE,OAAO;QAACuB,KAAK,EAAE;UACdE,OAAO,EAAE,MAAM;UACfc,QAAQ,EAAE,MAAM;UAChBX,UAAU,EAAE;QACd,CAAE;QAAAzB,QAAA,EACCA;MAAQ;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;;AAED;AAAAzB,EAAA,CAxIMP,cAAmD;AAAAgD,EAAA,GAAnDhD,cAAmD;AAyIzD,MAAMiD,uBAA4D,GAAIC,KAAK,IAAK;EAC9E,IAAI;IACF,oBAAOtD,OAAA,CAACH,gBAAgB;MAAA,GAAKyD;IAAK;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EACxC,CAAC,CAAC,OAAOmB,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,2DAA2D,EAAEF,KAAK,CAAC;IAChF,oBAAOvD,OAAA,CAACI,cAAc;MAAA,GAAKkD;IAAK;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EACtC;AACF,CAAC;;AAED;AAAAsB,GAAA,GATML,uBAA4D;AAUlE,MAAMM,+BAA+B,GAAG/D,eAAe,CACrDyD,uBAAuB,EACvBjD,cAAc,EACd,qBAAqB,EACrB,kBACF,CAAC;;AAED;AAAAwD,GAAA,GAPMD,+BAA+B;AAQrC,MAAME,oBAAyD,GAAIP,KAAK,IAAK;EAAAQ,GAAA;EAC3E,MAAMC,mBAAmB,GAAGjE,cAAc,CAAC,qBAAqB,CAAC;EAEjE,IAAI,CAACiE,mBAAmB,EAAE;IACxB,oBAAO/D,OAAA,CAACI,cAAc;MAAA,GAAKkD;IAAK;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EACtC;EAEA,oBAAOpC,OAAA,CAAC2D,+BAA+B;IAAA,GAAKL;EAAK;IAAArB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;AACvD,CAAC;;AAED;AAAA0B,GAAA,CAVMD,oBAAyD;EAAA,QACjC/D,cAAc;AAAA;AAAAkE,GAAA,GADtCH,oBAAyD;AAW/D,MAAMI,YAAY,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMC,OAAO,GAAG,+BAA+B;EAC/C,IAAI,CAACD,QAAQ,CAACE,cAAc,CAACD,OAAO,CAAC,EAAE;IACrC,MAAM1C,KAAK,GAAGyC,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;IAC7C5C,KAAK,CAAC6C,EAAE,GAAGH,OAAO;IAClB1C,KAAK,CAAC8C,WAAW,GAAGN,YAAY;IAChCC,QAAQ,CAACM,IAAI,CAACC,WAAW,CAAChD,KAAK,CAAC;EAClC;AACF;AAEA,eAAeoC,oBAAoB;;AAEnC;AAAA,IAAAT,EAAA,EAAAM,GAAA,EAAAE,GAAA,EAAAI,GAAA;AAAAU,YAAA,CAAAtB,EAAA;AAAAsB,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAV,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}