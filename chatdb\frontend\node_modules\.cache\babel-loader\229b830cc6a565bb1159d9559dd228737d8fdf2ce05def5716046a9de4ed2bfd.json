{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\components\\\\SafeEnhancedButton.tsx\",\n  _s = $RefreshSig$();\n/**\n * 安全的增强按钮组件 - 带有自动回退机制\n */\n\nimport React from 'react';\nimport { Button } from 'antd';\nimport { withSafeWrapper } from './SafeComponentWrapper';\nimport EnhancedButton from './EnhancedButton';\nimport { useFeatureFlag } from '../utils/featureFlags';\n\n// 扩展的按钮属性接口\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// 原始按钮组件（回退组件）\nconst FallbackButton = ({\n  variant,\n  size = 'middle',\n  tooltip,\n  loadingText,\n  ripple,\n  elevated,\n  fullWidth,\n  children,\n  className = '',\n  style = {},\n  ...props\n}) => {\n  // 将自定义属性映射到Ant Design Button属性\n  const antdProps = {\n    ...props,\n    size: size === 'medium' ? 'middle' : size === 'small' ? 'small' : 'large',\n    type: variant === 'primary' ? 'primary' : variant === 'danger' ? 'primary' : variant === 'ghost' ? 'ghost' : 'default',\n    danger: variant === 'danger',\n    className: `${className} ${fullWidth ? 'full-width-button' : ''}`,\n    style: {\n      ...style,\n      ...(fullWidth && {\n        width: '100%'\n      }),\n      ...(elevated && {\n        boxShadow: '0 2px 8px rgba(0,0,0,0.15)'\n      })\n    }\n  };\n\n  // 如果有tooltip，包装在Tooltip中\n  if (tooltip) {\n    const {\n      Tooltip\n    } = require('antd');\n    return /*#__PURE__*/_jsxDEV(Tooltip, {\n      title: tooltip,\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        ...antdProps,\n        children: props.loading && loadingText ? loadingText : children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Button, {\n    ...antdProps,\n    children: props.loading && loadingText ? loadingText : children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n};\n\n// 增强按钮组件适配器\n_c = FallbackButton;\nconst EnhancedButtonAdapter = props => {\n  try {\n    return /*#__PURE__*/_jsxDEV(EnhancedButton, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 12\n    }, this);\n  } catch (error) {\n    console.warn('EnhancedButton failed, falling back to standard button:', error);\n    return /*#__PURE__*/_jsxDEV(FallbackButton, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 12\n    }, this);\n  }\n};\n\n// 使用安全包装器包装增强按钮\n_c2 = EnhancedButtonAdapter;\nconst SafeEnhancedButtonWithWrapper = withSafeWrapper(EnhancedButtonAdapter, FallbackButton, 'useEnhancedButton', 'EnhancedButton');\n\n// 主要导出组件 - 根据功能开关选择组件\n_c3 = SafeEnhancedButtonWithWrapper;\nconst SafeEnhancedButton = props => {\n  _s();\n  const isEnhancedEnabled = useFeatureFlag('useEnhancedButton');\n  if (!isEnhancedEnabled) {\n    return /*#__PURE__*/_jsxDEV(FallbackButton, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(SafeEnhancedButtonWithWrapper, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 10\n  }, this);\n};\n\n// 添加CSS样式以支持回退按钮\n_s(SafeEnhancedButton, \"MzuwmClsQVT1lX6FVceLxwU/nAo=\", false, function () {\n  return [useFeatureFlag];\n});\n_c4 = SafeEnhancedButton;\nconst buttonStyles = `\n.full-width-button {\n  width: 100% !important;\n}\n\n.ant-btn.full-width-button {\n  display: block;\n}\n\n/* 为回退按钮添加一些基本的增强样式 */\n.ant-btn {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.ant-btn:hover:not(:disabled) {\n  transform: translateY(-1px);\n}\n\n.ant-btn-primary.ant-btn-dangerous {\n  background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);\n  border-color: #ff4d4f;\n}\n\n.ant-btn-primary.ant-btn-dangerous:hover {\n  background: linear-gradient(135deg, #ff7875 0%, #ff4d4f 100%);\n  border-color: #ff7875;\n}\n`;\n\n// 动态注入样式\nif (typeof document !== 'undefined') {\n  const styleId = 'safe-enhanced-button-styles';\n  if (!document.getElementById(styleId)) {\n    const style = document.createElement('style');\n    style.id = styleId;\n    style.textContent = buttonStyles;\n    document.head.appendChild(style);\n  }\n}\nexport default SafeEnhancedButton;\n\n// 导出类型\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"FallbackButton\");\n$RefreshReg$(_c2, \"EnhancedButtonAdapter\");\n$RefreshReg$(_c3, \"SafeEnhancedButtonWithWrapper\");\n$RefreshReg$(_c4, \"SafeEnhancedButton\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "withSafeWrapper", "Enhanced<PERSON><PERSON><PERSON>", "useFeatureFlag", "jsxDEV", "_jsxDEV", "Fallback<PERSON><PERSON>on", "variant", "size", "tooltip", "loadingText", "ripple", "elevated", "fullWidth", "children", "className", "style", "props", "antdProps", "type", "danger", "width", "boxShadow", "<PERSON><PERSON><PERSON>", "require", "title", "loading", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "EnhancedButtonAdapter", "error", "console", "warn", "_c2", "SafeEnhancedButtonWithWrapper", "_c3", "SafeEnhancedButton", "_s", "isEnhancedEnabled", "_c4", "buttonStyles", "document", "styleId", "getElementById", "createElement", "id", "textContent", "head", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/components/SafeEnhancedButton.tsx"], "sourcesContent": ["/**\n * 安全的增强按钮组件 - 带有自动回退机制\n */\n\nimport React from 'react';\nimport { Button, ButtonProps } from 'antd';\nimport { withSafeWrapper } from './SafeComponentWrapper';\nimport EnhancedButton from './EnhancedButton';\nimport { useFeatureFlag } from '../utils/featureFlags';\n\n// 扩展的按钮属性接口\ninterface SafeEnhancedButtonProps extends ButtonProps {\n  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost';\n  size?: 'small' | 'medium' | 'large';\n  tooltip?: string;\n  loading?: boolean;\n  loadingText?: string;\n  ripple?: boolean;\n  elevated?: boolean;\n  fullWidth?: boolean;\n}\n\n// 原始按钮组件（回退组件）\nconst FallbackButton: React.FC<SafeEnhancedButtonProps> = ({\n  variant,\n  size = 'middle',\n  tooltip,\n  loadingText,\n  ripple,\n  elevated,\n  fullWidth,\n  children,\n  className = '',\n  style = {},\n  ...props\n}) => {\n  // 将自定义属性映射到Ant Design Button属性\n  const antdProps: ButtonProps = {\n    ...props,\n    size: size === 'medium' ? 'middle' : size === 'small' ? 'small' : 'large',\n    type: variant === 'primary' ? 'primary' : \n          variant === 'danger' ? 'primary' : \n          variant === 'ghost' ? 'ghost' : 'default',\n    danger: variant === 'danger',\n    className: `${className} ${fullWidth ? 'full-width-button' : ''}`,\n    style: {\n      ...style,\n      ...(fullWidth && { width: '100%' }),\n      ...(elevated && { boxShadow: '0 2px 8px rgba(0,0,0,0.15)' }),\n    },\n  };\n\n  // 如果有tooltip，包装在Tooltip中\n  if (tooltip) {\n    const { Tooltip } = require('antd');\n    return (\n      <Tooltip title={tooltip}>\n        <Button {...antdProps}>\n          {props.loading && loadingText ? loadingText : children}\n        </Button>\n      </Tooltip>\n    );\n  }\n\n  return (\n    <Button {...antdProps}>\n      {props.loading && loadingText ? loadingText : children}\n    </Button>\n  );\n};\n\n// 增强按钮组件适配器\nconst EnhancedButtonAdapter: React.FC<SafeEnhancedButtonProps> = (props) => {\n  try {\n    return <EnhancedButton {...props} />;\n  } catch (error) {\n    console.warn('EnhancedButton failed, falling back to standard button:', error);\n    return <FallbackButton {...props} />;\n  }\n};\n\n// 使用安全包装器包装增强按钮\nconst SafeEnhancedButtonWithWrapper = withSafeWrapper(\n  EnhancedButtonAdapter,\n  FallbackButton,\n  'useEnhancedButton',\n  'EnhancedButton'\n);\n\n// 主要导出组件 - 根据功能开关选择组件\nconst SafeEnhancedButton: React.FC<SafeEnhancedButtonProps> = (props) => {\n  const isEnhancedEnabled = useFeatureFlag('useEnhancedButton');\n  \n  if (!isEnhancedEnabled) {\n    return <FallbackButton {...props} />;\n  }\n\n  return <SafeEnhancedButtonWithWrapper {...props} />;\n};\n\n// 添加CSS样式以支持回退按钮\nconst buttonStyles = `\n.full-width-button {\n  width: 100% !important;\n}\n\n.ant-btn.full-width-button {\n  display: block;\n}\n\n/* 为回退按钮添加一些基本的增强样式 */\n.ant-btn {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.ant-btn:hover:not(:disabled) {\n  transform: translateY(-1px);\n}\n\n.ant-btn-primary.ant-btn-dangerous {\n  background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);\n  border-color: #ff4d4f;\n}\n\n.ant-btn-primary.ant-btn-dangerous:hover {\n  background: linear-gradient(135deg, #ff7875 0%, #ff4d4f 100%);\n  border-color: #ff7875;\n}\n`;\n\n// 动态注入样式\nif (typeof document !== 'undefined') {\n  const styleId = 'safe-enhanced-button-styles';\n  if (!document.getElementById(styleId)) {\n    const style = document.createElement('style');\n    style.id = styleId;\n    style.textContent = buttonStyles;\n    document.head.appendChild(style);\n  }\n}\n\nexport default SafeEnhancedButton;\n\n// 导出类型\nexport type { SafeEnhancedButtonProps };\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAqB,MAAM;AAC1C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,cAAc,QAAQ,uBAAuB;;AAEtD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAYA;AACA,MAAMC,cAAiD,GAAGA,CAAC;EACzDC,OAAO;EACPC,IAAI,GAAG,QAAQ;EACfC,OAAO;EACPC,WAAW;EACXC,MAAM;EACNC,QAAQ;EACRC,SAAS;EACTC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACV,GAAGC;AACL,CAAC,KAAK;EACJ;EACA,MAAMC,SAAsB,GAAG;IAC7B,GAAGD,KAAK;IACRT,IAAI,EAAEA,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAGA,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO;IACzEW,IAAI,EAAEZ,OAAO,KAAK,SAAS,GAAG,SAAS,GACjCA,OAAO,KAAK,QAAQ,GAAG,SAAS,GAChCA,OAAO,KAAK,OAAO,GAAG,OAAO,GAAG,SAAS;IAC/Ca,MAAM,EAAEb,OAAO,KAAK,QAAQ;IAC5BQ,SAAS,EAAE,GAAGA,SAAS,IAAIF,SAAS,GAAG,mBAAmB,GAAG,EAAE,EAAE;IACjEG,KAAK,EAAE;MACL,GAAGA,KAAK;MACR,IAAIH,SAAS,IAAI;QAAEQ,KAAK,EAAE;MAAO,CAAC,CAAC;MACnC,IAAIT,QAAQ,IAAI;QAAEU,SAAS,EAAE;MAA6B,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,IAAIb,OAAO,EAAE;IACX,MAAM;MAAEc;IAAQ,CAAC,GAAGC,OAAO,CAAC,MAAM,CAAC;IACnC,oBACEnB,OAAA,CAACkB,OAAO;MAACE,KAAK,EAAEhB,OAAQ;MAAAK,QAAA,eACtBT,OAAA,CAACL,MAAM;QAAA,GAAKkB,SAAS;QAAAJ,QAAA,EAClBG,KAAK,CAACS,OAAO,IAAIhB,WAAW,GAAGA,WAAW,GAAGI;MAAQ;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEd;EAEA,oBACEzB,OAAA,CAACL,MAAM;IAAA,GAAKkB,SAAS;IAAAJ,QAAA,EAClBG,KAAK,CAACS,OAAO,IAAIhB,WAAW,GAAGA,WAAW,GAAGI;EAAQ;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChD,CAAC;AAEb,CAAC;;AAED;AAAAC,EAAA,GAhDMzB,cAAiD;AAiDvD,MAAM0B,qBAAwD,GAAIf,KAAK,IAAK;EAC1E,IAAI;IACF,oBAAOZ,OAAA,CAACH,cAAc;MAAA,GAAKe;IAAK;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,yDAAyD,EAAEF,KAAK,CAAC;IAC9E,oBAAO5B,OAAA,CAACC,cAAc;MAAA,GAAKW;IAAK;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EACtC;AACF,CAAC;;AAED;AAAAM,GAAA,GATMJ,qBAAwD;AAU9D,MAAMK,6BAA6B,GAAGpC,eAAe,CACnD+B,qBAAqB,EACrB1B,cAAc,EACd,mBAAmB,EACnB,gBACF,CAAC;;AAED;AAAAgC,GAAA,GAPMD,6BAA6B;AAQnC,MAAME,kBAAqD,GAAItB,KAAK,IAAK;EAAAuB,EAAA;EACvE,MAAMC,iBAAiB,GAAGtC,cAAc,CAAC,mBAAmB,CAAC;EAE7D,IAAI,CAACsC,iBAAiB,EAAE;IACtB,oBAAOpC,OAAA,CAACC,cAAc;MAAA,GAAKW;IAAK;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EACtC;EAEA,oBAAOzB,OAAA,CAACgC,6BAA6B;IAAA,GAAKpB;EAAK;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;AACrD,CAAC;;AAED;AAAAU,EAAA,CAVMD,kBAAqD;EAAA,QAC/BpC,cAAc;AAAA;AAAAuC,GAAA,GADpCH,kBAAqD;AAW3D,MAAMI,YAAY,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMC,OAAO,GAAG,6BAA6B;EAC7C,IAAI,CAACD,QAAQ,CAACE,cAAc,CAACD,OAAO,CAAC,EAAE;IACrC,MAAM7B,KAAK,GAAG4B,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;IAC7C/B,KAAK,CAACgC,EAAE,GAAGH,OAAO;IAClB7B,KAAK,CAACiC,WAAW,GAAGN,YAAY;IAChCC,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACnC,KAAK,CAAC;EAClC;AACF;AAEA,eAAeuB,kBAAkB;;AAEjC;AAAA,IAAAR,EAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAI,GAAA;AAAAU,YAAA,CAAArB,EAAA;AAAAqB,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAV,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}